1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplicationtv"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:5:5-67
11-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:5:22-64
12
13    <uses-feature
13-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:7:5-9:36
14        android:name="android.hardware.touchscreen"
14-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:8:9-52
15        android:required="false" />
15-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:9:9-33
16    <uses-feature
16-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:10:5-12:35
17        android:name="android.software.leanback"
17-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:11:9-49
18        android:required="true" />
18-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:12:9-32
19
20    <permission
20-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
21        android:name="com.example.myapplicationtv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.example.myapplicationtv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
25
26    <application
26-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:14:5-57:19
27        android:name="com.example.myapplicationtv.TVApplication"
27-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:21:9-38
28        android:allowBackup="true"
28-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:15:9-35
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
30        android:extractNativeLibs="true"
31        android:icon="@mipmap/ic_launcher"
31-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:16:9-43
32        android:label="@string/app_name"
32-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:17:9-41
33        android:supportsRtl="true"
33-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:18:9-35
34        android:theme="@style/Theme.MyApplicationTV"
34-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:19:9-53
35        android:usesCleartextTraffic="true" >
35-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:20:9-44
36        <activity
36-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:22:9-35:20
37            android:name="com.example.myapplicationtv.MainActivity"
37-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:23:13-41
38            android:banner="@drawable/app_icon_your_company"
38-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:24:13-61
39            android:exported="true"
39-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:25:13-36
40            android:icon="@drawable/app_icon_your_company"
40-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:26:13-59
41            android:label="@string/app_name"
41-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:27:13-45
42            android:logo="@drawable/app_icon_your_company"
42-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:28:13-59
43            android:screenOrientation="landscape" >
43-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:29:13-50
44            <intent-filter>
44-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:30:13-34:29
45                <action android:name="android.intent.action.MAIN" />
45-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:31:17-69
45-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:31:25-66
46
47                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
47-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:33:17-86
47-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:33:27-83
48            </intent-filter>
49        </activity>
50        <activity
50-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:36:9-38:40
51            android:name="com.example.myapplicationtv.DetailsActivity"
51-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:37:13-44
52            android:exported="false" />
52-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:38:13-37
53        <activity
53-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:39:9-41:40
54            android:name="com.example.myapplicationtv.PlaybackActivity"
54-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:40:13-45
55            android:exported="false" />
55-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:41:13-37
56        <activity
56-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:42:9-44:40
57            android:name="com.example.myapplicationtv.BrowseErrorActivity"
57-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:43:13-48
58            android:exported="false" />
58-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:44:13-37
59        <activity
59-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:45:9-47:40
60            android:name="com.example.myapplicationtv.SearchActivity"
60-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:46:13-43
61            android:exported="false" />
61-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:47:13-37
62        <activity
62-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:48:9-50:40
63            android:name="com.example.myapplicationtv.LoginActivity"
63-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:49:13-42
64            android:exported="false" />
64-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:50:13-37
65        <activity
65-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:51:9-53:40
66            android:name="com.example.myapplicationtv.UserCenterActivity"
66-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:52:13-47
67            android:exported="false" />
67-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:53:13-37
68        <activity
68-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:54:9-56:40
69            android:name="com.example.myapplicationtv.TestActivity"
69-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:55:13-41
70            android:exported="false" />
70-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:56:13-37
71
72        <provider
72-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
73            android:name="androidx.startup.InitializationProvider"
73-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:25:13-67
74            android:authorities="com.example.myapplicationtv.androidx-startup"
74-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:26:13-68
75            android:exported="false" >
75-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:27:13-37
76            <meta-data
76-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
77                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
77-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
78                android:value="androidx.startup" />
78-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
79        </provider>
80
81        <receiver
81-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
82            android:name="androidx.profileinstaller.ProfileInstallReceiver"
82-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
83            android:directBootAware="false"
83-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
84            android:enabled="true"
84-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
85            android:exported="true"
85-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
86            android:permission="android.permission.DUMP" >
86-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
88                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
88-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
88-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
91                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
91-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
91-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
92            </intent-filter>
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
94                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
94-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
94-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
97                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
97-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
97-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
98            </intent-filter>
99        </receiver>
100    </application>
101
102</manifest>
