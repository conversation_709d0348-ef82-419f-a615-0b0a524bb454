# 🚀 TV端影视应用 API 测试文档

## 📋 基础信息

**服务地址**: `http://localhost:8080/api`  
**认证方式**: JWT Token  
**内容类型**: `application/json`

## ✅ 测试状态

### 1. 服务健康检查 ✅

**接口**: `GET /test/health`  
**状态**: 正常  
**响应示例**:
```json
{
  "code": 200,
  "message": "服务运行正常",
  "data": {
    "message": "MovieTV Backend Service is running",
    "status": "UP",
    "timestamp": "2025-07-02T23:58:03.3870951"
  },
  "timestamp": 1751471883387,
  "success": true
}
```

### 2. 公开接口测试 ✅

**接口**: `GET /test/public`  
**状态**: 正常  
**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "公开接口访问成功",
  "timestamp": 1751471857285,
  "success": true
}
```

## 🔐 用户认证接口

### 用户注册

**接口**: `POST /auth/register`  
**请求体**:
```json
{
  "username": "testuser",
  "password": "123456",
  "phone": "13800138000",
  "email": "<EMAIL>"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "id": "user_id",
    "username": "testuser",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "createTime": "2025-07-02T23:58:00"
  },
  "success": true
}
```

### 用户登录

**接口**: `POST /auth/login`  
**请求体**:
```json
{
  "username": "testuser",
  "password": "123456"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": "user_id",
      "username": "testuser",
      "phone": "13800138000",
      "email": "<EMAIL>"
    }
  },
  "success": true
}
```

## 🎬 电影相关接口

### 获取电影列表

**接口**: `GET /movies/list`  
**参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 20)
- `keyword`: 搜索关键词 (可选)
- `type`: 类型筛选 (可选)
- `category`: 分类筛选 (可选)

**请求头**:
```
Authorization: Bearer <your-jwt-token>
```

### 获取电影详情

**接口**: `GET /movies/{id}`  
**请求头**:
```
Authorization: Bearer <your-jwt-token>
```

## 📂 分类接口

### 获取所有分类

**接口**: `GET /categories/all`  
**状态**: 公开接口，无需认证

### 根据类型获取分类

**接口**: `GET /categories/type/{type}`  
**状态**: 公开接口，无需认证

## ❤️ 用户收藏接口

### 添加收藏

**接口**: `POST /favorites/{movieId}`  
**请求头**:
```
Authorization: Bearer <your-jwt-token>
```

### 取消收藏

**接口**: `DELETE /favorites/{movieId}`  
**请求头**:
```
Authorization: Bearer <your-jwt-token>
```

### 获取收藏列表

**接口**: `GET /favorites/list`  
**参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 20)

**请求头**:
```
Authorization: Bearer <your-jwt-token>
```

## 📺 观看历史接口

### 保存播放历史

**接口**: `POST /history/save`  
**请求体**:
```json
{
  "movieId": "movie_id",
  "chapterId": "chapter_id",
  "progress": 1800,
  "duration": 3600
}
```

**请求头**:
```
Authorization: Bearer <your-jwt-token>
```

### 获取观看历史

**接口**: `GET /history/list`  
**参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 20)

**请求头**:
```
Authorization: Bearer <your-jwt-token>
```

## ⭐ 评分接口

### 提交评分

**接口**: `POST /ratings/submit`  
**请求体**:
```json
{
  "movieId": "movie_id",
  "rating": 8.5,
  "comment": "很好看的电影"
}
```

**请求头**:
```
Authorization: Bearer <your-jwt-token>
```

### 获取电影评分

**接口**: `GET /ratings/movie/{movieId}`  
**状态**: 公开接口，无需认证

## 🔧 测试工具

### Postman 测试集合

可以使用以下curl命令进行测试：

```bash
# 健康检查
curl -X GET http://localhost:8080/api/test/health

# 用户注册
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456",
    "phone": "13800138000",
    "email": "<EMAIL>"
  }'

# 用户登录
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456"
  }'
```

## 📊 测试进度

- [x] 服务启动
- [x] 数据库连接
- [x] 健康检查接口
- [x] 公开接口访问
- [ ] 用户注册接口
- [ ] 用户登录接口
- [ ] JWT认证流程
- [ ] 电影列表接口
- [ ] 收藏功能接口
- [ ] 历史记录接口
- [ ] 评分功能接口

## 🚨 注意事项

1. **JWT Token**: 登录后获取的token需要在请求头中携带
2. **Token格式**: `Authorization: Bearer <token>`
3. **Token过期**: 默认24小时过期，需要重新登录
4. **数据库**: 确保MySQL服务正常运行
5. **端口**: 默认8080端口，确保未被占用

---

**下一步**: 继续测试用户注册和登录接口，验证JWT认证流程
