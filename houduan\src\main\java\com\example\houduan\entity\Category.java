package com.example.houduan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 分类实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "category")
public class Category {

    @Id
    @Column(length = 32)
    private String id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类类型(MOVIE,APP,GAME,PRODUCT)
     */
    private String type;

    /**
     * 父分类ID
     */
    private String parentId;

    /**
     * 分类图标
     */
    private String icon;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态(0:禁用,1:启用)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
