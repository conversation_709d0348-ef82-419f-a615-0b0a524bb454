package com.example.houduan.service;

import com.example.houduan.dto.ProductQueryRequest;
import com.example.houduan.dto.ProductResponse;
import com.example.houduan.common.PageResult;
import com.example.houduan.entity.Product;
import com.example.houduan.mapper.ProductMapper;
import com.example.houduan.util.IdGenerator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品服务类
 */
@Service
@RequiredArgsConstructor
public class ProductService {

    private final ProductMapper productMapper;
    private final ObjectMapper objectMapper;

    /**
     * 分页查询商品列表
     */
    public PageResult<ProductResponse> getProductList(ProductQueryRequest request) {
        // 构建排序
        Sort sort = buildSort(request.getSort());
        Pageable pageable = PageRequest.of(request.getPage() - 1, request.getSize(), sort);

        Page<Product> page;
        
        // 根据条件查询
        if (StringUtils.hasText(request.getKeyword())) {
            page = productMapper.findByNameContainingAndStatus(request.getKeyword(), request.getStatus(), pageable);
        } else if (StringUtils.hasText(request.getCategory())) {
            page = productMapper.findByCategoryAndStatus(request.getCategory(), request.getStatus(), pageable);
        } else if (StringUtils.hasText(request.getBrand())) {
            page = productMapper.findByBrandAndStatus(request.getBrand(), request.getStatus(), pageable);
        } else if (request.getMinPrice() != null && request.getMaxPrice() != null) {
            page = productMapper.findByPriceBetweenAndStatus(request.getMinPrice(), request.getMaxPrice(), request.getStatus(), pageable);
        } else {
            page = productMapper.findByStatus(request.getStatus(), pageable);
        }

        List<ProductResponse> list = page.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        return PageResult.of(list, page.getTotalElements(), request.getPage(), request.getSize());
    }

    /**
     * 根据ID获取商品详情
     */
    public ProductResponse getProductById(String id) {
        Product product = productMapper.findById(id)
                .orElseThrow(() -> new RuntimeException("商品不存在"));
        return convertToResponse(product);
    }

    /**
     * 获取推荐商品
     */
    public List<ProductResponse> getRecommendedProducts(int limit) {
        List<Product> products = productMapper.findByIsRecommendedAndStatusOrderByCreateTimeDesc(1, 1);
        return products.stream()
                .limit(limit)
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 获取热销商品
     */
    public List<ProductResponse> getHotProducts(int limit) {
        List<Product> products = productMapper.findByIsHotAndStatusOrderBySalesDesc(1, 1);
        return products.stream()
                .limit(limit)
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 创建商品
     */
    public ProductResponse createProduct(Product product) {
        product.setId(IdGenerator.generateId());
        product.setCreateTime(LocalDateTime.now());
        product.setUpdateTime(LocalDateTime.now());
        
        if (product.getStatus() == null) {
            product.setStatus(1);
        }
        if (product.getIsRecommended() == null) {
            product.setIsRecommended(0);
        }
        if (product.getIsHot() == null) {
            product.setIsHot(0);
        }
        if (product.getStock() == null) {
            product.setStock(0);
        }
        if (product.getSales() == null) {
            product.setSales(0);
        }
        if (product.getRatingCount() == null) {
            product.setRatingCount(0);
        }

        Product savedProduct = productMapper.save(product);
        return convertToResponse(savedProduct);
    }

    /**
     * 更新商品
     */
    public ProductResponse updateProduct(String id, Product product) {
        Product existingProduct = productMapper.findById(id)
                .orElseThrow(() -> new RuntimeException("商品不存在"));

        // 更新字段
        if (StringUtils.hasText(product.getName())) {
            existingProduct.setName(product.getName());
        }
        if (StringUtils.hasText(product.getImage())) {
            existingProduct.setImage(product.getImage());
        }
        if (StringUtils.hasText(product.getImages())) {
            existingProduct.setImages(product.getImages());
        }
        if (StringUtils.hasText(product.getCategory())) {
            existingProduct.setCategory(product.getCategory());
        }
        if (StringUtils.hasText(product.getBrand())) {
            existingProduct.setBrand(product.getBrand());
        }
        if (product.getPrice() != null) {
            existingProduct.setPrice(product.getPrice());
        }
        if (product.getOriginalPrice() != null) {
            existingProduct.setOriginalPrice(product.getOriginalPrice());
        }
        if (product.getStock() != null) {
            existingProduct.setStock(product.getStock());
        }
        if (product.getRating() != null) {
            existingProduct.setRating(product.getRating());
        }
        if (StringUtils.hasText(product.getDescription())) {
            existingProduct.setDescription(product.getDescription());
        }
        if (StringUtils.hasText(product.getSpecifications())) {
            existingProduct.setSpecifications(product.getSpecifications());
        }
        if (product.getIsRecommended() != null) {
            existingProduct.setIsRecommended(product.getIsRecommended());
        }
        if (product.getIsHot() != null) {
            existingProduct.setIsHot(product.getIsHot());
        }
        if (product.getStatus() != null) {
            existingProduct.setStatus(product.getStatus());
        }

        existingProduct.setUpdateTime(LocalDateTime.now());
        Product savedProduct = productMapper.save(existingProduct);
        return convertToResponse(savedProduct);
    }

    /**
     * 删除商品
     */
    public void deleteProduct(String id) {
        if (!productMapper.existsById(id)) {
            throw new RuntimeException("商品不存在");
        }
        productMapper.deleteById(id);
    }

    /**
     * 构建排序
     */
    private Sort buildSort(String sortType) {
        return switch (sortType) {
            case "hot" -> Sort.by(Sort.Direction.DESC, "sales");
            case "price_asc" -> Sort.by(Sort.Direction.ASC, "price");
            case "price_desc" -> Sort.by(Sort.Direction.DESC, "price");
            case "rating" -> Sort.by(Sort.Direction.DESC, "rating");
            case "sales" -> Sort.by(Sort.Direction.DESC, "sales");
            default -> Sort.by(Sort.Direction.DESC, "createTime");
        };
    }

    /**
     * 转换为响应DTO
     */
    private ProductResponse convertToResponse(Product product) {
        ProductResponse response = new ProductResponse();
        response.setId(product.getId());
        response.setName(product.getName());
        response.setImage(product.getImage());
        response.setCategory(product.getCategory());
        response.setBrand(product.getBrand());
        response.setPrice(product.getPrice());
        response.setOriginalPrice(product.getOriginalPrice());
        response.setStock(product.getStock());
        response.setSales(product.getSales());
        response.setRating(product.getRating());
        response.setRatingCount(product.getRatingCount());
        response.setDescription(product.getDescription());
        response.setIsRecommended(product.getIsRecommended() == 1);
        response.setIsHot(product.getIsHot() == 1);
        response.setInStock(product.getStock() != null && product.getStock() > 0);
        response.setStatus(product.getStatus());
        response.setCreateTime(product.getCreateTime());
        response.setUpdateTime(product.getUpdateTime());

        // 计算折扣
        if (product.getOriginalPrice() != null && product.getPrice() != null && 
            product.getOriginalPrice().compareTo(product.getPrice()) > 0) {
            BigDecimal discount = product.getPrice()
                    .divide(product.getOriginalPrice(), 2, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(10));
            response.setDiscount(discount);
        }

        // 解析图片JSON
        if (StringUtils.hasText(product.getImages())) {
            try {
                List<String> images = objectMapper.readValue(product.getImages(), new TypeReference<List<String>>() {});
                response.setImages(images);
            } catch (Exception e) {
                // 忽略JSON解析错误
            }
        }

        // 解析规格JSON
        if (StringUtils.hasText(product.getSpecifications())) {
            try {
                Map<String, Object> specifications = objectMapper.readValue(product.getSpecifications(), new TypeReference<Map<String, Object>>() {});
                response.setSpecifications(specifications);
            } catch (Exception e) {
                // 忽略JSON解析错误
            }
        }

        return response;
    }
}
