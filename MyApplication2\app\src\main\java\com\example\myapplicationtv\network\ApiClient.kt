package com.example.myapplicationtv.network

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.GsonBuilder
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * API客户端
 */
object ApiClient {
    
    private const val BASE_URL = "http://10.0.2.2:8080/api/"
    private const val TIMEOUT = 30L
    
    private var retrofit: Retrofit? = null
    private var context: Context? = null
    
    fun init(context: Context) {
        this.context = context
    }
    
    private fun getRetrofit(): Retrofit {
        if (retrofit == null) {
            val gson = GsonBuilder()
                .setLenient()
                .create()
            
            val httpClient = OkHttpClient.Builder()
                .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(TIMEOUT, TimeUnit.SECONDS)
                .addInterceptor(createAuthInterceptor())
                .addInterceptor(createLoggingInterceptor())
                .build()
            
            retrofit = Retrofit.Builder()
                .baseUrl(BASE_URL)
                .client(httpClient)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build()
        }
        return retrofit!!
    }
    
    private fun createAuthInterceptor(): Interceptor {
        return Interceptor { chain ->
            val originalRequest = chain.request()
            val token = getAuthToken()
            
            val newRequest = if (token != null) {
                originalRequest.newBuilder()
                    .header("Authorization", "Bearer $token")
                    .build()
            } else {
                originalRequest
            }
            
            chain.proceed(newRequest)
        }
    }
    
    private fun createLoggingInterceptor(): HttpLoggingInterceptor {
        val logging = HttpLoggingInterceptor()
        logging.level = HttpLoggingInterceptor.Level.BODY
        return logging
    }
    
    private fun getAuthToken(): String? {
        return context?.let { ctx ->
            val prefs = ctx.getSharedPreferences("user_prefs", Context.MODE_PRIVATE)
            prefs.getString("token", null)
        }
    }

    fun saveAuthToken(token: String) {
        context?.let { ctx ->
            val prefs = ctx.getSharedPreferences("user_prefs", Context.MODE_PRIVATE)
            prefs.edit().putString("token", token).apply()
        }
    }

    fun clearAuthToken() {
        context?.let { ctx ->
            val prefs = ctx.getSharedPreferences("user_prefs", Context.MODE_PRIVATE)
            prefs.edit().remove("token").apply()
        }
    }
    
    fun getApiService(): ApiService {
        return getRetrofit().create(ApiService::class.java)
    }
}
