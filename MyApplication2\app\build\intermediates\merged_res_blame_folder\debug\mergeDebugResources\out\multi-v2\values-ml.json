{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-ml/values-ml.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,310,412,516,619,720,5465", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "202,305,407,511,614,715,837,5561"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,511,639,749,849,944,1089,1233,1353,1489,1655,1791,1926,2039,2148,2298,2395,2505,2618,2727,2832,2951,3076,3209,3341,3451,3568,3691,3814,3937,4060,4147,4231,4340,4475,4635", "endColumns": "106,100,95,101,127,109,99,94,144,143,119,135,165,135,134,112,108,149,96,109,112,108,104,118,124,132,131,109,116,122,122,122,122,86,83,108,134,159,92", "endOffsets": "207,308,404,506,634,744,844,939,1084,1228,1348,1484,1650,1786,1921,2034,2143,2293,2390,2500,2613,2722,2827,2946,3071,3204,3336,3446,3563,3686,3809,3932,4055,4142,4226,4335,4470,4630,4723"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,949,1050,1146,1248,1376,1486,1586,1681,1826,1970,2090,2226,2392,2528,2663,2776,2885,3035,3132,3242,3355,3464,3569,3688,3813,3946,4078,4188,4305,4428,4551,4674,4797,4884,4968,5077,5212,5372", "endColumns": "106,100,95,101,127,109,99,94,144,143,119,135,165,135,134,112,108,149,96,109,112,108,104,118,124,132,131,109,116,122,122,122,122,86,83,108,134,159,92", "endOffsets": "944,1045,1141,1243,1371,1481,1581,1676,1821,1965,2085,2221,2387,2523,2658,2771,2880,3030,3127,3237,3350,3459,3564,3683,3808,3941,4073,4183,4300,4423,4546,4669,4792,4879,4963,5072,5207,5367,5460"}}]}]}