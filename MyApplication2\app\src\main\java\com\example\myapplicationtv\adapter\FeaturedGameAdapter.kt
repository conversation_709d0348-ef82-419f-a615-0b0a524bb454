package com.example.myapplicationtv.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.R
import com.example.myapplicationtv.ContentItem

class FeaturedGameAdapter(
    private val onItemClick: (ContentItem) -> Unit
) : RecyclerView.Adapter<FeaturedGameAdapter.ViewHolder>() {

    private var games = listOf<ContentItem>()

    fun updateData(newGames: List<ContentItem>) {
        games = newGames
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_featured_game, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val game = games[position]
        holder.bind(game)
    }

    override fun getItemCount(): Int = games.size

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivGameScreenshot: ImageView = itemView.findViewById(R.id.iv_game_screenshot)
        private val tvGameTitle: TextView = itemView.findViewById(R.id.tv_game_title)
        private val tvGameDescription: TextView = itemView.findViewById(R.id.tv_game_description)

        fun bind(game: ContentItem) {
            tvGameTitle.text = game.title
            tvGameDescription.text = game.description
            
            // 设置游戏截图
            // TODO: 使用图片加载库加载真实图片
            ivGameScreenshot.setImageResource(R.drawable.default_background)
            
            itemView.setOnClickListener {
                onItemClick(game)
            }
        }
    }
}
