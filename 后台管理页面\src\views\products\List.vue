<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="搜索商品名称"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.category"
        placeholder="选择分类"
        clearable
        style="width: 150px"
        class="filter-item"
      >
        <el-option
          v-for="item in categoryOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="Plus"
        @click="handleCreate"
      >
        添加商品
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="{ row }">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="商品图片" width="100" align="center">
        <template #default="{ row }">
          <el-image
            style="width: 50px; height: 50px"
            :src="row.image"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column label="商品名称" min-width="150">
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分类" width="100" align="center">
        <template #default="{ row }">
          <span>{{ row.category }}</span>
        </template>
      </el-table-column>
      <el-table-column label="品牌" width="120" align="center">
        <template #default="{ row }">
          <span>{{ row.brand }}</span>
        </template>
      </el-table-column>
      <el-table-column label="价格" width="100" align="center">
        <template #default="{ row }">
          <span class="price">¥{{ row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column label="原价" width="100" align="center">
        <template #default="{ row }">
          <span class="original-price">¥{{ row.originalPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="库存" width="80" align="center">
        <template #default="{ row }">
          <span>{{ row.stock }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销量" width="80" align="center">
        <template #default="{ row }">
          <span>{{ row.sales }}</span>
        </template>
      </el-table-column>
      <el-table-column label="推荐" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.isRecommended ? 'success' : 'info'">
            {{ row.isRecommended ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'danger'">
            {{ row.status === 'ACTIVE' ? '上架' : '下架' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button
            v-if="row.status === 'ACTIVE'"
            size="small"
            type="warning"
            @click="handleModifyStatus(row, 'INACTIVE')"
          >
            下架
          </el-button>
          <el-button
            v-else
            size="small"
            type="success"
            @click="handleModifyStatus(row, 'ACTIVE')"
          >
            上架
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import Pagination from '@/components/Pagination/index.vue'

const router = useRouter()

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = reactive({
  page: 1,
  size: 20,
  keyword: undefined,
  category: undefined,
  sort: 'latest'
})
const categoryOptions = ref([])

const getList = async () => {
  try {
    listLoading.value = true
    // 模拟商品数据
    const mockData = {
      data: {
        list: [
          {
            id: 1,
            name: 'iPhone 15 Pro',
            image: '/images/products/iphone15.jpg',
            category: '手机',
            brand: 'Apple',
            price: 7999,
            originalPrice: 8999,
            stock: 100,
            sales: 1500,
            status: 'ACTIVE',
            createdAt: '2025-01-01'
          },
          {
            id: 2,
            name: 'MacBook Pro',
            image: '/images/products/macbook.jpg',
            category: '电脑',
            brand: 'Apple',
            price: 12999,
            originalPrice: 13999,
            stock: 50,
            sales: 800,
            status: 'ACTIVE',
            createdAt: '2025-01-02'
          }
        ],
        total: 2
      }
    }
    list.value = mockData.data.list
    total.value = mockData.data.total
  } catch (error) {
    console.error('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败')
  } finally {
    listLoading.value = false
  }
}

const getCategoryList = async () => {
  try {
    categoryOptions.value = [
      { id: 1, name: '手机' },
      { id: 2, name: '电脑' },
      { id: 3, name: '平板' },
      { id: 4, name: '配件' }
    ]
  } catch (error) {
    console.error('获取分类失败:', error)
  }
}

const handleFilter = () => {
  listQuery.page = 1
  getList()
}

const handleCreate = () => {
  router.push('/products/add')
}

const handleUpdate = (row) => {
  router.push(`/products/edit/${row.id}`)
}

const handleModifyStatus = async (row, status) => {
  try {
    ElMessage.success('操作成功')
    row.status = status
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('此操作将永久删除该商品, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    ElMessage.success('删除成功!')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

onMounted(() => {
  getList()
  getCategoryList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
.price {
  color: #f56c6c;
  font-weight: bold;
}
.original-price {
  color: #909399;
  text-decoration: line-through;
}
</style>
