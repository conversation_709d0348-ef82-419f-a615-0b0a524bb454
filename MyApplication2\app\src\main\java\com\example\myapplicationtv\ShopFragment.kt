package com.example.myapplicationtv

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.app.ActivityOptionsCompat
import androidx.leanback.app.BrowseSupportFragment
import androidx.leanback.widget.*
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.example.myapplicationtv.ContentItem
import com.example.myapplicationtv.viewmodel.ShopViewModel

/**
 * 商城页面Fragment
 */
class ShopFragment : BrowseSupportFragment() {
    
    private val TAG = "ShopFragment"
    private lateinit var viewModel: ShopViewModel
    private lateinit var rowsAdapter: ArrayObjectAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        Log.i(TAG, "onCreate")
        super.onActivityCreated(savedInstanceState)

        viewModel = ViewModelProvider(this)[ShopViewModel::class.java]
        
        setupUIElements()
        loadRows()
        setupEventListeners()
    }

    private fun setupUIElements() {
        title = "商品分类"
        // 设置头部颜色
        headersState = BrowseSupportFragment.HEADERS_ENABLED
        isHeadersTransitionOnBackEnabled = true

        // 设置品牌颜色
        brandColor = resources.getColor(R.color.fastlane_background, null)
        // 设置搜索图标
        searchAffordanceColor = resources.getColor(R.color.search_opaque, null)
    }

    private fun loadRows() {
        rowsAdapter = ArrayObjectAdapter(ListRowPresenter())
        adapter = rowsAdapter

        val cardPresenter = CardPresenter()

        // 观察商品分类数据
        viewModel.productCategories.observe(this, Observer { categories ->
            categories.forEach { category ->
                val listRowAdapter = ArrayObjectAdapter(cardPresenter)
                category.products.forEach { product ->
                    listRowAdapter.add(product)
                }
                val header = HeaderItem(category.id.toLong(), category.name)
                rowsAdapter.add(ListRow(header, listRowAdapter))
            }
        })

        // 观察热门商品数据
        viewModel.hotProducts.observe(this, Observer { products ->
            if (products.isNotEmpty()) {
                val listRowAdapter = ArrayObjectAdapter(cardPresenter)
                products.forEach { product ->
                    listRowAdapter.add(product)
                }
                val header = HeaderItem(999, "热门商品")
                rowsAdapter.add(ListRow(header, listRowAdapter))
            }
        })

        // 加载数据
        viewModel.loadShopData()
    }

    private fun setupEventListeners() {
        setOnSearchClickedListener {
            val intent = Intent(activity!!, SearchActivity::class.java)
            intent.putExtra("search_type", "product")
            startActivity(intent)
        }

        onItemViewClickedListener = ItemViewClickedListener()
        onItemViewSelectedListener = ItemViewSelectedListener()
    }

    private inner class ItemViewClickedListener : OnItemViewClickedListener {
        override fun onItemClicked(
            itemViewHolder: Presenter.ViewHolder,
            item: Any,
            rowViewHolder: RowPresenter.ViewHolder,
            row: Row
        ) {
            when (item) {
                is Movie -> {
                    Log.d(TAG, "Movie: " + item.toString())
                    val intent = Intent(activity!!, DetailsActivity::class.java)
                    intent.putExtra(DetailsActivity.MOVIE, item)

                    val bundle = ActivityOptionsCompat.makeSceneTransitionAnimation(
                        activity!!,
                        (itemViewHolder.view as ImageCardView).mainImageView,
                        DetailsActivity.SHARED_ELEMENT_NAME
                    ).toBundle()
                    startActivity(intent, bundle)
                }
                is ContentItem -> {
                    Log.d(TAG, "Product: ${item.type} - ${item.title}")
                    val intent = Intent(activity!!, DetailsActivity::class.java)
                    intent.putExtra("CONTENT_ITEM", item)
                    
                    val bundle = ActivityOptionsCompat.makeSceneTransitionAnimation(
                        activity!!,
                        (itemViewHolder.view as ImageCardView).mainImageView,
                        DetailsActivity.SHARED_ELEMENT_NAME
                    ).toBundle()
                    startActivity(intent, bundle)
                }
                is String -> {
                    Toast.makeText(activity!!, item, Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private inner class ItemViewSelectedListener : OnItemViewSelectedListener {
        override fun onItemSelected(
            itemViewHolder: Presenter.ViewHolder?, item: Any?,
            rowViewHolder: RowPresenter.ViewHolder, row: Row
        ) {
            // 处理选中事件
        }
    }
}
