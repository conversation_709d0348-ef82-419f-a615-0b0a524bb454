package com.example.myapplicationtv

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.cardview.widget.CardView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.adapter.ProductCategoryAdapter
import com.example.myapplicationtv.adapter.HotProductAdapter
import com.example.myapplicationtv.viewmodel.ShopViewModel

/**
 * 商城页面Fragment
 */
class ShopFragment : Fragment() {

    private val TAG = "ShopFragment"
    private lateinit var viewModel: ShopViewModel

    private lateinit var rvProductCategories: RecyclerView
    private lateinit var rvHotProducts: RecyclerView
    private lateinit var cvSmartHomeBanner: CardView

    private lateinit var categoryAdapter: ProductCategoryAdapter
    private lateinit var hotProductAdapter: HotProductAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_shop, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.i(TAG, "onViewCreated")

        viewModel = ViewModelProvider(this)[ShopViewModel::class.java]

        initViews(view)
        setupRecyclerViews()
        observeData()
        loadData()
    }

    private fun initViews(view: View) {
        rvProductCategories = view.findViewById(R.id.rv_product_categories)
        rvHotProducts = view.findViewById(R.id.rv_hot_products)
        cvSmartHomeBanner = view.findViewById(R.id.cv_smart_home_banner)
    }

    private fun setupRecyclerViews() {
        // 设置商品分类RecyclerView
        rvProductCategories.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        categoryAdapter = ProductCategoryAdapter { category ->
            Log.d(TAG, "Category clicked: ${category.name}")
            // 处理分类点击事件
        }
        rvProductCategories.adapter = categoryAdapter

        // 设置热门商品RecyclerView
        rvHotProducts.layoutManager = GridLayoutManager(context, 4)
        hotProductAdapter = HotProductAdapter { product ->
            Log.d(TAG, "Hot product clicked: ${product.title}")
            // 处理热门商品点击事件
        }
        rvHotProducts.adapter = hotProductAdapter

        // 设置智能家居横幅点击事件
        cvSmartHomeBanner.setOnClickListener {
            Log.d(TAG, "Smart home banner clicked")
            // 处理横幅点击事件
        }
    }

    private fun observeData() {
        // 观察商品分类数据
        viewModel.productCategories.observe(viewLifecycleOwner, Observer { categories ->
            categoryAdapter.updateData(categories)
        })

        // 观察热门商品数据
        viewModel.hotProducts.observe(viewLifecycleOwner, Observer { products ->
            hotProductAdapter.updateData(products)
        })
    }

    private fun loadData() {
        viewModel.loadShopData()
    }

}
