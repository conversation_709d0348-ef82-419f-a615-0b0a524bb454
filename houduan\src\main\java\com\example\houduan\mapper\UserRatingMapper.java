package com.example.houduan.mapper;

import com.example.houduan.entity.UserRating;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户评分Repository接口
 */
@Repository
public interface UserRatingMapper extends JpaRepository<UserRating, String> {

    Optional<UserRating> findByUserIdAndMovieId(String userId, String movieId);
    List<UserRating> findByMovieIdOrderByCreateTimeDesc(String movieId);
}
