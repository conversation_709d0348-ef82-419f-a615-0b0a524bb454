{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-29:/values-el/values-el.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,406,509,617,723,5373", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "198,301,401,504,612,718,835,5469"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,504,633,745,844,935,1065,1192,1301,1426,1574,1706,1835,1950,2041,2189,2285,2390,2497,2612,2708,2821,2939,3070,3198,3306,3422,3551,3685,3812,3944,4031,4118,4234,4372,4542", "endColumns": "106,100,97,92,128,111,98,90,129,126,108,124,147,131,128,114,90,147,95,104,106,114,95,112,117,130,127,107,115,128,133,126,131,86,86,115,137,169,95", "endOffsets": "207,308,406,499,628,740,839,930,1060,1187,1296,1421,1569,1701,1830,1945,2036,2184,2280,2385,2492,2607,2703,2816,2934,3065,3193,3301,3417,3546,3680,3807,3939,4026,4113,4229,4367,4537,4633"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,947,1048,1146,1239,1368,1480,1579,1670,1800,1927,2036,2161,2309,2441,2570,2685,2776,2924,3020,3125,3232,3347,3443,3556,3674,3805,3933,4041,4157,4286,4420,4547,4679,4766,4853,4969,5107,5277", "endColumns": "106,100,97,92,128,111,98,90,129,126,108,124,147,131,128,114,90,147,95,104,106,114,95,112,117,130,127,107,115,128,133,126,131,86,86,115,137,169,95", "endOffsets": "942,1043,1141,1234,1363,1475,1574,1665,1795,1922,2031,2156,2304,2436,2565,2680,2771,2919,3015,3120,3227,3342,3438,3551,3669,3800,3928,4036,4152,4281,4415,4542,4674,4761,4848,4964,5102,5272,5368"}}]}]}