package com.example.myapplicationtv.base

import android.view.KeyEvent
import androidx.fragment.app.FragmentActivity
import com.example.myapplicationtv.utils.FocusHelper

/**
 * TV端Activity基类
 */
abstract class BaseTVActivity : FragmentActivity() {
    
    override fun onKeyDown(keyCode: Int, event: KeyEvent): Bo<PERSON>an {
        // 首先尝试用FocusHelper处理
        if (FocusHelper.handleKeyEvent(this, keyCode, event)) {
            return true
        }
        
        // 处理特定的TV遥控器按键
        return when (keyCode) {
            KeyEvent.KEYCODE_DPAD_CENTER,
            KeyEvent.KEYCODE_ENTER -> {
                // 确认键
                handleEnterKey()
            }
            KeyEvent.KEYCODE_BACK -> {
                // 返回键
                handleBackKey()
            }
            KeyEvent.KEYCODE_MENU -> {
                // 菜单键
                handleMenuKey()
            }
            else -> super.onKeyDown(keyCode, event)
        }
    }
    
    /**
     * 处理确认键
     */
    protected open fun handleEnterKey(): <PERSON><PERSON><PERSON> {
        // 子类可以重写此方法
        return false
    }
    
    /**
     * 处理返回键
     */
    protected open fun handleBackKey(): Boolean {
        // 默认行为：结束Activity
        finish()
        return true
    }
    
    /**
     * 处理菜单键
     */
    protected open fun handleMenuKey(): Boolean {
        // 子类可以重写此方法
        return false
    }
    
    /**
     * 设置默认焦点
     */
    protected open fun setDefaultFocus() {
        // 子类可以重写此方法来设置默认焦点
    }
    
    override fun onResume() {
        super.onResume()
        // 恢复时设置默认焦点
        setDefaultFocus()
    }
}
