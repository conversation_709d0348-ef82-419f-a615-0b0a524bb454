<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="320dp"
    android:layout_height="180dp"
    android:layout_marginEnd="16dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="8dp"
    app:cardBackgroundColor="@color/card_background"
    android:focusable="true"
    android:clickable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 游戏截图背景 -->
        <ImageView
            android:id="@+id/iv_game_screenshot"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/default_background" />

        <!-- 渐变遮罩 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/gradient_overlay" />

        <!-- 游戏信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:id="@+id/tv_game_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="王国战争"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tv_game_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="策略战争游戏，征服世界"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:alpha="0.8" />

        </LinearLayout>

    </RelativeLayout>

</androidx.cardview.widget.CardView>
