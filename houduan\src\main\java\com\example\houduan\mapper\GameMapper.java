package com.example.houduan.mapper;

import com.example.houduan.entity.Game;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 游戏数据访问层
 */
@Repository
public interface GameMapper extends JpaRepository<Game, String> {

    /**
     * 根据分类查询游戏列表
     */
    Page<Game> findByCategoryAndStatus(String category, Integer status, Pageable pageable);

    /**
     * 根据状态查询游戏列表
     */
    Page<Game> findByStatus(Integer status, Pageable pageable);

    /**
     * 根据名称模糊查询游戏
     */
    @Query("SELECT g FROM Game g WHERE g.name LIKE %:keyword% AND g.status = :status")
    Page<Game> findByNameContainingAndStatus(@Param("keyword") String keyword, @Param("status") Integer status, Pageable pageable);

    /**
     * 查询推荐游戏
     */
    List<Game> findByIsRecommendedAndStatusOrderByCreateTimeDesc(Integer isRecommended, Integer status);

    /**
     * 查询精选游戏
     */
    List<Game> findByIsFeaturedAndStatusOrderByCreateTimeDesc(Integer isFeatured, Integer status);

    /**
     * 根据分类查询游戏数量
     */
    long countByCategoryAndStatus(String category, Integer status);

    /**
     * 查询热门游戏(按游玩次数排序)
     */
    @Query("SELECT g FROM Game g WHERE g.status = :status ORDER BY g.playCount DESC")
    List<Game> findHotGames(@Param("status") Integer status, Pageable pageable);

    /**
     * 查询最新游戏
     */
    List<Game> findByStatusOrderByCreateTimeDesc(Integer status, Pageable pageable);

    /**
     * 根据评分查询游戏
     */
    @Query("SELECT g FROM Game g WHERE g.rating >= :minRating AND g.status = :status ORDER BY g.rating DESC")
    List<Game> findByRatingGreaterThanEqualAndStatus(@Param("minRating") Double minRating, @Param("status") Integer status, Pageable pageable);

    /**
     * 查询推荐和精选游戏
     */
    @Query("SELECT g FROM Game g WHERE (g.isRecommended = 1 OR g.isFeatured = 1) AND g.status = :status ORDER BY g.createTime DESC")
    List<Game> findRecommendedAndFeaturedGames(@Param("status") Integer status, Pageable pageable);
}
