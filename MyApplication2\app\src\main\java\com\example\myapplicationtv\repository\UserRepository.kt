package com.example.myapplicationtv.repository

import com.example.myapplicationtv.model.*
import com.example.myapplicationtv.network.ApiClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 用户数据仓库
 */
class UserRepository {
    
    private val apiService = ApiClient.getApiService()
    
    /**
     * 用户登录
     */
    suspend fun login(username: String, password: String): Result<UserResponse> = withContext(Dispatchers.IO) {
        try {
            val request = LoginRequest(username, password)
            val response = apiService.login(request)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "登录失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 用户注册
     */
    suspend fun register(username: String, password: String): Result<UserResponse> = withContext(Dispatchers.IO) {
        try {
            val request = RegisterRequest(username, password)
            val response = apiService.register(request)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "注册失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取用户信息
     */
    suspend fun getUserInfo(): Result<UserResponse> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getUserInfo()
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取用户信息失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 添加收藏
     */
    suspend fun addFavorite(contentId: String, contentType: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val request = FavoriteRequest(contentId, contentType)
            val response = apiService.addFavorite(request)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(Unit)
            } else {
                Result.failure(Exception(response.body()?.message ?: "添加收藏失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 取消收藏
     */
    suspend fun removeFavorite(contentId: String, contentType: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.removeFavorite(contentId, contentType)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(Unit)
            } else {
                Result.failure(Exception(response.body()?.message ?: "取消收藏失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取收藏列表
     */
    suspend fun getFavorites(
        page: Int = 1,
        size: Int = 20,
        contentType: String? = null
    ): Result<PageResult<FavoriteResponse>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getFavorites(page, size, contentType)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取收藏列表失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取观看历史
     */
    suspend fun getHistory(
        page: Int = 1,
        size: Int = 20,
        contentType: String? = null
    ): Result<PageResult<HistoryResponse>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getHistory(page, size, contentType)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取观看历史失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 添加观看历史
     */
    suspend fun addHistory(contentId: String, contentType: String, watchTime: Int? = null): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val request = HistoryRequest(contentId, contentType, watchTime)
            val response = apiService.addHistory(request)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(Unit)
            } else {
                Result.failure(Exception(response.body()?.message ?: "添加观看历史失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
