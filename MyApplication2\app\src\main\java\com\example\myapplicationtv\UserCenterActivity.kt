package com.example.myapplicationtv

import android.content.Intent
import android.os.Bundle
import com.example.myapplicationtv.base.BaseTVActivity
import com.example.myapplicationtv.utils.UserManager

/**
 * 用户中心Activity
 */
class UserCenterActivity : BaseTVActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_user_center)
        
        // 检查登录状态
        if (!UserManager.isLoggedIn()) {
            // 未登录，跳转到登录页面
            val intent = Intent(this, LoginActivity::class.java)
            startActivity(intent)
            finish()
            return
        }
        
        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .replace(R.id.user_center_fragment, UserCenterFragment())
                .commitNow()
        }
    }
}
