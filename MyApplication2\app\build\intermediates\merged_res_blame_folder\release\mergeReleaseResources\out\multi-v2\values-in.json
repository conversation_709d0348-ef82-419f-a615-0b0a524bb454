{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-in/values-in.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,507,627,737,839,929,1065,1197,1294,1407,1554,1682,1806,1915,2005,2133,2223,2326,2431,2530,2623,2733,2844,2956,3064,3174,3288,3409,3532,3645,3760,3847,3933,4042,4179,4339", "endColumns": "106,100,98,94,119,109,101,89,135,131,96,112,146,127,123,108,89,127,89,102,104,98,92,109,110,111,107,109,113,120,122,112,114,86,85,108,136,159,96", "endOffsets": "207,308,407,502,622,732,834,924,1060,1192,1289,1402,1549,1677,1801,1910,2000,2128,2218,2321,2426,2525,2618,2728,2839,2951,3059,3169,3283,3404,3527,3640,3755,3842,3928,4037,4174,4334,4431"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "835,942,1043,1142,1237,1357,1467,1569,1659,1795,1927,2024,2137,2284,2412,2536,2645,2735,2863,2953,3056,3161,3260,3353,3463,3574,3686,3794,3904,4018,4139,4262,4375,4490,4577,4663,4772,4909,5069", "endColumns": "106,100,98,94,119,109,101,89,135,131,96,112,146,127,123,108,89,127,89,102,104,98,92,109,110,111,107,109,113,120,122,112,114,86,85,108,136,159,96", "endOffsets": "937,1038,1137,1232,1352,1462,1564,1654,1790,1922,2019,2132,2279,2407,2531,2640,2730,2858,2948,3051,3156,3255,3348,3458,3569,3681,3789,3899,4013,4134,4257,4370,4485,4572,4658,4767,4904,5064,5161"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,399,496,602,720,5166", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "195,297,394,491,597,715,830,5262"}}]}]}