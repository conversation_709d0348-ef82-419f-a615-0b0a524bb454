package com.example.houduan.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 游戏响应DTO
 */
@Data
public class GameResponse {

    /**
     * 游戏ID
     */
    private String id;

    /**
     * 游戏名称
     */
    private String name;

    /**
     * 游戏封面URL
     */
    private String cover;

    /**
     * 游戏图标URL
     */
    private String icon;

    /**
     * 游戏分类
     */
    private String category;

    /**
     * 版本号
     */
    private String version;

    /**
     * 游戏大小(字节)
     */
    private Long size;

    /**
     * 格式化的游戏大小
     */
    private String sizeFormatted;

    /**
     * 评分(0.0-10.0)
     */
    private BigDecimal rating;

    /**
     * 评分人数
     */
    private Integer ratingCount;

    /**
     * 游玩次数
     */
    private Long playCount;

    /**
     * 格式化的游玩次数
     */
    private String playCountFormatted;

    /**
     * 游戏描述
     */
    private String description;

    /**
     * 游戏地址
     */
    private String playUrl;

    /**
     * 截图URLs
     */
    private List<String> screenshots;

    /**
     * 是否推荐
     */
    private Boolean isRecommended;

    /**
     * 是否精选
     */
    private Boolean isFeatured;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
