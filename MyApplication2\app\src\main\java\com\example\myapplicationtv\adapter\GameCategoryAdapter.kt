package com.example.myapplicationtv.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.R
import com.example.myapplicationtv.model.GameCategory

class GameCategoryAdapter(
    private val onItemClick: (GameCategory) -> Unit
) : RecyclerView.Adapter<GameCategoryAdapter.ViewHolder>() {

    private var categories = listOf<GameCategory>()

    fun updateData(newCategories: List<GameCategory>) {
        categories = newCategories
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_game_category, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val category = categories[position]
        holder.bind(category)
    }

    override fun getItemCount(): Int = categories.size

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivCategoryIcon: ImageView = itemView.findViewById(R.id.iv_category_icon)
        private val tvCategoryName: TextView = itemView.findViewById(R.id.tv_category_name)

        fun bind(category: GameCategory) {
            tvCategoryName.text = category.name
            // 设置分类图标
            ivCategoryIcon.setImageResource(getCategoryIcon(category.name))
            
            itemView.setOnClickListener {
                onItemClick(category)
            }
        }

        private fun getCategoryIcon(categoryName: String): Int {
            return when (categoryName) {
                "休闲游戏" -> R.drawable.ic_games
                "益智游戏" -> R.drawable.ic_games
                "儿童游戏" -> R.drawable.ic_games
                "策略游戏" -> R.drawable.ic_games
                "竞技游戏" -> R.drawable.ic_games
                "动作游戏" -> R.drawable.ic_games
                else -> R.drawable.ic_games
            }
        }
    }
}
