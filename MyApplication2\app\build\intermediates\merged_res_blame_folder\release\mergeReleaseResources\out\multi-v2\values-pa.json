{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-pa/values-pa.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,624,735,831,927,1052,1177,1288,1415,1548,1676,1804,1909,1999,2133,2222,2322,2432,2537,2633,2746,2853,2968,3083,3186,3294,3414,3534,3645,3756,3843,3925,4022,4155,4303", "endColumns": "106,100,98,95,115,110,95,95,124,124,110,126,132,127,127,104,89,133,88,99,109,104,95,112,106,114,114,102,107,119,119,110,110,86,81,96,132,147,86", "endOffsets": "207,308,407,503,619,730,826,922,1047,1172,1283,1410,1543,1671,1799,1904,1994,2128,2217,2317,2427,2532,2628,2741,2848,2963,3078,3181,3289,3409,3529,3640,3751,3838,3920,4017,4150,4298,4385"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "838,945,1046,1145,1241,1357,1468,1564,1660,1785,1910,2021,2148,2281,2409,2537,2642,2732,2866,2955,3055,3165,3270,3366,3479,3586,3701,3816,3919,4027,4147,4267,4378,4489,4576,4658,4755,4888,5036", "endColumns": "106,100,98,95,115,110,95,95,124,124,110,126,132,127,127,104,89,133,88,99,109,104,95,112,106,114,114,102,107,119,119,110,110,86,81,96,132,147,86", "endOffsets": "940,1041,1140,1236,1352,1463,1559,1655,1780,1905,2016,2143,2276,2404,2532,2637,2727,2861,2950,3050,3160,3265,3361,3474,3581,3696,3811,3914,4022,4142,4262,4373,4484,4571,4653,4750,4883,5031,5118"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,408,509,611,709,5123", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "198,300,403,504,606,704,833,5219"}}]}]}