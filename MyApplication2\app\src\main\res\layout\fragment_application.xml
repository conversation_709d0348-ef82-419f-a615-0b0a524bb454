<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/default_background"
    android:padding="24dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 应用分类标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="应用分类"
            android:textColor="@android:color/white"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <!-- 应用分类水平滚动列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_app_categories"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:clipToPadding="false"
            android:paddingStart="8dp"
            android:paddingEnd="8dp" />

        <!-- 应用商店横幅 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cv_app_store_banner"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginBottom="32dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="8dp"
            android:focusable="true"
            android:clickable="true"
            android:foreground="@drawable/banner_button_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/app_store_gradient"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="24dp">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:src="@drawable/ic_app_store"
                    android:layout_marginEnd="24dp"
                    android:contentDescription="应用商店" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="应用商店"
                        android:textColor="@android:color/white"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="发现更多精彩应用"
                        android:textColor="@android:color/white"
                        android:textSize="14sp"
                        android:alpha="0.8"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

                <Button
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="进入"
                    android:textColor="@android:color/white"
                    android:background="@drawable/banner_button_background"
                    android:paddingHorizontal="24dp"
                    android:paddingVertical="8dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 推荐应用标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="推荐应用"
            android:textColor="@android:color/white"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <!-- 推荐应用网格 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_recommended_apps"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false" />

    </LinearLayout>

</ScrollView>
