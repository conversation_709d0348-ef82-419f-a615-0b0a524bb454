package com.example.houduan.service;

import com.example.houduan.dto.AppQueryRequest;
import com.example.houduan.dto.AppResponse;
import com.example.houduan.common.PageResult;
import com.example.houduan.entity.App;
import com.example.houduan.mapper.AppMapper;
import com.example.houduan.util.IdGenerator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 应用服务类
 */
@Service
@RequiredArgsConstructor
public class AppService {

    private final AppMapper appMapper;
    private final ObjectMapper objectMapper;

    /**
     * 分页查询应用列表
     */
    public PageResult<AppResponse> getAppList(AppQueryRequest request) {
        // 构建排序
        Sort sort = buildSort(request.getSort());
        Pageable pageable = PageRequest.of(request.getPage() - 1, request.getSize(), sort);

        Page<App> page;
        
        // 根据条件查询
        if (StringUtils.hasText(request.getKeyword())) {
            page = appMapper.findByNameContainingAndStatus(request.getKeyword(), request.getStatus(), pageable);
        } else if (StringUtils.hasText(request.getCategory())) {
            page = appMapper.findByCategoryAndStatus(request.getCategory(), request.getStatus(), pageable);
        } else {
            page = appMapper.findByStatus(request.getStatus(), pageable);
        }

        List<AppResponse> list = page.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        return PageResult.of(list, page.getTotalElements(), request.getPage(), request.getSize());
    }

    /**
     * 根据ID获取应用详情
     */
    public AppResponse getAppById(String id) {
        App app = appMapper.findById(id)
                .orElseThrow(() -> new RuntimeException("应用不存在"));
        return convertToResponse(app);
    }

    /**
     * 获取推荐应用
     */
    public List<AppResponse> getRecommendedApps(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<App> apps = appMapper.findByIsRecommendedAndStatusOrderByCreateTimeDesc(1, 1);
        return apps.stream()
                .limit(limit)
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 获取热门应用
     */
    public List<AppResponse> getHotApps(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<App> apps = appMapper.findHotApps(1, pageable);
        return apps.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 创建应用
     */
    public AppResponse createApp(App app) {
        app.setId(IdGenerator.generateId());
        app.setCreateTime(LocalDateTime.now());
        app.setUpdateTime(LocalDateTime.now());
        
        if (app.getStatus() == null) {
            app.setStatus(1);
        }
        if (app.getIsRecommended() == null) {
            app.setIsRecommended(0);
        }
        if (app.getDownloadCount() == null) {
            app.setDownloadCount(0L);
        }
        if (app.getRatingCount() == null) {
            app.setRatingCount(0);
        }

        App savedApp = appMapper.save(app);
        return convertToResponse(savedApp);
    }

    /**
     * 更新应用
     */
    public AppResponse updateApp(String id, App app) {
        App existingApp = appMapper.findById(id)
                .orElseThrow(() -> new RuntimeException("应用不存在"));

        // 更新字段
        if (StringUtils.hasText(app.getName())) {
            existingApp.setName(app.getName());
        }
        if (StringUtils.hasText(app.getIcon())) {
            existingApp.setIcon(app.getIcon());
        }
        if (StringUtils.hasText(app.getCategory())) {
            existingApp.setCategory(app.getCategory());
        }
        if (StringUtils.hasText(app.getVersion())) {
            existingApp.setVersion(app.getVersion());
        }
        if (app.getSize() != null) {
            existingApp.setSize(app.getSize());
        }
        if (app.getRating() != null) {
            existingApp.setRating(app.getRating());
        }
        if (StringUtils.hasText(app.getDescription())) {
            existingApp.setDescription(app.getDescription());
        }
        if (StringUtils.hasText(app.getDownloadUrl())) {
            existingApp.setDownloadUrl(app.getDownloadUrl());
        }
        if (StringUtils.hasText(app.getScreenshots())) {
            existingApp.setScreenshots(app.getScreenshots());
        }
        if (app.getIsRecommended() != null) {
            existingApp.setIsRecommended(app.getIsRecommended());
        }
        if (app.getStatus() != null) {
            existingApp.setStatus(app.getStatus());
        }

        existingApp.setUpdateTime(LocalDateTime.now());
        App savedApp = appMapper.save(existingApp);
        return convertToResponse(savedApp);
    }

    /**
     * 删除应用
     */
    public void deleteApp(String id) {
        if (!appMapper.existsById(id)) {
            throw new RuntimeException("应用不存在");
        }
        appMapper.deleteById(id);
    }

    /**
     * 构建排序
     */
    private Sort buildSort(String sortType) {
        return switch (sortType) {
            case "hot" -> Sort.by(Sort.Direction.DESC, "downloadCount");
            case "rating" -> Sort.by(Sort.Direction.DESC, "rating");
            case "download" -> Sort.by(Sort.Direction.DESC, "downloadCount");
            default -> Sort.by(Sort.Direction.DESC, "createTime");
        };
    }

    /**
     * 转换为响应DTO
     */
    private AppResponse convertToResponse(App app) {
        AppResponse response = new AppResponse();
        response.setId(app.getId());
        response.setName(app.getName());
        response.setIcon(app.getIcon());
        response.setCategory(app.getCategory());
        response.setVersion(app.getVersion());
        response.setSize(app.getSize());
        response.setSizeFormatted(formatSize(app.getSize()));
        response.setRating(app.getRating());
        response.setRatingCount(app.getRatingCount());
        response.setDownloadCount(app.getDownloadCount());
        response.setDownloadCountFormatted(formatNumber(app.getDownloadCount()));
        response.setDescription(app.getDescription());
        response.setDownloadUrl(app.getDownloadUrl());
        response.setIsRecommended(app.getIsRecommended() == 1);
        response.setStatus(app.getStatus());
        response.setCreateTime(app.getCreateTime());
        response.setUpdateTime(app.getUpdateTime());

        // 解析截图JSON
        if (StringUtils.hasText(app.getScreenshots())) {
            try {
                List<String> screenshots = objectMapper.readValue(app.getScreenshots(), new TypeReference<List<String>>() {});
                response.setScreenshots(screenshots);
            } catch (Exception e) {
                // 忽略JSON解析错误
            }
        }

        return response;
    }

    /**
     * 格式化文件大小
     */
    private String formatSize(Long size) {
        if (size == null || size == 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        double fileSize = size.doubleValue();
        
        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", fileSize, units[unitIndex]);
    }

    /**
     * 格式化数字
     */
    private String formatNumber(Long number) {
        if (number == null || number == 0) {
            return "0";
        }
        
        if (number >= 10000) {
            return String.format("%.1f万", number / 10000.0);
        }
        
        return number.toString();
    }
}
