import request from '@/utils/request'

// 获取商品列表
export function getProducts(params) {
  return request({
    url: '/products',
    method: 'get',
    params
  })
}

// 获取单个商品
export function getProduct(id) {
  return request({
    url: `/api/products/${id}`,
    method: 'get'
  })
}

// 创建商品
export function createProduct(data) {
  return request({
    url: '/products',
    method: 'post',
    data
  })
}

// 更新商品
export function updateProduct(data) {
  return request({
    url: `/products/${data.id}`,
    method: 'put',
    data
  })
}

// 删除商品
export function deleteProduct(id) {
  return request({
    url: `/products/${id}`,
    method: 'delete'
  })
}

// 更新商品状态
export function updateProductStatus(id, status) {
  return request({
    url: `/products/${id}/status`,
    method: 'patch',
    data: { status }
  })
}

// 批量操作商品
export function batchUpdateProducts(data) {
  return request({
    url: '/products/batch',
    method: 'patch',
    data
  })
}

// 获取商品统计
export function getProductStatistics() {
  return request({
    url: '/products/statistics',
    method: 'get'
  })
}

// 获取推荐商品
export function getRecommendedProducts(params) {
  return request({
    url: '/products/recommended',
    method: 'get',
    params
  })
}
