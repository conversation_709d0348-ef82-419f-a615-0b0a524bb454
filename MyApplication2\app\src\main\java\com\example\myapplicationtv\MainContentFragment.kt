package com.example.myapplicationtv

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.adapter.AppCategoryAdapter
import com.example.myapplicationtv.adapter.FeaturedMovieAdapter
import com.example.myapplicationtv.model.AppCategory
import com.example.myapplicationtv.viewmodel.MainViewModel

/**
 * 主内容Fragment - 显示电影海报和应用分类
 */
class MainContentFragment : Fragment() {

    private lateinit var rvFeaturedMovies: RecyclerView
    private lateinit var rvAppCategories: RecyclerView
    private lateinit var viewModel: MainViewModel
    private lateinit var movieAdapter: FeaturedMovieAdapter
    private lateinit var categoryAdapter: AppCategoryAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_main_content, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        initViewModel()
        setupRecyclerViews()
        observeData()
    }

    private fun initViews(view: View) {
        rvFeaturedMovies = view.findViewById(R.id.rv_featured_movies)
        rvAppCategories = view.findViewById(R.id.rv_app_categories)
    }

    private fun initViewModel() {
        viewModel = ViewModelProvider(this)[MainViewModel::class.java]
    }

    private fun setupRecyclerViews() {
        // 设置电影海报RecyclerView
        movieAdapter = FeaturedMovieAdapter { movie ->
            // 处理电影点击
        }
        rvFeaturedMovies.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        rvFeaturedMovies.adapter = movieAdapter

        // 设置应用分类RecyclerView
        val categories = createAppCategories()
        categoryAdapter = AppCategoryAdapter(categories) { category ->
            // 处理分类点击
        }
        rvAppCategories.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        rvAppCategories.adapter = categoryAdapter
    }

    private fun observeData() {
        // 观察推荐电影数据
        viewModel.recommendedMovies.observe(viewLifecycleOwner) { movies ->
            // 转换ContentItem到MovieResponse
            val movieResponses = movies.map { contentItem ->
                com.example.myapplicationtv.model.MovieResponse(
                    id = contentItem.id,
                    title = contentItem.title ?: "未知标题",
                    originalTitle = null,
                    type = "MOVIE",
                    category = "电影",
                    area = null,
                    language = null,
                    year = null,
                    duration = null,
                    director = null,
                    actors = null,
                    cover = null,
                    poster = null,
                    playUrl = null,
                    trailerUrl = null,
                    description = contentItem.description,
                    rating = contentItem.rating?.let { java.math.BigDecimal.valueOf(it.toDouble()) },
                    ratingCount = null,
                    viewCount = null,
                    isRecommended = true,
                    isHot = false,
                    isNew = false,
                    status = 1,
                    createTime = null,
                    updateTime = null
                )
            }
            movieAdapter.updateMovies(movieResponses)
        }

        // 加载数据
        viewModel.loadAllContent()
    }

    private fun createAppCategories(): List<AppCategory> {
        return listOf(
            AppCategory("视频库", R.drawable.ic_video_library, "#3b82f6"),
            AppCategory("电视节目", R.drawable.ic_tv_shows, "#8b5cf6"),
            AppCategory("体育健身", R.drawable.ic_fitness, "#f59e0b"),
            AppCategory("健身应用", R.drawable.ic_fitness, "#10b981"),
            AppCategory("音乐", R.drawable.ic_music, "#f59e0b"),
            AppCategory("汽车应用", R.drawable.ic_car, "#06b6d4")
        )
    }
}
