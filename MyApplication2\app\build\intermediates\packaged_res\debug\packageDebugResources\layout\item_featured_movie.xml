<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="280dp"
    android:layout_height="420dp"
    android:layout_margin="12dp"
    android:orientation="vertical"
    android:focusable="true"
    android:clickable="true"
    android:background="@drawable/movie_card_background"
    android:elevation="8dp">

    <ImageView
        android:id="@+id/iv_movie_poster"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:scaleType="centerCrop"
        android:background="@color/default_background" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:padding="16dp">

            <TextView
                android:id="@+id/tv_movie_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_movie_rating"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/accent_yellow"
                    android:textSize="14sp"
                    android:text="★★★★★" />

                <TextView
                    android:id="@+id/tv_movie_score"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@android:color/white"
                    android:textSize="12sp"
                    android:layout_marginStart="8dp"
                    android:text="9.0" />

            </LinearLayout>

    </LinearLayout>

    <!-- 焦点指示器 -->
    <View
        android:id="@+id/focus_indicator"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/movie_card_focus_overlay"
        android:visibility="gone" />

</LinearLayout>
