package com.example.myapplicationtv

import java.util.Collections
import java.util.Timer
import java.util.TimerTask

import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.leanback.app.BackgroundManager
import androidx.leanback.app.BrowseSupportFragment
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.HeaderItem
import androidx.leanback.widget.ImageCardView
import androidx.leanback.widget.ListRow
import androidx.leanback.widget.ListRowPresenter
import androidx.leanback.widget.OnItemViewClickedListener
import androidx.leanback.widget.OnItemViewSelectedListener
import androidx.leanback.widget.Presenter
import androidx.leanback.widget.Row
import androidx.leanback.widget.RowPresenter
import androidx.core.app.ActivityOptionsCompat
import androidx.core.content.ContextCompat
import android.util.DisplayMetrics
import android.util.Log
import android.view.Gravity
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.Observer

import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.example.myapplicationtv.viewmodel.MainViewModel

/**
 * Loads a grid of cards with movies to browse.
 */
class MainFragment : BrowseSupportFragment() {

    private val mHandler = Handler(Looper.myLooper()!!)
    private lateinit var mBackgroundManager: BackgroundManager
    private var mDefaultBackground: Drawable? = null
    private lateinit var mMetrics: DisplayMetrics
    private var mBackgroundTimer: Timer? = null
    private var mBackgroundUri: String? = null

    // ViewModel
    private lateinit var viewModel: MainViewModel
    private lateinit var rowsAdapter: ArrayObjectAdapter

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        Log.i(TAG, "onCreate")
        super.onActivityCreated(savedInstanceState)

        prepareBackgroundManager()
        setupUIElements()
        initViewModel()
        setupEventListeners()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy: " + mBackgroundTimer?.toString())
        mBackgroundTimer?.cancel()
    }

    private fun prepareBackgroundManager() {

        mBackgroundManager = BackgroundManager.getInstance(activity)
        mBackgroundManager.attach(activity!!.window)
        mDefaultBackground = ContextCompat.getDrawable(activity!!, R.drawable.default_background)
        mMetrics = DisplayMetrics()
        activity!!.windowManager.defaultDisplay.getMetrics(mMetrics)
    }

    private fun setupUIElements() {
        title = getString(R.string.browse_title)
        // over title
        headersState = BrowseSupportFragment.HEADERS_ENABLED
        isHeadersTransitionOnBackEnabled = true

        // set fastLane (or headers) background color
        brandColor = ContextCompat.getColor(activity!!, R.color.fastlane_background)
        // set search icon color
        searchAffordanceColor = ContextCompat.getColor(activity!!, R.color.search_opaque)
    }

    private fun initViewModel() {
        viewModel = ViewModelProvider(this)[MainViewModel::class.java]

        // 初始化适配器
        rowsAdapter = ArrayObjectAdapter(ListRowPresenter())
        adapter = rowsAdapter

        // 观察数据变化
        observeViewModel()
    }

    private fun observeViewModel() {
        val cardPresenter = CardPresenter()

        // 观察推荐电影
        viewModel.recommendedMovies.observe(this, Observer { movies ->
            if (movies.isNotEmpty()) {
                val listRowAdapter = ArrayObjectAdapter(cardPresenter)
                movies.forEach { movie ->
                    listRowAdapter.add(movie)
                }
                val header = HeaderItem(0, "推荐电影")
                updateOrAddRow(ListRow(header, listRowAdapter), 0)
            }
        })

        // 观察热门电影
        viewModel.hotMovies.observe(this, Observer { movies ->
            if (movies.isNotEmpty()) {
                val listRowAdapter = ArrayObjectAdapter(cardPresenter)
                movies.forEach { movie ->
                    listRowAdapter.add(movie)
                }
                val header = HeaderItem(1, "热门电影")
                updateOrAddRow(ListRow(header, listRowAdapter), 1)
            }
        })

        // 观察推荐应用
        viewModel.recommendedApps.observe(this, Observer { apps ->
            if (apps.isNotEmpty()) {
                val listRowAdapter = ArrayObjectAdapter(cardPresenter)
                apps.forEach { app ->
                    listRowAdapter.add(app)
                }
                val header = HeaderItem(2, "推荐应用")
                updateOrAddRow(ListRow(header, listRowAdapter), 2)
            }
        })

        // 观察推荐游戏
        viewModel.recommendedGames.observe(this, Observer { games ->
            if (games.isNotEmpty()) {
                val listRowAdapter = ArrayObjectAdapter(cardPresenter)
                games.forEach { game ->
                    listRowAdapter.add(game)
                }
                val header = HeaderItem(3, "推荐游戏")
                updateOrAddRow(ListRow(header, listRowAdapter), 3)
            }
        })

        // 观察精选游戏
        viewModel.featuredGames.observe(this, Observer { games ->
            if (games.isNotEmpty()) {
                val listRowAdapter = ArrayObjectAdapter(cardPresenter)
                games.forEach { game ->
                    listRowAdapter.add(game)
                }
                val header = HeaderItem(4, "精选游戏")
                updateOrAddRow(ListRow(header, listRowAdapter), 4)
            }
        })

        // 观察推荐商品
        viewModel.recommendedProducts.observe(this, Observer { products ->
            if (products.isNotEmpty()) {
                val listRowAdapter = ArrayObjectAdapter(cardPresenter)
                products.forEach { product ->
                    listRowAdapter.add(product)
                }
                val header = HeaderItem(5, "推荐商品")
                updateOrAddRow(ListRow(header, listRowAdapter), 5)
            }
        })

        // 添加设置行
        addPreferencesRow()

        // 观察加载状态
        viewModel.isLoading.observe(this, Observer { isLoading ->
            // 可以在这里显示加载指示器
            Log.d(TAG, "Loading: $isLoading")
        })

        // 观察错误信息
        viewModel.error.observe(this, Observer { error ->
            if (error.isNotEmpty()) {
                Toast.makeText(context, error, Toast.LENGTH_LONG).show()
                Log.e(TAG, "Error: $error")
            }
        })
    }

    private fun updateOrAddRow(row: ListRow, position: Int) {
        if (position < rowsAdapter.size()) {
            rowsAdapter.replace(position, row)
        } else {
            rowsAdapter.add(row)
        }
    }

    private fun addPreferencesRow() {
        val gridHeader = HeaderItem(6, "设置")
        val mGridPresenter = GridItemPresenter()
        val gridRowAdapter = ArrayObjectAdapter(mGridPresenter)
        gridRowAdapter.add("游戏")
        gridRowAdapter.add("商城")
        gridRowAdapter.add("个人中心")
        gridRowAdapter.add("用户登录")
        gridRowAdapter.add("系统测试")
        gridRowAdapter.add(resources.getString(R.string.grid_view))
        gridRowAdapter.add(getString(R.string.error_fragment))
        gridRowAdapter.add(resources.getString(R.string.personal_settings))
        rowsAdapter.add(ListRow(gridHeader, gridRowAdapter))
    }

    private fun setupEventListeners() {
        setOnSearchClickedListener {
            val intent = Intent(activity!!, SearchActivity::class.java)
            startActivity(intent)
        }

        onItemViewClickedListener = ItemViewClickedListener()
        onItemViewSelectedListener = ItemViewSelectedListener()
    }

    private inner class ItemViewClickedListener : OnItemViewClickedListener {
        override fun onItemClicked(
            itemViewHolder: Presenter.ViewHolder,
            item: Any,
            rowViewHolder: RowPresenter.ViewHolder,
            row: Row
        ) {

            when (item) {
                is Movie -> {
                    Log.d(TAG, "Movie Item: " + item.toString())
                    val intent = Intent(activity!!, DetailsActivity::class.java)
                    intent.putExtra(DetailsActivity.MOVIE, item)

                    val bundle = ActivityOptionsCompat.makeSceneTransitionAnimation(
                        activity!!,
                        (itemViewHolder.view as ImageCardView).mainImageView,
                        DetailsActivity.SHARED_ELEMENT_NAME
                    ).toBundle()
                    startActivity(intent, bundle)
                }
                is ContentItem -> {
                    Log.d(TAG, "ContentItem: ${item.type} - ${item.title}")
                    val intent = Intent(activity!!, DetailsActivity::class.java)
                    intent.putExtra("CONTENT_ITEM", item)

                    val bundle = ActivityOptionsCompat.makeSceneTransitionAnimation(
                        activity!!,
                        (itemViewHolder.view as ImageCardView).mainImageView,
                        DetailsActivity.SHARED_ELEMENT_NAME
                    ).toBundle()
                    startActivity(intent, bundle)
                }
                is String -> {
                    when {
                        item == "个人中心" -> {
                            val intent = Intent(activity!!, UserCenterActivity::class.java)
                            startActivity(intent)
                        }
                        item == "用户登录" -> {
                            val intent = Intent(activity!!, LoginActivity::class.java)
                            startActivity(intent)
                        }
                        item == "系统测试" -> {
                            val intent = Intent(activity!!, TestActivity::class.java)
                            startActivity(intent)
                        }
                        item == "游戏" -> {
                            val intent = Intent(activity!!, GameActivity::class.java)
                            startActivity(intent)
                        }
                        item == "商城" -> {
                            val intent = Intent(activity!!, ShopActivity::class.java)
                            startActivity(intent)
                        }
                        item.contains(getString(R.string.error_fragment)) -> {
                            val intent = Intent(activity!!, BrowseErrorActivity::class.java)
                            startActivity(intent)
                        }
                        else -> {
                            Toast.makeText(activity!!, item, Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            }
        }
    }

    private inner class ItemViewSelectedListener : OnItemViewSelectedListener {
        override fun onItemSelected(
            itemViewHolder: Presenter.ViewHolder?, item: Any?,
            rowViewHolder: RowPresenter.ViewHolder, row: Row
        ) {
            when (item) {
                is Movie -> {
                    mBackgroundUri = item.backgroundImageUrl
                    startBackgroundTimer()
                }
                is ContentItem -> {
                    mBackgroundUri = item.backgroundImageUrl
                    startBackgroundTimer()
                }
            }
        }
    }

    private fun updateBackground(uri: String?) {
        val width = mMetrics.widthPixels
        val height = mMetrics.heightPixels
        Glide.with(activity!!)
            .load(uri)
            .centerCrop()
            .error(mDefaultBackground)
            .into<SimpleTarget<Drawable>>(
                object : SimpleTarget<Drawable>(width, height) {
                    override fun onResourceReady(
                        drawable: Drawable,
                        transition: Transition<in Drawable>?
                    ) {
                        mBackgroundManager.drawable = drawable
                    }
                })
        mBackgroundTimer?.cancel()
    }

    private fun startBackgroundTimer() {
        mBackgroundTimer?.cancel()
        mBackgroundTimer = Timer()
        mBackgroundTimer?.schedule(UpdateBackgroundTask(), BACKGROUND_UPDATE_DELAY.toLong())
    }

    private inner class UpdateBackgroundTask : TimerTask() {

        override fun run() {
            mHandler.post { updateBackground(mBackgroundUri) }
        }
    }

    private inner class GridItemPresenter : Presenter() {
        override fun onCreateViewHolder(parent: ViewGroup): Presenter.ViewHolder {
            val view = TextView(parent.context)
            view.layoutParams = ViewGroup.LayoutParams(GRID_ITEM_WIDTH, GRID_ITEM_HEIGHT)
            view.isFocusable = true
            view.isFocusableInTouchMode = true
            view.setBackgroundColor(ContextCompat.getColor(activity!!, R.color.default_background))
            view.setTextColor(Color.WHITE)
            view.gravity = Gravity.CENTER
            return Presenter.ViewHolder(view)
        }

        override fun onBindViewHolder(viewHolder: Presenter.ViewHolder, item: Any) {
            (viewHolder.view as TextView).text = item as String
        }

        override fun onUnbindViewHolder(viewHolder: Presenter.ViewHolder) {}
    }

    companion object {
        private val TAG = "MainFragment"

        private val BACKGROUND_UPDATE_DELAY = 300
        private val GRID_ITEM_WIDTH = 200
        private val GRID_ITEM_HEIGHT = 200
        private val NUM_ROWS = 6
        private val NUM_COLS = 15
    }
}