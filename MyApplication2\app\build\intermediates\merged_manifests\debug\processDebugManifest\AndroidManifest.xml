<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.myapplicationtv"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="35" />

    <uses-permission android:name="android.permission.INTERNET" />

    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="true" />

    <permission
        android:name="com.example.myapplicationtv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.example.myapplicationtv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.example.myapplicationtv.TVApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/Theme.MyApplicationTV"
        android:usesCleartextTraffic="true" >
        <activity
            android:name="com.example.myapplicationtv.MainActivity"
            android:banner="@drawable/app_icon_your_company"
            android:exported="true"
            android:icon="@drawable/app_icon_your_company"
            android:label="@string/app_name"
            android:logo="@drawable/app_icon_your_company"
            android:screenOrientation="landscape" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.example.myapplicationtv.DetailsActivity"
            android:exported="false" />
        <activity
            android:name="com.example.myapplicationtv.PlaybackActivity"
            android:exported="false" />
        <activity
            android:name="com.example.myapplicationtv.BrowseErrorActivity"
            android:exported="false" />
        <activity
            android:name="com.example.myapplicationtv.SearchActivity"
            android:exported="false" />
        <activity
            android:name="com.example.myapplicationtv.LoginActivity"
            android:exported="false" />
        <activity
            android:name="com.example.myapplicationtv.UserCenterActivity"
            android:exported="false" />
        <activity
            android:name="com.example.myapplicationtv.TestActivity"
            android:exported="false" />
        <activity
            android:name="com.example.myapplicationtv.GameActivity"
            android:exported="false" />
        <activity
            android:name="com.example.myapplicationtv.ShopActivity"
            android:exported="false" />
        <activity
            android:name="com.example.myapplicationtv.ApplicationActivity"
            android:exported="false" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.example.myapplicationtv.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>