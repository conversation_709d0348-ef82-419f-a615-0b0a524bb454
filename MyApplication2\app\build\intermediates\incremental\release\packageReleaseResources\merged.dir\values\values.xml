<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="background_gradient_end">#DDDDDD</color>
    <color name="background_gradient_start">#000000</color>
    <color name="default_background">#3d3d3d</color>
    <color name="fastlane_background">#0096a6</color>
    <color name="search_opaque">#ffaa3f</color>
    <color name="selected_background">#ffaa3f</color>
    <string name="app_name">My Application TV</string>
    <string name="browse_title">Videos by Your Company</string>
    <string name="buy_1">Buy and Own</string>
    <string name="buy_2">AT $9.99</string>
    <string name="dismiss_error">Dismiss</string>
    <string name="error_fragment">Error Fragment</string>
    <string name="error_fragment_message">An error occurred</string>
    <string name="grid_view">Grid View</string>
    <string name="movie">Movie</string>
    <string name="personal_settings">Personal Settings</string>
    <string name="related_movies">Related Videos</string>
    <string name="rent_1">Rent By Day</string>
    <string name="rent_2">From $1.99</string>
    <string name="watch_trailer_1">Watch trailer</string>
    <string name="watch_trailer_2">FREE</string>
    <style name="Theme.MyApplicationTV" parent="@style/Theme.Leanback"/>
</resources>