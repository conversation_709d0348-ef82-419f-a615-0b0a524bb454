#Mon Jul 07 17:14:35 CST 2025
com.example.myapplicationtv.app-main-33\:/layout/activity_login.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_login.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/app_category_background.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_app_category_background.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/ic_shopping.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_shopping.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/ic_movie.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_movie.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/app_category_dynamic_background.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_app_category_dynamic_background.xml.flat
com.example.myapplicationtv.app-main-33\:/layout/activity_user_center.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_user_center.xml.flat
com.example.myapplicationtv.app-main-33\:/mipmap-hdpi/ic_launcher.webp=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.myapplicationtv.app-main-33\:/layout/fragment_login.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_login.xml.flat
com.example.myapplicationtv.app-main-33\:/layout/item_navigation.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_navigation.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/ic_live_tv.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_live_tv.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/ic_music.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_music.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/app_icon_your_company.png=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_app_icon_your_company.png.flat
com.example.myapplicationtv.app-main-33\:/drawable/movie_card_background.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_movie_card_background.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/search_button_background.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_search_button_background.xml.flat
com.example.myapplicationtv.app-main-33\:/layout/item_app_category.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_app_category.xml.flat
com.example.myapplicationtv.app-main-33\:/layout/item_featured_movie.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_featured_movie.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/ic_video_library.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_video_library.xml.flat
com.example.myapplicationtv.app-main-33\:/mipmap-xhdpi/ic_launcher.webp=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.myapplicationtv.app-main-33\:/layout/activity_details.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_details.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/ic_fitness.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fitness.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/default_background.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_default_background.xml.flat
com.example.myapplicationtv.app-main-33\:/layout/activity_search.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_search.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/ic_games.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_games.xml.flat
com.example.myapplicationtv.app-main-33\:/layout/fragment_main_content.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_main_content.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/ic_car.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_car.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/movie.png=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_movie.png.flat
com.example.myapplicationtv.app-main-33\:/drawable/button_background.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_background.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/ic_tv_shows.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_tv_shows.xml.flat
com.example.myapplicationtv.app-main-33\:/mipmap-mdpi/ic_launcher.webp=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.myapplicationtv.app-main-33\:/drawable/edit_text_background.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_edit_text_background.xml.flat
com.example.myapplicationtv.app-main-33\:/mipmap-xxhdpi/ic_launcher.webp=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.myapplicationtv.app-main-33\:/drawable/movie_card_focus_overlay.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_movie_card_focus_overlay.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/ic_search.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_search.xml.flat
com.example.myapplicationtv.app-main-33\:/layout/activity_test.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_test.xml.flat
com.example.myapplicationtv.app-main-33\:/layout/activity_main.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/ic_apps.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_apps.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/navigation_item_background.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_navigation_item_background.xml.flat
com.example.myapplicationtv.app-main-33\:/drawable/ic_person.xml=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_person.xml.flat
com.example.myapplicationtv.app-main-33\:/mipmap-xxxhdpi/ic_launcher.webp=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
