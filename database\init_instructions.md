# 数据库初始化说明

## 前置条件

请确保已安装MySQL 8.0+，并且服务正在运行。

## 初始化步骤

### 方法一：使用MySQL命令行工具

1. **打开MySQL命令行**
   ```bash
   # Windows (如果MySQL在PATH中)
   mysql -u root -p
   
   # 或者使用完整路径，例如：
   "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -p
   ```

2. **输入密码**
   ```
   Enter password: 123456
   ```

3. **执行创建脚本**
   ```sql
   source E:/aikaifa/MovieTV/database/create_database.sql;
   ```

### 方法二：使用MySQL Workbench

1. 打开MySQL Workbench
2. 连接到本地MySQL服务器 (root/123456)
3. 打开SQL文件：`database/create_database.sql`
4. 执行整个脚本

### 方法三：使用其他数据库管理工具

如Navicat、phpMyAdmin等，导入`create_database.sql`文件执行。

## 验证安装

执行以下SQL验证数据库是否创建成功：

```sql
-- 1. 检查数据库是否存在
SHOW DATABASES LIKE 'movietv';

-- 2. 使用数据库
USE movietv;

-- 3. 检查表是否创建成功
SHOW TABLES;

-- 4. 检查初始数据
SELECT COUNT(*) as admin_count FROM admin;
SELECT COUNT(*) as category_count FROM category;
SELECT COUNT(*) as config_count FROM system_config;

-- 5. 测试管理员登录
SELECT id, username, nickname, role FROM admin WHERE username = 'admin';
```

预期结果：
- 数据库 `movietv` 存在
- 包含16个表
- admin表有1条记录
- category表有28条记录
- system_config表有8条记录

## 可能遇到的问题

### 1. MySQL服务未启动
```bash
# Windows
net start mysql80

# 或者通过服务管理器启动MySQL服务
```

### 2. 权限问题
确保root用户有创建数据库的权限：
```sql
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 字符集问题
如果遇到中文乱码，检查MySQL配置：
```sql
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';
```

### 4. 连接问题
检查MySQL配置文件(my.ini或my.cnf)：
```ini
[mysql]
default-character-set=utf8mb4

[mysqld]
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
```

## 下一步

数据库初始化完成后，可以继续进行：

1. **后端Spring Boot项目配置**
   - 配置数据库连接
   - 添加JPA依赖
   - 创建实体类

2. **API接口开发**
   - 用户管理接口
   - 影视内容接口
   - 其他业务接口

3. **前端对接**
   - 后台管理系统
   - TV端应用

## 联系方式

如果在数据库初始化过程中遇到问题，请提供：
1. 错误信息截图
2. MySQL版本信息
3. 操作系统信息

这样可以更好地协助解决问题。
