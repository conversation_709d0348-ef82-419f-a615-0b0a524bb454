package com.example.myapplicationtv

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.core.app.ActivityOptionsCompat
import androidx.leanback.app.BrowseSupportFragment
import androidx.leanback.widget.*
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.Observer
import com.example.myapplicationtv.viewmodel.UserCenterViewModel
import com.example.myapplicationtv.utils.UserManager

/**
 * 用户中心Fragment
 */
class UserCenterFragment : BrowseSupportFragment() {

    private lateinit var viewModel: UserCenterViewModel
    private lateinit var rowsAdapter: ArrayObjectAdapter
    private val cardPresenter = CardPresenter()

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        
        setupUI()
        initViewModel()
        setupEventListeners()
    }

    private fun setupUI() {
        title = "个人中心"
        headersState = BrowseSupportFragment.HEADERS_ENABLED
        isHeadersTransitionOnBackEnabled = true
        
        // 初始化适配器
        rowsAdapter = ArrayObjectAdapter(ListRowPresenter())
        adapter = rowsAdapter
    }

    private fun initViewModel() {
        viewModel = ViewModelProvider(this)[UserCenterViewModel::class.java]
        
        // 观察收藏列表
        viewModel.favorites.observe(this, Observer { favorites ->
            if (favorites.isNotEmpty()) {
                val listRowAdapter = ArrayObjectAdapter(cardPresenter)
                favorites.forEach { favorite ->
                    listRowAdapter.add(favorite)
                }
                val header = HeaderItem(0, "我的收藏 (${favorites.size})")
                updateOrAddRow(ListRow(header, listRowAdapter), 0)
            }
        })
        
        // 观察历史记录
        viewModel.history.observe(this, Observer { history ->
            if (history.isNotEmpty()) {
                val listRowAdapter = ArrayObjectAdapter(cardPresenter)
                history.forEach { item ->
                    listRowAdapter.add(item)
                }
                val header = HeaderItem(1, "观看历史 (${history.size})")
                updateOrAddRow(ListRow(header, listRowAdapter), 1)
            }
        })
        
        // 添加设置选项
        addSettingsRow()
        
        // 观察加载状态
        viewModel.isLoading.observe(this, Observer { isLoading ->
            Log.d(TAG, "Loading: $isLoading")
        })
        
        // 观察错误信息
        viewModel.error.observe(this, Observer { error ->
            if (error.isNotEmpty()) {
                Toast.makeText(context, error, Toast.LENGTH_LONG).show()
                Log.e(TAG, "Error: $error")
            }
        })
        
        // 加载数据
        viewModel.loadUserData()
    }

    private fun setupEventListeners() {
        onItemViewClickedListener = ItemViewClickedListener()
        onItemViewSelectedListener = ItemViewSelectedListener()
    }

    private fun updateOrAddRow(row: ListRow, position: Int) {
        if (position < rowsAdapter.size()) {
            rowsAdapter.replace(position, row)
        } else {
            rowsAdapter.add(row)
        }
    }

    private fun addSettingsRow() {
        val gridHeader = HeaderItem(2, "设置")
        val mGridPresenter = GridItemPresenter()
        val gridRowAdapter = ArrayObjectAdapter(mGridPresenter)
        
        val user = UserManager.getUser()
        if (user != null) {
            gridRowAdapter.add("用户: ${user.name}")
            gridRowAdapter.add("会员等级: ${user.memberLevel ?: "普通用户"}")
        }
        gridRowAdapter.add("账号设置")
        gridRowAdapter.add("退出登录")
        
        rowsAdapter.add(ListRow(gridHeader, gridRowAdapter))
    }

    private inner class ItemViewClickedListener : OnItemViewClickedListener {
        override fun onItemClicked(
            itemViewHolder: Presenter.ViewHolder,
            item: Any,
            rowViewHolder: RowPresenter.ViewHolder,
            row: Row
        ) {
            when (item) {
                is ContentItem -> {
                    Log.d(TAG, "ContentItem clicked: ${item.type} - ${item.title}")
                    val intent = Intent(activity!!, DetailsActivity::class.java)
                    intent.putExtra(DetailsActivity.CONTENT_ITEM, item)

                    val bundle = ActivityOptionsCompat.makeSceneTransitionAnimation(
                        activity!!,
                        (itemViewHolder.view as ImageCardView).mainImageView,
                        DetailsActivity.SHARED_ELEMENT_NAME
                    ).toBundle()
                    startActivity(intent, bundle)
                }
                is String -> {
                    when (item) {
                        "账号设置" -> {
                            Toast.makeText(activity!!, "账号设置功能开发中", Toast.LENGTH_SHORT).show()
                        }
                        "退出登录" -> {
                            viewModel.logout()
                            val intent = Intent(activity!!, LoginActivity::class.java)
                            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
                            startActivity(intent)
                            activity!!.finish()
                        }
                        else -> {
                            Toast.makeText(activity!!, item, Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            }
        }
    }

    private inner class ItemViewSelectedListener : OnItemViewSelectedListener {
        override fun onItemSelected(
            itemViewHolder: Presenter.ViewHolder?, item: Any?,
            rowViewHolder: RowPresenter.ViewHolder, row: Row
        ) {
            // 可以在这里处理选中事件
        }
    }

    private inner class GridItemPresenter : Presenter() {
        override fun onCreateViewHolder(parent: ViewGroup): Presenter.ViewHolder {
            val view = android.widget.TextView(parent.context)
            view.layoutParams = ViewGroup.LayoutParams(200, 200)
            view.isFocusable = true
            view.isFocusableInTouchMode = true
            view.setBackgroundColor(androidx.core.content.ContextCompat.getColor(activity!!, R.color.default_background))
            view.setTextColor(android.graphics.Color.WHITE)
            view.gravity = android.view.Gravity.CENTER
            return Presenter.ViewHolder(view)
        }

        override fun onBindViewHolder(viewHolder: Presenter.ViewHolder, item: Any) {
            (viewHolder.view as android.widget.TextView).text = item as String
        }

        override fun onUnbindViewHolder(viewHolder: Presenter.ViewHolder) {}
    }

    companion object {
        private const val TAG = "UserCenterFragment"
    }
}
