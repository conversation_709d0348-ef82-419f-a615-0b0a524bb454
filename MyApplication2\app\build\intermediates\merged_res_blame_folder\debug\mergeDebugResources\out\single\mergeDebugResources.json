[{"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_navigation_item_background.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\navigation_item_background.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_music.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_music.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_edit_text_background.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\edit_text_background.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_payment.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_payment.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_activity_details.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\activity_details.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_item_app_category.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\item_app_category.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_search_button_background.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\search_button_background.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_app_category_background.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\app_category_background.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_phone.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_phone.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_item_hot_product.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\item_hot_product.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_default_background.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\default_background.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_food.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_food.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_fragment_game.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\fragment_game.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_fragment_main_content.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\fragment_main_content.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_activity_main.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\activity_main.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_activity_game.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\activity_game.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_book.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_book.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_social.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_social.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_item_recommended_app.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\item_recommended_app.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_button_background.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\button_background.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_item_game_category.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\item_game_category.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_activity_search.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\activity_search.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_work.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_work.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_tools.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_tools.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_tv.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_tv.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_activity_login.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\activity_login.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_map.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_map.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_fragment_shop.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\fragment_shop.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_app_category_dynamic_background.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\app_category_dynamic_background.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_item_featured_movie.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\item_featured_movie.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_item_featured_game.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\item_featured_game.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_fitness.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_fitness.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_news.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_news.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_service.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_service.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_sports.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_sports.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_smart_home.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_smart_home.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_video_library.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_video_library.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_category_item_background.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\category_item_background.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_gradient_overlay.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\gradient_overlay.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_appliance.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_appliance.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_item_hot_game.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\item_hot_game.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_cloud.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_cloud.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_tv_shows.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_tv_shows.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_video.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_video.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_item_navigation.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\item_navigation.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_smart_home_gradient.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\smart_home_gradient.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_shopping.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_shopping.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_computer.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_computer.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_app_icon_your_company.png.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\app_icon_your_company.png"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_fragment_login.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\fragment_login.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_fragment_application.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\fragment_application.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_apps.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_apps.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_banner_button_background.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\banner_button_background.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_activity_application.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\activity_application.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_clothing.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_clothing.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_movie.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_movie.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_activity_shop.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\activity_shop.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_app_store.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_app_store.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_movie_card_focus_overlay.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\movie_card_focus_overlay.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_activity_test.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\activity_test.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_person.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_person.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_activity_user_center.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\activity_user_center.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_movie.png.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\movie.png"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_games.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_games.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_live_tv.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_live_tv.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_app_store_gradient.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\app_store_gradient.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\layout_item_product_category.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\layout\\item_product_category.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_education.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_education.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_headphone.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_headphone.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_car.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_car.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_digital.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_digital.xml"}, {"merged": "com.example.myapplicationtv.app-debug-32:/drawable_card_selector.xml.flat", "source": "com.example.myapplicationtv.app-main-34:/drawable/card_selector.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_speaker.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_speaker.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_movie_card_background.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\movie_card_background.xml"}, {"merged": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-debug-32:\\drawable_ic_search.xml.flat", "source": "D:\\Android\\GradleRepository\\daemon\\8.12\\com.example.myapplicationtv.app-main-34:\\drawable\\ic_search.xml"}]