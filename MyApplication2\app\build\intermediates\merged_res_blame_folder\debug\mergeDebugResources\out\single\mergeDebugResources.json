[{"merged": "com.example.myapplicationtv.app-debug-31:/drawable_app_icon_your_company.png.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/app_icon_your_company.png"}, {"merged": "com.example.myapplicationtv.app-debug-31:/layout_activity_details.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/layout/activity_details.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/layout_activity_search.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/layout/activity_search.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_movie_card_focus_overlay.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/movie_card_focus_overlay.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_search_button_background.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/search_button_background.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_ic_live_tv.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/ic_live_tv.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_movie_card_background.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/movie_card_background.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/layout_activity_user_center.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/layout/activity_user_center.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/layout_item_featured_movie.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/layout/item_featured_movie.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.myapplicationtv.app-main-33:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_ic_car.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/ic_car.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/layout_fragment_main_content.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/layout/fragment_main_content.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_default_background.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/default_background.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.myapplicationtv.app-main-33:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_edit_text_background.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/edit_text_background.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_ic_music.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/ic_music.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/layout_activity_test.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/layout/activity_test.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/layout_activity_main.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/layout/activity_main.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_button_background.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/button_background.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_ic_person.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/ic_person.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_app_category_background.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/app_category_background.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/layout_item_navigation.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/layout/item_navigation.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_ic_tv_shows.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/ic_tv_shows.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_ic_fitness.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/ic_fitness.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_ic_apps.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/ic_apps.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_ic_shopping.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/ic_shopping.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.myapplicationtv.app-main-33:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_ic_games.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/ic_games.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.myapplicationtv.app-main-33:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_navigation_item_background.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/navigation_item_background.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_ic_movie.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/ic_movie.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/layout_item_app_category.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/layout/item_app_category.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.myapplicationtv.app-main-33:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_ic_video_library.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/ic_video_library.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_ic_search.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/ic_search.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/layout_activity_login.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/layout/activity_login.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/layout_fragment_login.xml.flat", "source": "com.example.myapplicationtv.app-main-33:/layout/fragment_login.xml"}, {"merged": "com.example.myapplicationtv.app-debug-31:/drawable_movie.png.flat", "source": "com.example.myapplicationtv.app-main-33:/drawable/movie.png"}]