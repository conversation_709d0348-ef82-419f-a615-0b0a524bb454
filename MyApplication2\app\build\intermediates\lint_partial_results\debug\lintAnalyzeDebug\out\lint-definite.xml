<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/TestHelper.kt"
            line="115"
            column="16"
            startOffset="3547"
            endLine="115"
            endColumn="64"
            endOffset="3595"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/ErrorFragment.kt"
            line="21"
            column="39"
            startOffset="564"
            endLine="21"
            endColumn="49"
            endOffset="574"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/ErrorFragment.kt"
            line="21"
            column="39"
            startOffset="564"
            endLine="21"
            endColumn="49"
            endOffset="574"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireFragmentManager() instead of fragmentManager!!">
        <fix-replace
            description="Replace with requireFragmentManager()"
            robot="true"
            independent="true"
            oldString="fragmentManager!!"
            replacement="requireFragmentManager()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/ErrorFragment.kt"
            line="27"
            column="13"
            startOffset="866"
            endLine="27"
            endColumn="30"
            endOffset="883"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireFragmentManager() instead of fragmentManager!!">
        <fix-replace
            description="Replace with requireFragmentManager()"
            robot="true"
            independent="true"
            oldString="fragmentManager!!"
            replacement="requireFragmentManager()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/ErrorFragment.kt"
            line="27"
            column="13"
            startOffset="866"
            endLine="27"
            endColumn="30"
            endOffset="883"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="76"
            column="35"
            startOffset="2542"
            endLine="76"
            endColumn="45"
            endOffset="2552"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="77"
            column="56"
            startOffset="2616"
            endLine="77"
            endColumn="66"
            endOffset="2626"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="79"
            column="9"
            startOffset="2703"
            endLine="79"
            endColumn="19"
            endOffset="2713"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="89"
            column="45"
            startOffset="3086"
            endLine="89"
            endColumn="55"
            endOffset="3096"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="89"
            column="45"
            startOffset="3086"
            endLine="89"
            endColumn="55"
            endOffset="3096"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="91"
            column="56"
            startOffset="3215"
            endLine="91"
            endColumn="66"
            endOffset="3225"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="91"
            column="56"
            startOffset="3215"
            endLine="91"
            endColumn="66"
            endOffset="3225"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="221"
            column="33"
            startOffset="7663"
            endLine="221"
            endColumn="43"
            endOffset="7673"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="310"
            column="20"
            startOffset="11316"
            endLine="310"
            endColumn="30"
            endOffset="11326"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="57"
            column="32"
            startOffset="2238"
            endLine="57"
            endColumn="42"
            endOffset="2248"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="61"
            column="30"
            startOffset="2440"
            endLine="61"
            endColumn="40"
            endOffset="2450"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="74"
            column="33"
            startOffset="3018"
            endLine="74"
            endColumn="43"
            endOffset="3028"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="83"
            column="20"
            startOffset="3319"
            endLine="83"
            endColumn="30"
            endOffset="3329"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="104"
            column="55"
            startOffset="4130"
            endLine="104"
            endColumn="65"
            endOffset="4140"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="104"
            column="55"
            startOffset="4130"
            endLine="104"
            endColumn="65"
            endOffset="4140"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="105"
            column="38"
            startOffset="4210"
            endLine="105"
            endColumn="48"
            endOffset="4220"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="106"
            column="39"
            startOffset="4280"
            endLine="106"
            endColumn="49"
            endOffset="4290"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="110"
            column="20"
            startOffset="4424"
            endLine="110"
            endColumn="30"
            endOffset="4434"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="234"
            column="36"
            startOffset="8630"
            endLine="234"
            endColumn="46"
            endOffset="8640"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="234"
            column="36"
            startOffset="8630"
            endLine="234"
            endColumn="46"
            endOffset="8640"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="247"
            column="41"
            startOffset="9245"
            endLine="247"
            endColumn="51"
            endOffset="9255"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="247"
            column="41"
            startOffset="9245"
            endLine="247"
            endColumn="51"
            endOffset="9255"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="252"
            column="36"
            startOffset="9490"
            endLine="252"
            endColumn="46"
            endOffset="9500"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="252"
            column="36"
            startOffset="9490"
            endLine="252"
            endColumn="46"
            endOffset="9500"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="255"
            column="36"
            startOffset="9659"
            endLine="255"
            endColumn="46"
            endOffset="9669"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="255"
            column="36"
            startOffset="9659"
            endLine="255"
            endColumn="46"
            endOffset="9669"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="258"
            column="36"
            startOffset="9828"
            endLine="258"
            endColumn="46"
            endOffset="9838"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="258"
            column="36"
            startOffset="9828"
            endLine="258"
            endColumn="46"
            endOffset="9838"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="261"
            column="36"
            startOffset="9988"
            endLine="261"
            endColumn="46"
            endOffset="9998"/>
    </incident>

    <incident
        id="UseRequireInsteadOfGet"
        severity="error"
        message="Use requireActivity() instead of activity!!">
        <fix-replace
            description="Replace with requireActivity()"
            robot="true"
            independent="true"
            oldString="activity!!"
            replacement="requireActivity()"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/VideoDetailsFragment.kt"
            line="261"
            column="36"
            startOffset="9988"
            endLine="261"
            endColumn="46"
            endOffset="9998"/>
    </incident>

    <incident
        id="FragmentLiveDataObserve"
        severity="error"
        message="Use viewLifecycleOwner as the LifecycleOwner.">
        <fix-replace
            description="Replace with viewLifecycleOwner"
            replacement="viewLifecycleOwner"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="109"
            column="45"
            startOffset="3669"
            endLine="109"
            endColumn="49"
            endOffset="3673"/>
    </incident>

    <incident
        id="FragmentLiveDataObserve"
        severity="error"
        message="Use viewLifecycleOwner as the LifecycleOwner.">
        <fix-replace
            description="Replace with viewLifecycleOwner"
            replacement="viewLifecycleOwner"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="121"
            column="37"
            startOffset="4110"
            endLine="121"
            endColumn="41"
            endOffset="4114"/>
    </incident>

    <incident
        id="FragmentLiveDataObserve"
        severity="error"
        message="Use viewLifecycleOwner as the LifecycleOwner.">
        <fix-replace
            description="Replace with viewLifecycleOwner"
            replacement="viewLifecycleOwner"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="133"
            column="43"
            startOffset="4557"
            endLine="133"
            endColumn="47"
            endOffset="4561"/>
    </incident>

    <incident
        id="FragmentLiveDataObserve"
        severity="error"
        message="Use viewLifecycleOwner as the LifecycleOwner.">
        <fix-replace
            description="Replace with viewLifecycleOwner"
            replacement="viewLifecycleOwner"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="145"
            column="44"
            startOffset="4995"
            endLine="145"
            endColumn="48"
            endOffset="4999"/>
    </incident>

    <incident
        id="FragmentLiveDataObserve"
        severity="error"
        message="Use viewLifecycleOwner as the LifecycleOwner.">
        <fix-replace
            description="Replace with viewLifecycleOwner"
            replacement="viewLifecycleOwner"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="157"
            column="41"
            startOffset="5435"
            endLine="157"
            endColumn="45"
            endOffset="5439"/>
    </incident>

    <incident
        id="FragmentLiveDataObserve"
        severity="error"
        message="Use viewLifecycleOwner as the LifecycleOwner.">
        <fix-replace
            description="Replace with viewLifecycleOwner"
            replacement="viewLifecycleOwner"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="169"
            column="47"
            startOffset="5881"
            endLine="169"
            endColumn="51"
            endOffset="5885"/>
    </incident>

    <incident
        id="FragmentLiveDataObserve"
        severity="error"
        message="Use viewLifecycleOwner as the LifecycleOwner.">
        <fix-replace
            description="Replace with viewLifecycleOwner"
            replacement="viewLifecycleOwner"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="184"
            column="37"
            startOffset="6378"
            endLine="184"
            endColumn="41"
            endOffset="6382"/>
    </incident>

    <incident
        id="FragmentLiveDataObserve"
        severity="error"
        message="Use viewLifecycleOwner as the LifecycleOwner.">
        <fix-replace
            description="Replace with viewLifecycleOwner"
            replacement="viewLifecycleOwner"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/MainFragment.kt"
            line="190"
            column="33"
            startOffset="6544"
            endLine="190"
            endColumn="37"
            endOffset="6548"/>
    </incident>

    <incident
        id="FragmentLiveDataObserve"
        severity="error"
        message="Use viewLifecycleOwner as the LifecycleOwner.">
        <fix-replace
            description="Replace with viewLifecycleOwner"
            replacement="viewLifecycleOwner"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/UserCenterFragment.kt"
            line="48"
            column="37"
            startOffset="1462"
            endLine="48"
            endColumn="41"
            endOffset="1466"/>
    </incident>

    <incident
        id="FragmentLiveDataObserve"
        severity="error"
        message="Use viewLifecycleOwner as the LifecycleOwner.">
        <fix-replace
            description="Replace with viewLifecycleOwner"
            replacement="viewLifecycleOwner"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/UserCenterFragment.kt"
            line="60"
            column="35"
            startOffset="1944"
            endLine="60"
            endColumn="39"
            endOffset="1948"/>
    </incident>

    <incident
        id="FragmentLiveDataObserve"
        severity="error"
        message="Use viewLifecycleOwner as the LifecycleOwner.">
        <fix-replace
            description="Replace with viewLifecycleOwner"
            replacement="viewLifecycleOwner"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/UserCenterFragment.kt"
            line="75"
            column="37"
            startOffset="2464"
            endLine="75"
            endColumn="41"
            endOffset="2468"/>
    </incident>

    <incident
        id="FragmentLiveDataObserve"
        severity="error"
        message="Use viewLifecycleOwner as the LifecycleOwner.">
        <fix-replace
            description="Replace with viewLifecycleOwner"
            replacement="viewLifecycleOwner"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/UserCenterFragment.kt"
            line="80"
            column="33"
            startOffset="2610"
            endLine="80"
            endColumn="37"
            endOffset="2614"/>
    </incident>

    <incident
        id="FragmentTagUsage"
        severity="warning"
        message="Replace the &lt;fragment> tag with FragmentContainerView.">
        <fix-replace
            description="Replace with androidx.fragment.app.FragmentContainerView"
            oldString="fragment"
            replacement="androidx.fragment.app.FragmentContainerView"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="2"
            column="2"
            startOffset="40"
            endLine="2"
            endColumn="10"
            endOffset="48"/>
    </incident>

    <incident
        id="FragmentTagUsage"
        severity="warning"
        message="Replace the &lt;fragment> tag with FragmentContainerView.">
        <fix-replace
            description="Replace with androidx.fragment.app.FragmentContainerView"
            oldString="fragment"
            replacement="androidx.fragment.app.FragmentContainerView"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_search.xml"
            line="2"
            column="2"
            startOffset="40"
            endLine="2"
            endColumn="10"
            endOffset="48"/>
    </incident>

    <incident
        id="FragmentTagUsage"
        severity="warning"
        message="Replace the &lt;fragment> tag with FragmentContainerView.">
        <fix-replace
            description="Replace with androidx.fragment.app.FragmentContainerView"
            oldString="fragment"
            replacement="androidx.fragment.app.FragmentContainerView"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_user_center.xml"
            line="2"
            column="2"
            startOffset="40"
            endLine="2"
            endColumn="10"
            endOffset="48"/>
    </incident>

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="27"
            column="13"
            startOffset="998"
            endLine="27"
            endColumn="45"
            endOffset="1030"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0">
        <fix-replace
            description="Change to 2.11.0"
            family="Update versions"
            oldString="2.10.1"
            replacement="2.11.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="47"
            column="20"
            startOffset="1136"
            endLine="47"
            endColumn="54"
            endOffset="1170"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.1.0">
        <fix-replace
            description="Change to 2.1.0"
            family="Update versions"
            oldString="2.0.21"
            replacement="2.1.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="3"
            column="10"
            startOffset="35"
            endLine="3"
            endColumn="18"
            endOffset="43"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="29"
            column="13"
            startOffset="1104"
            endLine="29"
            endColumn="50"
            endOffset="1141"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `ApiClient` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/network/ApiClient.kt"
            line="13"
            column="1"
            startOffset="370"
            endLine="98"
            endColumn="2"
            endOffset="3013"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields; this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/network/ApiClient.kt"
            line="22"
            column="5"
            startOffset="561"
            endLine="22"
            endColumn="41"
            endOffset="597"/>
    </incident>

    <incident
        id="MergeRootFrame"
        severity="warning"
        message="This `&lt;FrameLayout>` can be replaced with a `&lt;merge>` tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_details.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="8"
            endColumn="28"
            endOffset="346"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/default_background` with a theme that also paints a background (inferred theme is `@style/Theme.MyApplicationTV`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_test.xml"
            line="8"
            column="5"
            startOffset="288"
            endLine="8"
            endColumn="51"
            endOffset="334"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/default_background` with a theme that also paints a background (inferred theme is `@style/Theme.MyApplicationTV`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
            line="8"
            column="5"
            startOffset="288"
            endLine="8"
            endColumn="51"
            endOffset="334"/>
    </incident>

    <incident
        id="IconLauncherShape"
        severity="warning"
        message="Launcher icons should not fill every pixel of their square region; see the design guide for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/app_icon_your_company.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/app_icon_your_company.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/app_icon_your_company.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/movie.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/movie.png"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
                    startOffset="1900"
                    endOffset="3074"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
                    startOffset="2080"
                    endOffset="2559"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
                    startOffset="2569"
                    endOffset="3053"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
            line="60"
            column="10"
            startOffset="2081"
            endLine="60"
            endColumn="16"
            endOffset="2087"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
                    startOffset="1900"
                    endOffset="3074"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
                    startOffset="2080"
                    endOffset="2559"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
                    startOffset="2569"
                    endOffset="3053"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
            line="73"
            column="10"
            startOffset="2570"
            endLine="73"
            endColumn="16"
            endOffset="2576"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
            line="26"
            column="6"
            startOffset="874"
            endLine="26"
            endColumn="14"
            endOffset="882"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
            line="40"
            column="6"
            startOffset="1384"
            endLine="40"
            endColumn="14"
            endOffset="1392"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/network/ApiClient.kt"
                    startOffset="2625"
                    endOffset="2630"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/network/ApiClient.kt"
                    startOffset="2630"
                    endOffset="2631"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/network/ApiClient.kt"
                    startOffset="2631"
                    endOffset="2632"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/network/ApiClient.kt"
                    startOffset="2662"
                    endOffset="2670"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/network/ApiClient.kt"
            line="84"
            column="13"
            startOffset="2619"
            endLine="84"
            endColumn="25"
            endOffset="2631"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/network/ApiClient.kt"
                    startOffset="2851"
                    endOffset="2856"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/network/ApiClient.kt"
                    startOffset="2856"
                    endOffset="2857"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/network/ApiClient.kt"
                    startOffset="2857"
                    endOffset="2858"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/network/ApiClient.kt"
                    startOffset="2878"
                    endOffset="2886"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/network/ApiClient.kt"
            line="91"
            column="13"
            startOffset="2845"
            endLine="91"
            endColumn="25"
            endOffset="2857"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toUri` instead?">
        <fix-replace
            description="Replace with the toUri extension function"
            family="Replace with the toUri extension function"
            robot="true"
            independent="true"
            oldString="Uri.parse(videoUrl)"
            replacement="videoUrl.toUri()"
            reformat="value"
            imports="androidx.core.net.toUri"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/PlaybackVideoFragment.kt"
                startOffset="3620"
                endOffset="3639"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/PlaybackVideoFragment.kt"
            line="100"
            column="41"
            startOffset="3620"
            endLine="100"
            endColumn="60"
            endOffset="3639"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Delete &quot;val editor = &quot;"
                robot="true"
                independent="true"
                oldString="val editor = "
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="793"
                    endOffset="806"/>
            </fix-replace>
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="824"
                    endOffset="829"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="829"
                    endOffset="830"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;editor.&quot;"
                robot="true"
                independent="true"
                oldString="editor."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="839"
                    endOffset="846"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;editor.&quot;"
                robot="true"
                independent="true"
                oldString="editor."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="898"
                    endOffset="905"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;editor.&quot;"
                robot="true"
                independent="true"
                oldString="editor."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="941"
                    endOffset="948"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString="editor.apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="991"
                    endOffset="1005"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="831"
                    endOffset="831"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="890"
                    endOffset="890"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="933"
                    endOffset="933"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
            line="32"
            column="22"
            startOffset="806"
            endLine="32"
            endColumn="46"
            endOffset="830"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Delete &quot;val editor = &quot;"
                robot="true"
                independent="true"
                oldString="val editor = "
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="1756"
                    endOffset="1769"/>
            </fix-replace>
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="1787"
                    endOffset="1792"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="1792"
                    endOffset="1793"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;editor.&quot;"
                robot="true"
                independent="true"
                oldString="editor."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="1802"
                    endOffset="1809"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;editor.&quot;"
                robot="true"
                independent="true"
                oldString="editor."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="1839"
                    endOffset="1846"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;editor.&quot;"
                robot="true"
                independent="true"
                oldString="editor."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="1872"
                    endOffset="1879"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString="editor.apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="1923"
                    endOffset="1937"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="1794"
                    endOffset="1794"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="1831"
                    endOffset="1831"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
                    startOffset="1864"
                    endOffset="1864"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/UserManager.kt"
            line="73"
            column="22"
            startOffset="1769"
            endLine="73"
            endColumn="46"
            endOffset="1793"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit2-retrofit"
            robot="true">
            <fix-replace
                description="Replace with retrofitVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofitVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="99"
                    endOffset="99"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofitVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofitVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="388"
                    endOffset="388"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit2.retrofit"
                robot="true"
                replacement="libs.retrofit2.retrofit"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="940"
                    endOffset="979"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="44"
            column="20"
            startOffset="940"
            endLine="44"
            endColumn="59"
            endOffset="979"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit2-converter-gson"
            robot="true">
            <fix-replace
                description="Replace with converterGsonVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="converterGsonVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGsonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGsonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="388"
                    endOffset="388"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit2.converter.gson"
                robot="true"
                replacement="libs.retrofit2.converter.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1000"
                    endOffset="1045"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="45"
            column="20"
            startOffset="1000"
            endLine="45"
            endColumn="65"
            endOffset="1045"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for okhttp3-logging-interceptor"
            robot="true">
            <fix-replace
                description="Replace with loggingInterceptorVersion = &quot;4.11.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="loggingInterceptorVersion = &quot;4.11.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="99"
                    endOffset="99"/>
            </fix-replace>
            <fix-replace
                description="Replace with okhttp3-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptorVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="okhttp3-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptorVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="388"
                    endOffset="388"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.okhttp3.logging.interceptor"
                robot="true"
                replacement="libs.okhttp3.logging.interceptor"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1066"
                    endOffset="1115"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="46"
            column="20"
            startOffset="1066"
            endLine="46"
            endColumn="69"
            endOffset="1115"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for google-gson"
            robot="true">
            <fix-replace
                description="Replace with gsonVersion = &quot;2.10.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="gsonVersion = &quot;2.10.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with google-gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gsonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="google-gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gsonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="388"
                    endOffset="388"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.google.gson"
                robot="true"
                replacement="libs.google.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1136"
                    endOffset="1170"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="47"
            column="20"
            startOffset="1136"
            endLine="47"
            endColumn="54"
            endOffset="1170"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for jetbrains-kotlinx-coroutines-android"
            robot="true">
            <fix-replace
                description="Replace with kotlinxCoroutinesAndroidVersion = &quot;1.7.3&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="kotlinxCoroutinesAndroidVersion = &quot;1.7.3&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="63"
                    endOffset="63"/>
            </fix-replace>
            <fix-replace
                description="Replace with jetbrains-kotlinx-coroutines-android = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-android&quot;, version.ref = &quot;kotlinxCoroutinesAndroidVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="jetbrains-kotlinx-coroutines-android = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-android&quot;, version.ref = &quot;kotlinxCoroutinesAndroidVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="388"
                    endOffset="388"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.jetbrains.kotlinx.coroutines.android"
                robot="true"
                replacement="libs.jetbrains.kotlinx.coroutines.android"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1202"
                    endOffset="1258"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="50"
            column="20"
            startOffset="1202"
            endLine="50"
            endColumn="76"
            endOffset="1258"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for jetbrains-kotlinx-coroutines-core"
            robot="true">
            <fix-replace
                description="Replace with kotlinxCoroutinesCoreVersion = &quot;1.7.3&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="kotlinxCoroutinesCoreVersion = &quot;1.7.3&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="63"
                    endOffset="63"/>
            </fix-replace>
            <fix-replace
                description="Replace with jetbrains-kotlinx-coroutines-core = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-core&quot;, version.ref = &quot;kotlinxCoroutinesCoreVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="jetbrains-kotlinx-coroutines-core = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-core&quot;, version.ref = &quot;kotlinxCoroutinesCoreVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="388"
                    endOffset="388"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.jetbrains.kotlinx.coroutines.core"
                robot="true"
                replacement="libs.jetbrains.kotlinx.coroutines.core"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1279"
                    endOffset="1332"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="51"
            column="20"
            startOffset="1279"
            endLine="51"
            endColumn="73"
            endOffset="1332"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for lifecycle-viewmodel-ktx"
            robot="true">
            <fix-replace
                description="Replace with lifecycleViewmodelKtxVersion = &quot;2.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycleViewmodelKtxVersion = &quot;2.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="99"
                    endOffset="99"/>
            </fix-replace>
            <fix-replace
                description="Replace with lifecycle-viewmodel-ktx = { module = &quot;androidx.lifecycle:lifecycle-viewmodel-ktx&quot;, version.ref = &quot;lifecycleViewmodelKtxVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycle-viewmodel-ktx = { module = &quot;androidx.lifecycle:lifecycle-viewmodel-ktx&quot;, version.ref = &quot;lifecycleViewmodelKtxVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="388"
                    endOffset="388"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.lifecycle.viewmodel.ktx"
                robot="true"
                replacement="libs.lifecycle.viewmodel.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1380"
                    endOffset="1430"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="54"
            column="20"
            startOffset="1380"
            endLine="54"
            endColumn="70"
            endOffset="1430"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for lifecycle-livedata-ktx"
            robot="true">
            <fix-replace
                description="Replace with lifecycleLivedataKtxVersion = &quot;2.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycleLivedataKtxVersion = &quot;2.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="99"
                    endOffset="99"/>
            </fix-replace>
            <fix-replace
                description="Replace with lifecycle-livedata-ktx = { module = &quot;androidx.lifecycle:lifecycle-livedata-ktx&quot;, version.ref = &quot;lifecycleLivedataKtxVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycle-livedata-ktx = { module = &quot;androidx.lifecycle:lifecycle-livedata-ktx&quot;, version.ref = &quot;lifecycleLivedataKtxVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="388"
                    endOffset="388"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.lifecycle.livedata.ktx"
                robot="true"
                replacement="libs.lifecycle.livedata.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1451"
                    endOffset="1500"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="55"
            column="20"
            startOffset="1451"
            endLine="55"
            endColumn="69"
            endOffset="1500"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for lifecycle-runtime-ktx"
            robot="true">
            <fix-replace
                description="Replace with lifecycleRuntimeKtxVersion = &quot;2.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycleRuntimeKtxVersion = &quot;2.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="99"
                    endOffset="99"/>
            </fix-replace>
            <fix-replace
                description="Replace with lifecycle-runtime-ktx = { module = &quot;androidx.lifecycle:lifecycle-runtime-ktx&quot;, version.ref = &quot;lifecycleRuntimeKtxVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lifecycle-runtime-ktx = { module = &quot;androidx.lifecycle:lifecycle-runtime-ktx&quot;, version.ref = &quot;lifecycleRuntimeKtxVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="388"
                    endOffset="388"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.lifecycle.runtime.ktx"
                robot="true"
                replacement="libs.lifecycle.runtime.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1521"
                    endOffset="1569"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="56"
            column="20"
            startOffset="1521"
            endLine="56"
            endColumn="68"
            endOffset="1569"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for fragment-ktx"
            robot="true">
            <fix-replace
                description="Replace with fragmentKtxVersion = &quot;1.6.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="fragmentKtxVersion = &quot;1.6.2&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with fragment-ktx = { module = &quot;androidx.fragment:fragment-ktx&quot;, version.ref = &quot;fragmentKtxVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="fragment-ktx = { module = &quot;androidx.fragment:fragment-ktx&quot;, version.ref = &quot;fragmentKtxVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="301"
                    endOffset="301"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.fragment.ktx"
                robot="true"
                replacement="libs.fragment.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1607"
                    endOffset="1645"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="59"
            column="20"
            startOffset="1607"
            endLine="59"
            endColumn="58"
            endOffset="1645"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for recyclerview"
            robot="true">
            <fix-replace
                description="Replace with recyclerviewVersion = &quot;1.3.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="recyclerviewVersion = &quot;1.3.2&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="99"
                    endOffset="99"/>
            </fix-replace>
            <fix-replace
                description="Replace with recyclerview = { module = &quot;androidx.recyclerview:recyclerview&quot;, version.ref = &quot;recyclerviewVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="recyclerview = { module = &quot;androidx.recyclerview:recyclerview&quot;, version.ref = &quot;recyclerviewVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="388"
                    endOffset="388"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.recyclerview"
                robot="true"
                replacement="libs.recyclerview"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1687"
                    endOffset="1729"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="62"
            column="20"
            startOffset="1687"
            endLine="62"
            endColumn="62"
            endOffset="1729"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/LoginFragment.kt"
            line="52"
            column="35"
            startOffset="1681"
            endLine="52"
            endColumn="39"
            endOffset="1685"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/LoginFragment.kt"
            line="53"
            column="35"
            startOffset="1722"
            endLine="53"
            endColumn="41"
            endOffset="1728"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;系统集成测试&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_test.xml"
            line="13"
            column="9"
            startOffset="448"
            endLine="13"
            endColumn="30"
            endOffset="469"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;正在运行系统测试...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_test.xml"
            line="22"
            column="9"
            startOffset="742"
            endLine="22"
            endColumn="35"
            endOffset="768"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;测试项目：&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_test.xml"
            line="35"
            column="9"
            startOffset="1161"
            endLine="35"
            endColumn="29"
            endOffset="1181"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;• 设备兼容性测试\n• API连接测试\n• 内存性能测试\n• 遥控器功能测试\n• UI响应时间测试&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_test.xml"
            line="43"
            column="9"
            startOffset="1421"
            endLine="43"
            endColumn="77"
            endOffset="1489"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请查看日志获取详细测试结果&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_test.xml"
            line="51"
            column="9"
            startOffset="1725"
            endLine="51"
            endColumn="37"
            endOffset="1753"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;App Logo&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
            line="15"
            column="9"
            startOffset="532"
            endLine="15"
            endColumn="46"
            endOffset="569"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;MovieTV&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
            line="20"
            column="9"
            startOffset="685"
            endLine="20"
            endColumn="31"
            endOffset="707"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;用户名&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
            line="31"
            column="9"
            startOffset="1053"
            endLine="31"
            endColumn="27"
            endOffset="1071"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;密码&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
            line="45"
            column="9"
            startOffset="1563"
            endLine="45"
            endColumn="26"
            endOffset="1580"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;登录&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
            line="66"
            column="13"
            startOffset="2304"
            endLine="66"
            endColumn="30"
            endOffset="2321"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;注册&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_login.xml"
            line="79"
            column="13"
            startOffset="2798"
            endLine="79"
            endColumn="30"
            endOffset="2815"/>
    </incident>

</incidents>
