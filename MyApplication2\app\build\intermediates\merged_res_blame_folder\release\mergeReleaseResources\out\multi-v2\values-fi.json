{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-fi/values-fi.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,401,506,611,723,5205", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "196,298,396,501,606,718,834,5301"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,502,630,744,844,935,1066,1193,1301,1425,1570,1699,1824,1933,2027,2153,2244,2358,2469,2581,2684,2804,2913,3040,3163,3270,3381,3497,3613,3724,3836,3923,4005,4103,4237,4382", "endColumns": "106,100,94,93,127,113,99,90,130,126,107,123,144,128,124,108,93,125,90,113,110,111,102,119,108,126,122,106,110,115,115,110,111,86,81,97,133,144,88", "endOffsets": "207,308,403,497,625,739,839,930,1061,1188,1296,1420,1565,1694,1819,1928,2022,2148,2239,2353,2464,2576,2679,2799,2908,3035,3158,3265,3376,3492,3608,3719,3831,3918,4000,4098,4232,4377,4466"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "839,946,1047,1142,1236,1364,1478,1578,1669,1800,1927,2035,2159,2304,2433,2558,2667,2761,2887,2978,3092,3203,3315,3418,3538,3647,3774,3897,4004,4115,4231,4347,4458,4570,4657,4739,4837,4971,5116", "endColumns": "106,100,94,93,127,113,99,90,130,126,107,123,144,128,124,108,93,125,90,113,110,111,102,119,108,126,122,106,110,115,115,110,111,86,81,97,133,144,88", "endOffsets": "941,1042,1137,1231,1359,1473,1573,1664,1795,1922,2030,2154,2299,2428,2553,2662,2756,2882,2973,3087,3198,3310,3413,3533,3642,3769,3892,3999,4110,4226,4342,4453,4565,4652,4734,4832,4966,5111,5200"}}]}]}