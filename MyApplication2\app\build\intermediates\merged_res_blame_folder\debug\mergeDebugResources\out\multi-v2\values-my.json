{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-29:/values-my/values-my.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,208,312,415,517,622,728,5358", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "203,307,410,512,617,723,842,5454"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,512,642,754,854,946,1070,1194,1308,1438,1586,1716,1843,1962,2056,2204,2297,2408,2526,2638,2741,2861,2977,3095,3211,3326,3443,3568,3700,3818,3943,4030,4117,4218,4360,4516", "endColumns": "106,100,99,98,129,111,99,91,123,123,113,129,147,129,126,118,93,147,92,110,117,111,102,119,115,117,115,114,116,124,131,117,124,86,86,100,141,155,99", "endOffsets": "207,308,408,507,637,749,849,941,1065,1189,1303,1433,1581,1711,1838,1957,2051,2199,2292,2403,2521,2633,2736,2856,2972,3090,3206,3321,3438,3563,3695,3813,3938,4025,4112,4213,4355,4511,4611"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "847,954,1055,1155,1254,1384,1496,1596,1688,1812,1936,2050,2180,2328,2458,2585,2704,2798,2946,3039,3150,3268,3380,3483,3603,3719,3837,3953,4068,4185,4310,4442,4560,4685,4772,4859,4960,5102,5258", "endColumns": "106,100,99,98,129,111,99,91,123,123,113,129,147,129,126,118,93,147,92,110,117,111,102,119,115,117,115,114,116,124,131,117,124,86,86,100,141,155,99", "endOffsets": "949,1050,1150,1249,1379,1491,1591,1683,1807,1931,2045,2175,2323,2453,2580,2699,2793,2941,3034,3145,3263,3375,3478,3598,3714,3832,3948,4063,4180,4305,4437,4555,4680,4767,4854,4955,5097,5253,5353"}}]}]}