<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/custom_tv_background"
    android:padding="32dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 页面标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="商品分类"
            android:textColor="@android:color/white"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="24dp" />

        <!-- 商品分类栏 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_product_categories"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <!-- 智能家居新品横幅 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cv_smart_home_banner"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:layout_marginBottom="32dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            android:focusable="true"
            android:clickable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/smart_home_gradient">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="32dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="智能家居新品"
                        android:textColor="@android:color/white"
                        android:textSize="28sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="让生活更智能，享受科技带来的便利"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:alpha="0.9"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="立即体验"
                        android:textColor="@android:color/white"
                        android:textSize="14sp"
                        android:background="@drawable/banner_button_background"
                        android:paddingHorizontal="16dp"
                        android:paddingVertical="8dp" />

                </LinearLayout>

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="32dp"
                    android:src="@drawable/ic_smart_home"
                    android:alpha="0.3" />

            </RelativeLayout>

        </androidx.cardview.widget.CardView>

        <!-- 热门商品标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="热门商品"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <!-- 热门商品网格 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_hot_products"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="4" />

    </LinearLayout>

</ScrollView>
