{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-bn/values-bn.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,408,511,612,714,5331", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "199,301,403,506,607,709,829,5427"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,509,631,741,840,937,1062,1186,1294,1430,1577,1705,1832,1941,2034,2160,2250,2364,2482,2594,2692,2814,2937,3048,3158,3271,3388,3524,3669,3803,3946,4033,4116,4220,4355,4506", "endColumns": "106,100,99,95,121,109,98,96,124,123,107,135,146,127,126,108,92,125,89,113,117,111,97,121,122,110,109,112,116,135,144,133,142,86,82,103,134,150,95", "endOffsets": "207,308,408,504,626,736,835,932,1057,1181,1289,1425,1572,1700,1827,1936,2029,2155,2245,2359,2477,2589,2687,2809,2932,3043,3153,3266,3383,3519,3664,3798,3941,4028,4111,4215,4350,4501,4597"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "834,941,1042,1142,1238,1360,1470,1569,1666,1791,1915,2023,2159,2306,2434,2561,2670,2763,2889,2979,3093,3211,3323,3421,3543,3666,3777,3887,4000,4117,4253,4398,4532,4675,4762,4845,4949,5084,5235", "endColumns": "106,100,99,95,121,109,98,96,124,123,107,135,146,127,126,108,92,125,89,113,117,111,97,121,122,110,109,112,116,135,144,133,142,86,82,103,134,150,95", "endOffsets": "936,1037,1137,1233,1355,1465,1564,1661,1786,1910,2018,2154,2301,2429,2556,2665,2758,2884,2974,3088,3206,3318,3416,3538,3661,3772,3882,3995,4112,4248,4393,4527,4670,4757,4840,4944,5079,5230,5326"}}]}]}