package com.example.myapplicationtv.utils

import android.content.Context
import android.content.SharedPreferences
import com.example.myapplicationtv.model.UserResponse
import com.google.gson.Gson

/**
 * 用户管理工具类
 */
object UserManager {
    
    private const val PREF_NAME = "user_prefs"
    private const val KEY_USER_INFO = "user_info"
    private const val KEY_TOKEN = "token"
    private const val KEY_IS_LOGGED_IN = "is_logged_in"
    
    private lateinit var sharedPreferences: SharedPreferences
    private val gson = Gson()
    
    /**
     * 初始化
     */
    fun init(context: Context) {
        sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 保存用户信息
     */
    fun saveUser(user: UserResponse) {
        val editor = sharedPreferences.edit()
        editor.putString(KEY_USER_INFO, gson.toJson(user))
        editor.putString(KEY_TOKEN, user.token)
        editor.putBoolean(KEY_IS_LOGGED_IN, true)
        editor.apply()
    }
    
    /**
     * 获取用户信息
     */
    fun getUser(): UserResponse? {
        val userJson = sharedPreferences.getString(KEY_USER_INFO, null)
        return if (userJson != null) {
            try {
                gson.fromJson(userJson, UserResponse::class.java)
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }
    
    /**
     * 获取Token
     */
    fun getToken(): String? {
        return sharedPreferences.getString(KEY_TOKEN, null)
    }
    
    /**
     * 检查是否已登录
     */
    fun isLoggedIn(): Boolean {
        return sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false) && getToken() != null
    }
    
    /**
     * 退出登录
     */
    fun logout() {
        val editor = sharedPreferences.edit()
        editor.remove(KEY_USER_INFO)
        editor.remove(KEY_TOKEN)
        editor.putBoolean(KEY_IS_LOGGED_IN, false)
        editor.apply()
    }
    
    /**
     * 获取用户ID
     */
    fun getUserId(): String? {
        return getUser()?.id
    }
    
    /**
     * 获取用户名
     */
    fun getUsername(): String? {
        return getUser()?.name
    }
    
    /**
     * 获取用户头像
     */
    fun getUserAvatar(): String? {
        return getUser()?.avatar
    }
    
    /**
     * 获取会员等级
     */
    fun getMemberLevel(): String? {
        return getUser()?.memberLevel
    }
}
