package com.example.myapplicationtv.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.R
import com.example.myapplicationtv.ContentItem

class HotGameAdapter(
    private val onItemClick: (ContentItem) -> Unit
) : RecyclerView.Adapter<HotGameAdapter.ViewHolder>() {

    private var games = listOf<ContentItem>()

    fun updateData(newGames: List<ContentItem>) {
        games = newGames
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_hot_game, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val game = games[position]
        holder.bind(game)
    }

    override fun getItemCount(): Int = games.size

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivGameIcon: ImageView = itemView.findViewById(R.id.iv_game_icon)
        private val tvGameName: TextView = itemView.findViewById(R.id.tv_game_name)
        private val tvRating: TextView = itemView.findViewById(R.id.tv_rating)

        fun bind(game: ContentItem) {
            tvGameName.text = game.title
            
            // 设置评分显示
            val rating = game.rating ?: 5.0f
            val stars = "★".repeat(rating.toInt()) + "☆".repeat(5 - rating.toInt())
            tvRating.text = stars
            
            // 设置游戏图标
            // TODO: 使用图片加载库加载真实图片
            ivGameIcon.setImageResource(R.drawable.ic_games)
            
            itemView.setOnClickListener {
                onItemClick(game)
            }
        }
    }
}
