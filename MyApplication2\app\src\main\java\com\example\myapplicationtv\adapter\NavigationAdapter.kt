package com.example.myapplicationtv.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.R
import com.example.myapplicationtv.model.NavigationItem

/**
 * 导航菜单适配器
 */
class NavigationAdapter(
    private val items: List<NavigationItem>,
    private val onItemClick: (NavigationItem) -> Unit
) : RecyclerView.Adapter<NavigationAdapter.NavigationViewHolder>() {

    private var selectedPosition = 0

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NavigationViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_navigation, parent, false)
        return NavigationViewHolder(view)
    }

    override fun onBindViewHolder(holder: NavigationViewHolder, position: Int) {
        val item = items[position]
        holder.bind(item, position == selectedPosition)
        
        holder.itemView.setOnClickListener {
            val oldPosition = selectedPosition
            selectedPosition = position
            notifyItemChanged(oldPosition)
            notifyItemChanged(selectedPosition)
            onItemClick(item)
        }
        
        holder.itemView.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                val oldPosition = selectedPosition
                selectedPosition = position
                notifyItemChanged(oldPosition)
                notifyItemChanged(selectedPosition)
            }
        }
    }

    override fun getItemCount(): Int = items.size

    class NavigationViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivIcon: ImageView = itemView.findViewById(R.id.iv_icon)
        private val tvTitle: TextView = itemView.findViewById(R.id.tv_title)

        fun bind(item: NavigationItem, isSelected: Boolean) {
            ivIcon.setImageResource(item.iconRes)
            tvTitle.text = item.title
            itemView.isSelected = isSelected
        }
    }
}
