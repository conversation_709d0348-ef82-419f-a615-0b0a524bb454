package com.example.myapplicationtv.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplicationtv.ContentItem
import com.example.myapplicationtv.model.*
import com.example.myapplicationtv.repository.ContentRepository
import kotlinx.coroutines.launch

/**
 * 主页ViewModel
 */
class MainViewModel : ViewModel() {
    
    private val repository = ContentRepository()
    
    // 推荐电影
    private val _recommendedMovies = MutableLiveData<List<ContentItem>>()
    val recommendedMovies: LiveData<List<ContentItem>> = _recommendedMovies
    
    // 热门电影
    private val _hotMovies = MutableLiveData<List<ContentItem>>()
    val hotMovies: LiveData<List<ContentItem>> = _hotMovies
    
    // 推荐应用
    private val _recommendedApps = MutableLiveData<List<ContentItem>>()
    val recommendedApps: LiveData<List<ContentItem>> = _recommendedApps
    
    // 推荐游戏
    private val _recommendedGames = MutableLiveData<List<ContentItem>>()
    val recommendedGames: LiveData<List<ContentItem>> = _recommendedGames
    
    // 精选游戏
    private val _featuredGames = MutableLiveData<List<ContentItem>>()
    val featuredGames: LiveData<List<ContentItem>> = _featuredGames
    
    // 推荐商品
    private val _recommendedProducts = MutableLiveData<List<ContentItem>>()
    val recommendedProducts: LiveData<List<ContentItem>> = _recommendedProducts
    
    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    // 错误信息
    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    
    init {
        loadAllContent()
    }
    
    /**
     * 加载所有内容
     */
    fun loadAllContent() {
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                // 并行加载所有内容
                loadRecommendedMovies()
                loadHotMovies()
                loadRecommendedApps()
                loadRecommendedGames()
                loadFeaturedGames()
                loadRecommendedProducts()
            } catch (e: Exception) {
                _error.value = "加载内容失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 加载推荐电影
     */
    private suspend fun loadRecommendedMovies() {
        repository.getRecommendedMovies(10).fold(
            onSuccess = { movies ->
                _recommendedMovies.value = movies.map { ContentItem.fromMovieResponse(it) }
            },
            onFailure = { e ->
                _error.value = "加载推荐电影失败: ${e.message}"
            }
        )
    }
    
    /**
     * 加载热门电影
     */
    private suspend fun loadHotMovies() {
        repository.getHotMovies(10).fold(
            onSuccess = { movies ->
                _hotMovies.value = movies.map { ContentItem.fromMovieResponse(it) }
            },
            onFailure = { e ->
                _error.value = "加载热门电影失败: ${e.message}"
            }
        )
    }
    
    /**
     * 加载推荐应用
     */
    private suspend fun loadRecommendedApps() {
        repository.getRecommendedApps(10).fold(
            onSuccess = { apps ->
                _recommendedApps.value = apps.map { ContentItem.fromAppResponse(it) }
            },
            onFailure = { e ->
                _error.value = "加载推荐应用失败: ${e.message}"
            }
        )
    }
    
    /**
     * 加载推荐游戏
     */
    private suspend fun loadRecommendedGames() {
        repository.getRecommendedGames(10).fold(
            onSuccess = { games ->
                _recommendedGames.value = games.map { ContentItem.fromGameResponse(it) }
            },
            onFailure = { e ->
                _error.value = "加载推荐游戏失败: ${e.message}"
            }
        )
    }
    
    /**
     * 加载精选游戏
     */
    private suspend fun loadFeaturedGames() {
        repository.getFeaturedGames(10).fold(
            onSuccess = { games ->
                _featuredGames.value = games.map { ContentItem.fromGameResponse(it) }
            },
            onFailure = { e ->
                _error.value = "加载精选游戏失败: ${e.message}"
            }
        )
    }
    
    /**
     * 加载推荐商品
     */
    private suspend fun loadRecommendedProducts() {
        repository.getRecommendedProducts(10).fold(
            onSuccess = { products ->
                _recommendedProducts.value = products.map { ContentItem.fromProductResponse(it) }
            },
            onFailure = { e ->
                _error.value = "加载推荐商品失败: ${e.message}"
            }
        )
    }
    
    /**
     * 刷新数据
     */
    fun refresh() {
        loadAllContent()
    }
}
