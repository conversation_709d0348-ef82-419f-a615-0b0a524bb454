package com.example.houduan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户观看历史实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "user_history")
public class UserHistory {

    @Id
    @Column(length = 32)
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 电影ID
     */
    private String movieId;

    /**
     * 章节ID
     */
    private String chapterId;

    /**
     * 播放进度(秒)
     */
    private Integer progress;

    /**
     * 总时长(秒)
     */
    private Integer duration;

    /**
     * 观看时间
     */
    private LocalDateTime watchTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
