package com.example.houduan.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 应用响应DTO
 */
@Data
public class AppResponse {

    /**
     * 应用ID
     */
    private String id;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 应用图标URL
     */
    private String icon;

    /**
     * 应用分类
     */
    private String category;

    /**
     * 版本号
     */
    private String version;

    /**
     * 应用大小(字节)
     */
    private Long size;

    /**
     * 格式化的应用大小
     */
    private String sizeFormatted;

    /**
     * 评分(0.0-10.0)
     */
    private BigDecimal rating;

    /**
     * 评分人数
     */
    private Integer ratingCount;

    /**
     * 下载次数
     */
    private Long downloadCount;

    /**
     * 格式化的下载次数
     */
    private String downloadCountFormatted;

    /**
     * 应用描述
     */
    private String description;

    /**
     * 下载地址
     */
    private String downloadUrl;

    /**
     * 截图URLs
     */
    private List<String> screenshots;

    /**
     * 是否推荐
     */
    private Boolean isRecommended;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
