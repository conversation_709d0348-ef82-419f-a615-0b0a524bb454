{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-kn/values-kn.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,512,636,750,847,942,1085,1224,1333,1458,1601,1737,1869,1980,2071,2207,2296,2409,2526,2637,2731,2842,2961,3089,3213,3320,3431,3546,3664,3776,3891,3978,4062,4162,4297,4454", "endColumns": "106,100,99,98,123,113,96,94,142,138,108,124,142,135,131,110,90,135,88,112,116,110,93,110,118,127,123,106,110,114,117,111,114,86,83,99,134,156,90", "endOffsets": "207,308,408,507,631,745,842,937,1080,1219,1328,1453,1596,1732,1864,1975,2066,2202,2291,2404,2521,2632,2726,2837,2956,3084,3208,3315,3426,3541,3659,3771,3886,3973,4057,4157,4292,4449,4540"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "850,957,1058,1158,1257,1381,1495,1592,1687,1830,1969,2078,2203,2346,2482,2614,2725,2816,2952,3041,3154,3271,3382,3476,3587,3706,3834,3958,4065,4176,4291,4409,4521,4636,4723,4807,4907,5042,5199", "endColumns": "106,100,99,98,123,113,96,94,142,138,108,124,142,135,131,110,90,135,88,112,116,110,93,110,118,127,123,106,110,114,117,111,114,86,83,99,134,156,90", "endOffsets": "952,1053,1153,1252,1376,1490,1587,1682,1825,1964,2073,2198,2341,2477,2609,2720,2811,2947,3036,3149,3266,3377,3471,3582,3701,3829,3953,4060,4171,4286,4404,4516,4631,4718,4802,4902,5037,5194,5285"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,407,513,614,722,5290", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "198,301,402,508,609,717,845,5386"}}]}]}