# 数据库设计文档

## 数据库概述

本项目使用MySQL 8.0+作为数据库，数据库名称为`movietv`，字符集为`utf8mb4`。

## 数据库连接信息

- **数据库名**: movietv
- **用户名**: root
- **密码**: 123456
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci

## 表结构说明

### 核心业务表

1. **user** - 用户表
   - 存储用户基本信息、会员等级、状态等
   - 支持用户名、手机号、邮箱登录

2. **movie** - 电影表
   - 存储影视内容信息，包括电影、电视剧、综艺、动漫
   - 包含评分、播放次数、收藏次数等统计信息

3. **movie_chapter** - 电影章节表
   - 用于电视剧分集管理
   - 与movie表关联，支持多集内容

4. **user_favorite** - 用户收藏表
   - 记录用户收藏的影视内容
   - 用户与电影的多对多关系

5. **user_history** - 用户观看历史表
   - 记录用户观看进度和历史
   - 支持断点续播功能

6. **user_rating** - 用户评分表
   - 用户对影视内容的评分和评论
   - 用于计算平均评分

### 扩展功能表

7. **application** - 应用表
   - TV端应用商店的应用信息

8. **game** - 游戏表
   - TV端游戏中心的游戏信息

9. **product** - 商品表
   - 商城模块的商品信息

10. **order_info** - 订单表
    - 用户购买商品的订单信息

11. **order_item** - 订单详情表
    - 订单中的具体商品信息

### 系统管理表

12. **banner** - Banner表
    - 首页和各模块的轮播图管理

13. **category** - 分类表
    - 统一的分类管理，支持多种类型

14. **system_config** - 系统配置表
    - 系统参数配置

15. **admin** - 管理员表
    - 后台管理系统的管理员账号

16. **operation_log** - 操作日志表
    - 记录用户和管理员的操作日志

## 数据库初始化

### 1. 创建数据库

```bash
# 连接MySQL
mysql -u root -p

# 执行创建脚本
source /path/to/create_database.sql
```

### 2. 验证安装

```sql
-- 查看数据库
SHOW DATABASES;

-- 使用数据库
USE movietv;

-- 查看表结构
SHOW TABLES;

-- 查看初始数据
SELECT * FROM admin;
SELECT * FROM category WHERE type = 'MOVIE';
```

### 3. 默认账号

**管理员账号:**
- 用户名: `admin`
- 密码: `admin123`
- 角色: 超级管理员

## 索引设计

### 主要索引

1. **用户相关索引**
   - `uk_username` - 用户名唯一索引
   - `uk_phone` - 手机号唯一索引
   - `uk_email` - 邮箱唯一索引

2. **电影相关索引**
   - `idx_type` - 类型索引
   - `idx_category` - 分类索引
   - `idx_rating` - 评分索引
   - `idx_view_count` - 播放次数索引

3. **用户行为索引**
   - `uk_user_movie` - 用户电影唯一索引（收藏、评分）
   - `idx_watch_time` - 观看时间索引

## 视图说明

### v_movie_stats - 电影统计视图
提供电影的完整统计信息，包括实际收藏数、评分数、平均评分等。

### v_user_stats - 用户统计视图
提供用户的行为统计，包括收藏数、观看历史数、评分数等。

## 数据库维护

### 定期维护任务

1. **清理过期数据**
   - 清理过期的操作日志
   - 清理无效的观看历史

2. **统计数据更新**
   - 更新电影的播放次数
   - 更新用户的统计信息

3. **索引优化**
   - 定期分析表结构
   - 优化慢查询

### 备份策略

建议每日备份数据库，保留最近30天的备份文件。

```bash
# 备份命令示例
mysqldump -u root -p movietv > movietv_backup_$(date +%Y%m%d).sql
```

## 注意事项

1. 所有ID字段使用VARCHAR(32)，建议使用UUID
2. 时间字段统一使用DATETIME类型
3. 状态字段使用TINYINT，0表示禁用，1表示启用
4. 价格字段使用DECIMAL(10,2)确保精度
5. 文本内容使用TEXT类型，支持大容量存储
6. 外键约束确保数据一致性，删除时级联删除相关数据
