{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,298,392,486,579,673,4672", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "192,293,387,481,574,668,764,4768"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,317,409,499,612,713,807,896,1006,1115,1210,1322,1424,1532,1639,1736,1824,1930,2017,2114,2211,2308,2397,2504,2597,2699,2800,2895,2994,3092,3196,3292,3394,3481,3561,3654,3784,3927", "endColumns": "108,102,91,89,112,100,93,88,109,108,94,111,101,107,106,96,87,105,86,96,96,96,88,106,92,101,100,94,98,97,103,95,101,86,79,92,129,142,80", "endOffsets": "209,312,404,494,607,708,802,891,1001,1110,1205,1317,1419,1527,1634,1731,1819,1925,2012,2109,2206,2303,2392,2499,2592,2694,2795,2890,2989,3087,3191,3287,3389,3476,3556,3649,3779,3922,4003"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "769,878,981,1073,1163,1276,1377,1471,1560,1670,1779,1874,1986,2088,2196,2303,2400,2488,2594,2681,2778,2875,2972,3061,3168,3261,3363,3464,3559,3658,3756,3860,3956,4058,4145,4225,4318,4448,4591", "endColumns": "108,102,91,89,112,100,93,88,109,108,94,111,101,107,106,96,87,105,86,96,96,96,88,106,92,101,100,94,98,97,103,95,101,86,79,92,129,142,80", "endOffsets": "873,976,1068,1158,1271,1372,1466,1555,1665,1774,1869,1981,2083,2191,2298,2395,2483,2589,2676,2773,2870,2967,3056,3163,3256,3358,3459,3554,3653,3751,3855,3951,4053,4140,4220,4313,4443,4586,4667"}}]}]}