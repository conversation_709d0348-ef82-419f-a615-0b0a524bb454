<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.10.1">

    <issue
        id="MissingPermission"
        severity="Error"
        message="Missing permissions required by ConnectivityManager.getActiveNetwork: android.permission.ACCESS_NETWORK_STATE"
        category="Correctness"
        priority="9"
        summary="Missing Permissions"
        explanation="This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.&#xA;&#xA;Furthermore, for permissions that are revocable (with `targetSdkVersion` 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime."
        errorLine1="            val network = connectivityManager.activeNetwork"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\utils\TestHelper.kt"
            line="60"
            column="27"/>
    </issue>

    <issue
        id="MissingPermission"
        severity="Error"
        message="Missing permissions required by ConnectivityManager.getNetworkCapabilities: android.permission.ACCESS_NETWORK_STATE"
        category="Correctness"
        priority="9"
        summary="Missing Permissions"
        explanation="This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.&#xA;&#xA;Furthermore, for permissions that are revocable (with `targetSdkVersion` 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime."
        errorLine1="            val capabilities = connectivityManager.getNetworkCapabilities(network)"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\utils\TestHelper.kt"
            line="61"
            column="32"/>
    </issue>

    <issue
        id="MissingPermission"
        severity="Error"
        message="Missing permissions required by ConnectivityManager.getActiveNetworkInfo: android.permission.ACCESS_NETWORK_STATE"
        category="Correctness"
        priority="9"
        summary="Missing Permissions"
        explanation="This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.&#xA;&#xA;Furthermore, for permissions that are revocable (with `targetSdkVersion` 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime."
        errorLine1="            val networkInfo = connectivityManager.activeNetworkInfo"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\utils\TestHelper.kt"
            line="71"
            column="31"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return String.format(&quot;%.2f %s&quot;, size, units[unitIndex])"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\utils\TestHelper.kt"
            line="115"
            column="16"/>
    </issue>

    <issue
        id="NotificationPermission"
        severity="Error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission (usage from com.bumptech.glide.request.target.NotificationTarget)"
        category="Correctness"
        priority="6"
        summary="Notifications Without Permission"
        explanation="When targeting Android 13 and higher, posting permissions requires holding the runtime permission `android.permission.POST_NOTIFICATIONS`.">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml"/>
    </issue>

    <issue
        id="UnusedAttribute"
        severity="Warning"
        message="Attribute `usesCleartextTraffic` is only used in API level 23 and higher (current min is 21)"
        category="Correctness"
        priority="6"
        summary="Attribute unused on older versions"
        explanation="This check finds attributes set in XML files that were introduced in a version newer than the oldest version targeted by your application (with the `minSdkVersion` attribute).&#xA;&#xA;This is not an error; the application will simply ignore the attribute. However, if the attribute is important to the appearance or functionality of your application, you should consider finding an alternative way to achieve the same result with only available attributes, and then you can optionally create a copy of the layout in a layout-vNN folder which will be used on API NN or higher where you can take advantage of the newer attribute.&#xA;&#xA;Note: This check does not only apply to attributes. For example, some tags can be unused too, such as the new `&lt;tag>` element in layouts introduced in API 21."
        errorLine1="        android:usesCleartextTraffic=&quot;true&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml"
            line="20"
            column="9"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="            ContextCompat.getDrawable(activity!!, androidx.leanback.R.drawable.lb_ic_sad_cloud)"
        errorLine2="                                      ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\ErrorFragment.kt"
            line="21"
            column="39"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="            ContextCompat.getDrawable(activity!!, androidx.leanback.R.drawable.lb_ic_sad_cloud)"
        errorLine2="                                      ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\ErrorFragment.kt"
            line="21"
            column="39"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireFragmentManager() instead of fragmentManager!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="            fragmentManager!!.beginTransaction().remove(this@ErrorFragment).commit()"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\ErrorFragment.kt"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireFragmentManager() instead of fragmentManager!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="            fragmentManager!!.beginTransaction().remove(this@ErrorFragment).commit()"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\ErrorFragment.kt"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        mBackgroundManager.attach(activity!!.window)"
        errorLine2="                                  ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="76"
            column="35"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        mDefaultBackground = ContextCompat.getDrawable(activity!!, R.drawable.default_background)"
        errorLine2="                                                       ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="77"
            column="56"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        activity!!.windowManager.defaultDisplay.getMetrics(mMetrics)"
        errorLine2="        ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="79"
            column="9"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        brandColor = ContextCompat.getColor(activity!!, R.color.fastlane_background)"
        errorLine2="                                            ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="89"
            column="45"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        brandColor = ContextCompat.getColor(activity!!, R.color.fastlane_background)"
        errorLine2="                                            ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="89"
            column="45"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        searchAffordanceColor = ContextCompat.getColor(activity!!, R.color.search_opaque)"
        errorLine2="                                                       ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="91"
            column="56"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        searchAffordanceColor = ContextCompat.getColor(activity!!, R.color.search_opaque)"
        errorLine2="                                                       ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="91"
            column="56"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="            val intent = Intent(activity!!, SearchActivity::class.java)"
        errorLine2="                                ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="221"
            column="33"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        Glide.with(activity!!)"
        errorLine2="                   ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="310"
            column="20"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        mSelectedContentItem = activity!!.intent.getSerializableExtra(DetailsActivity.CONTENT_ITEM) as? ContentItem"
        errorLine2="                               ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="57"
            column="32"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="            mSelectedMovie = activity!!.intent.getSerializableExtra(DetailsActivity.MOVIE) as? Movie"
        errorLine2="                             ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="61"
            column="30"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="            val intent = Intent(activity!!, MainActivity::class.java)"
        errorLine2="                                ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="74"
            column="33"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        Glide.with(activity!!)"
        errorLine2="                   ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="83"
            column="20"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        row.imageDrawable = ContextCompat.getDrawable(activity!!, R.drawable.default_background)"
        errorLine2="                                                      ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="104"
            column="55"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        row.imageDrawable = ContextCompat.getDrawable(activity!!, R.drawable.default_background)"
        errorLine2="                                                      ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="104"
            column="55"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        val width = convertDpToPixel(activity!!, DETAIL_THUMB_WIDTH)"
        errorLine2="                                     ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="105"
            column="38"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        val height = convertDpToPixel(activity!!, DETAIL_THUMB_HEIGHT)"
        errorLine2="                                      ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="106"
            column="39"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="        Glide.with(activity!!)"
        errorLine2="                   ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="110"
            column="20"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="            ContextCompat.getColor(activity!!, R.color.selected_background)"
        errorLine2="                                   ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="234"
            column="36"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="            ContextCompat.getColor(activity!!, R.color.selected_background)"
        errorLine2="                                   ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="234"
            column="36"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="                    val intent = Intent(activity!!, PlaybackActivity::class.java)"
        errorLine2="                                        ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="247"
            column="41"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="                    val intent = Intent(activity!!, PlaybackActivity::class.java)"
        errorLine2="                                        ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="247"
            column="41"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="                    Toast.makeText(activity!!, &quot;启动游戏: ${mSelectedContentItem?.title}&quot;, Toast.LENGTH_SHORT).show()"
        errorLine2="                                   ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="252"
            column="36"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="                    Toast.makeText(activity!!, &quot;启动游戏: ${mSelectedContentItem?.title}&quot;, Toast.LENGTH_SHORT).show()"
        errorLine2="                                   ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="252"
            column="36"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="                    Toast.makeText(activity!!, &quot;开始下载: ${mSelectedContentItem?.title}&quot;, Toast.LENGTH_SHORT).show()"
        errorLine2="                                   ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="255"
            column="36"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="                    Toast.makeText(activity!!, &quot;开始下载: ${mSelectedContentItem?.title}&quot;, Toast.LENGTH_SHORT).show()"
        errorLine2="                                   ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="255"
            column="36"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="                    Toast.makeText(activity!!, &quot;已添加到收藏: ${mSelectedContentItem?.title}&quot;, Toast.LENGTH_SHORT).show()"
        errorLine2="                                   ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="258"
            column="36"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="                    Toast.makeText(activity!!, &quot;已添加到收藏: ${mSelectedContentItem?.title}&quot;, Toast.LENGTH_SHORT).show()"
        errorLine2="                                   ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="258"
            column="36"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="                    Toast.makeText(activity!!, action.toString(), Toast.LENGTH_SHORT).show()"
        errorLine2="                                   ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="261"
            column="36"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        severity="Error"
        message="Use requireActivity() instead of activity!!"
        category="Correctness"
        priority="6"
        summary="Use the &apos;require_____()&apos; API rather than &apos;get____()&apos; API for more descriptive error messages when it&apos;s null."
        explanation="AndroidX added new &quot;require____()&quot; versions of common &quot;get___()&quot; APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message."
        errorLine1="                    Toast.makeText(activity!!, action.toString(), Toast.LENGTH_SHORT).show()"
        errorLine2="                                   ~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt"
            line="261"
            column="36"/>
    </issue>

    <issue
        id="FragmentLiveDataObserve"
        severity="Error"
        message="Use viewLifecycleOwner as the LifecycleOwner."
        category="Correctness"
        priority="5"
        summary="Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object."
        explanation="When observing a LiveData object from a fragment&apos;s onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment&apos;s view is active."
        errorLine1="        viewModel.recommendedMovies.observe(this, Observer { movies ->"
        errorLine2="                                            ~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="109"
            column="45"/>
    </issue>

    <issue
        id="FragmentLiveDataObserve"
        severity="Error"
        message="Use viewLifecycleOwner as the LifecycleOwner."
        category="Correctness"
        priority="5"
        summary="Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object."
        explanation="When observing a LiveData object from a fragment&apos;s onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment&apos;s view is active."
        errorLine1="        viewModel.hotMovies.observe(this, Observer { movies ->"
        errorLine2="                                    ~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="121"
            column="37"/>
    </issue>

    <issue
        id="FragmentLiveDataObserve"
        severity="Error"
        message="Use viewLifecycleOwner as the LifecycleOwner."
        category="Correctness"
        priority="5"
        summary="Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object."
        explanation="When observing a LiveData object from a fragment&apos;s onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment&apos;s view is active."
        errorLine1="        viewModel.recommendedApps.observe(this, Observer { apps ->"
        errorLine2="                                          ~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="133"
            column="43"/>
    </issue>

    <issue
        id="FragmentLiveDataObserve"
        severity="Error"
        message="Use viewLifecycleOwner as the LifecycleOwner."
        category="Correctness"
        priority="5"
        summary="Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object."
        explanation="When observing a LiveData object from a fragment&apos;s onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment&apos;s view is active."
        errorLine1="        viewModel.recommendedGames.observe(this, Observer { games ->"
        errorLine2="                                           ~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="145"
            column="44"/>
    </issue>

    <issue
        id="FragmentLiveDataObserve"
        severity="Error"
        message="Use viewLifecycleOwner as the LifecycleOwner."
        category="Correctness"
        priority="5"
        summary="Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object."
        explanation="When observing a LiveData object from a fragment&apos;s onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment&apos;s view is active."
        errorLine1="        viewModel.featuredGames.observe(this, Observer { games ->"
        errorLine2="                                        ~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="157"
            column="41"/>
    </issue>

    <issue
        id="FragmentLiveDataObserve"
        severity="Error"
        message="Use viewLifecycleOwner as the LifecycleOwner."
        category="Correctness"
        priority="5"
        summary="Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object."
        explanation="When observing a LiveData object from a fragment&apos;s onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment&apos;s view is active."
        errorLine1="        viewModel.recommendedProducts.observe(this, Observer { products ->"
        errorLine2="                                              ~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="169"
            column="47"/>
    </issue>

    <issue
        id="FragmentLiveDataObserve"
        severity="Error"
        message="Use viewLifecycleOwner as the LifecycleOwner."
        category="Correctness"
        priority="5"
        summary="Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object."
        explanation="When observing a LiveData object from a fragment&apos;s onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment&apos;s view is active."
        errorLine1="        viewModel.isLoading.observe(this, Observer { isLoading ->"
        errorLine2="                                    ~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="184"
            column="37"/>
    </issue>

    <issue
        id="FragmentLiveDataObserve"
        severity="Error"
        message="Use viewLifecycleOwner as the LifecycleOwner."
        category="Correctness"
        priority="5"
        summary="Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object."
        explanation="When observing a LiveData object from a fragment&apos;s onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment&apos;s view is active."
        errorLine1="        viewModel.error.observe(this, Observer { error ->"
        errorLine2="                                ~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt"
            line="190"
            column="33"/>
    </issue>

    <issue
        id="FragmentLiveDataObserve"
        severity="Error"
        message="Use viewLifecycleOwner as the LifecycleOwner."
        category="Correctness"
        priority="5"
        summary="Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object."
        explanation="When observing a LiveData object from a fragment&apos;s onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment&apos;s view is active."
        errorLine1="        viewModel.favorites.observe(this, Observer { favorites ->"
        errorLine2="                                    ~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\UserCenterFragment.kt"
            line="48"
            column="37"/>
    </issue>

    <issue
        id="FragmentLiveDataObserve"
        severity="Error"
        message="Use viewLifecycleOwner as the LifecycleOwner."
        category="Correctness"
        priority="5"
        summary="Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object."
        explanation="When observing a LiveData object from a fragment&apos;s onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment&apos;s view is active."
        errorLine1="        viewModel.history.observe(this, Observer { history ->"
        errorLine2="                                  ~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\UserCenterFragment.kt"
            line="60"
            column="35"/>
    </issue>

    <issue
        id="FragmentLiveDataObserve"
        severity="Error"
        message="Use viewLifecycleOwner as the LifecycleOwner."
        category="Correctness"
        priority="5"
        summary="Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object."
        explanation="When observing a LiveData object from a fragment&apos;s onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment&apos;s view is active."
        errorLine1="        viewModel.isLoading.observe(this, Observer { isLoading ->"
        errorLine2="                                    ~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\UserCenterFragment.kt"
            line="75"
            column="37"/>
    </issue>

    <issue
        id="FragmentLiveDataObserve"
        severity="Error"
        message="Use viewLifecycleOwner as the LifecycleOwner."
        category="Correctness"
        priority="5"
        summary="Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object."
        explanation="When observing a LiveData object from a fragment&apos;s onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment&apos;s view is active."
        errorLine1="        viewModel.error.observe(this, Observer { error ->"
        errorLine2="                                ~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\UserCenterFragment.kt"
            line="80"
            column="33"/>
    </issue>

    <issue
        id="FragmentTagUsage"
        severity="Warning"
        message="Replace the &lt;fragment> tag with FragmentContainerView."
        category="Correctness"
        priority="5"
        summary="Use FragmentContainerView instead of the &lt;fragment> tag"
        explanation="FragmentContainerView replaces the &lt;fragment> tag as the preferred                 way of adding fragments via XML. Unlike the &lt;fragment> tag, FragmentContainerView                 uses a normal `FragmentTransaction` under the hood to add the initial fragment,                 allowing further FragmentTransaction operations on the FragmentContainerView                 and providing a consistent timing for lifecycle events."
        url="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html"
        urls="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html"
        errorLine1="&lt;fragment xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2=" ~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_login.xml"
            line="2"
            column="2"/>
    </issue>

    <issue
        id="FragmentTagUsage"
        severity="Warning"
        message="Replace the &lt;fragment> tag with FragmentContainerView."
        category="Correctness"
        priority="5"
        summary="Use FragmentContainerView instead of the &lt;fragment> tag"
        explanation="FragmentContainerView replaces the &lt;fragment> tag as the preferred                 way of adding fragments via XML. Unlike the &lt;fragment> tag, FragmentContainerView                 uses a normal `FragmentTransaction` under the hood to add the initial fragment,                 allowing further FragmentTransaction operations on the FragmentContainerView                 and providing a consistent timing for lifecycle events."
        url="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html"
        urls="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html"
        errorLine1="&lt;fragment xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2=" ~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_search.xml"
            line="2"
            column="2"/>
    </issue>

    <issue
        id="FragmentTagUsage"
        severity="Warning"
        message="Replace the &lt;fragment> tag with FragmentContainerView."
        category="Correctness"
        priority="5"
        summary="Use FragmentContainerView instead of the &lt;fragment> tag"
        explanation="FragmentContainerView replaces the &lt;fragment> tag as the preferred                 way of adding fragments via XML. Unlike the &lt;fragment> tag, FragmentContainerView                 uses a normal `FragmentTransaction` under the hood to add the initial fragment,                 allowing further FragmentTransaction operations on the FragmentContainerView                 and providing a consistent timing for lifecycle events."
        url="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html"
        urls="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html"
        errorLine1="&lt;fragment xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2=" ~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_user_center.xml"
            line="2"
            column="2"/>
    </issue>

    <issue
        id="RedundantLabel"
        severity="Warning"
        message="Redundant label can be removed"
        category="Correctness"
        priority="5"
        summary="Redundant label on activity"
        explanation="When an activity does not have a label attribute, it will use the one from the application tag. Since the application has already specified the same label, the label on this activity can be omitted."
        errorLine1="            android:label=&quot;@string/app_name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation(&quot;com.google.code.gson:gson:2.10.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts"
            line="47"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.1.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="kotlin = &quot;2.0.21&quot;"
        errorLine2="         ~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\gradle\libs.versions.toml"
            line="3"
            column="10"/>
    </issue>

    <issue
        id="DiscouragedApi"
        severity="Warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed."
        category="Correctness"
        priority="2"
        summary="Using discouraged APIs"
        explanation="Discouraged APIs are allowed and are not deprecated, but they may be unfit for common use (e.g. due to slow performance or subtle behavior)."
        errorLine1="            android:screenOrientation=&quot;landscape&quot;>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml"
            line="29"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        severity="Warning"
        message="Do not place Android context classes in static fields (static reference to `ApiClient` which has field `context` pointing to `Context`); this is a memory leak"
        category="Performance"
        priority="6"
        summary="Static Field Leaks"
        explanation="A static field will leak contexts.&#xA;&#xA;Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a `Fragment` or `Activity`, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.&#xA;&#xA;Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.&#xA;&#xA;ViewModel classes should never point to Views or non-application Contexts."
        errorLine1="/**"
        errorLine2="^">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\network\ApiClient.kt"
            line="13"
            column="1"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        severity="Warning"
        message="Do not place Android context classes in static fields; this is a memory leak"
        category="Performance"
        priority="6"
        summary="Static Field Leaks"
        explanation="A static field will leak contexts.&#xA;&#xA;Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a `Fragment` or `Activity`, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.&#xA;&#xA;Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.&#xA;&#xA;ViewModel classes should never point to Views or non-application Contexts."
        errorLine1="    private var context: Context? = null"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\network\ApiClient.kt"
            line="22"
            column="5"/>
    </issue>

    <issue
        id="MergeRootFrame"
        severity="Warning"
        message="This `&lt;FrameLayout>` can be replaced with a `&lt;merge>` tag"
        category="Performance"
        priority="4"
        summary="FrameLayout can be replaced with `&lt;merge>` tag"
        explanation="If a `&lt;FrameLayout>` is the root of a layout and does not provide background or padding etc, it can often be replaced with a `&lt;merge>` tag which is slightly more efficient. Note that this depends on context, so make sure you understand how the `&lt;merge>` tag works before proceeding."
        url="https://android-developers.googleblog.com/2009/03/android-layout-tricks-3-optimize-by.html"
        urls="https://android-developers.googleblog.com/2009/03/android-layout-tricks-3-optimize-by.html"
        errorLine1="&lt;FrameLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_details.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/default_background` with a theme that also paints a background (inferred theme is `@style/Theme.MyApplicationTV`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/default_background&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/default_background` with a theme that also paints a background (inferred theme is `@style/Theme.MyApplicationTV`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/default_background&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="IconLauncherShape"
        severity="Warning"
        message="Launcher icons should not fill every pixel of their square region; see the design guide for details"
        category="Usability:Icons"
        priority="6"
        summary="The launcher icon shape should use a distinct silhouette"
        explanation="According to the Android Design Guide (https://d.android.com/r/studio-ui/designer/material/iconography) your launcher icons should &quot;use a distinct silhouette&quot;, a &quot;three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth.&quot;&#xA;&#xA;The unique silhouette implies that your launcher icon should not be a filled square.">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\app_icon_your_company.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/app_icon_your_company.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\app_icon_your_company.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/movie.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\movie.png"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="        &lt;Button"
        errorLine2="         ~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml"
            line="60"
            column="10"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="        &lt;Button"
        errorLine2="         ~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml"
            line="73"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml"
            line="26"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml"
            line="40"
            column="6"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="            prefs.edit().putString(&quot;auth_token&quot;, token).apply()"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\network\ApiClient.kt"
            line="84"
            column="13"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="            prefs.edit().remove(&quot;auth_token&quot;).apply()"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\network\ApiClient.kt"
            line="91"
            column="13"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `String.toUri` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="            playerAdapter.setDataSource(Uri.parse(videoUrl))"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\PlaybackVideoFragment.kt"
            line="100"
            column="41"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        val editor = sharedPreferences.edit()"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\utils\UserManager.kt"
            line="32"
            column="22"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        val editor = sharedPreferences.edit()"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\utils\UserManager.kt"
            line="73"
            column="22"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;com.squareup.retrofit2:retrofit:2.9.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts"
            line="44"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;com.squareup.retrofit2:converter-gson:2.9.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts"
            line="45"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;com.squareup.okhttp3:logging-interceptor:4.11.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts"
            line="46"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;com.google.code.gson:gson:2.10.1&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts"
            line="47"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts"
            line="50"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts"
            line="51"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts"
            line="54"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-livedata-ktx:2.7.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts"
            line="55"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-runtime-ktx:2.7.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts"
            line="56"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;androidx.fragment:fragment-ktx:1.6.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts"
            line="59"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;androidx.recyclerview:recyclerview:1.3.2&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts"
            line="62"
            column="20"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        usernameEditText.setText(&quot;test&quot;)"
        errorLine2="                                  ~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\LoginFragment.kt"
            line="52"
            column="35"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        passwordEditText.setText(&quot;123456&quot;)"
        errorLine2="                                  ~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\LoginFragment.kt"
            line="53"
            column="35"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;系统集成测试&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;系统集成测试&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml"
            line="13"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;正在运行系统测试...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;正在运行系统测试...&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml"
            line="22"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;测试项目：&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;测试项目：&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml"
            line="35"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;• 设备兼容性测试\n• API连接测试\n• 内存性能测试\n• 遥控器功能测试\n• UI响应时间测试&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;• 设备兼容性测试\n• API连接测试\n• 内存性能测试\n• 遥控器功能测试\n• UI响应时间测试&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml"
            line="43"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;请查看日志获取详细测试结果&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;请查看日志获取详细测试结果&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml"
            line="51"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;App Logo&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:contentDescription=&quot;App Logo&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml"
            line="15"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;MovieTV&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;MovieTV&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml"
            line="20"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;用户名&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:hint=&quot;用户名&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml"
            line="31"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;密码&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:hint=&quot;密码&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml"
            line="45"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;登录&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;登录&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml"
            line="66"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;注册&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;注册&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml"
            line="79"
            column="13"/>
    </issue>

</issues>
