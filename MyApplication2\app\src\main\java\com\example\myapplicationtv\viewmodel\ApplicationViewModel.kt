package com.example.myapplicationtv.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.example.myapplicationtv.ContentItem
import com.example.myapplicationtv.model.AppCategory
import com.example.myapplicationtv.model.ContentType

/**
 * 应用页面ViewModel
 */
class ApplicationViewModel : ViewModel() {

    private val _appCategories = MutableLiveData<List<AppCategory>>()
    val appCategories: LiveData<List<AppCategory>> = _appCategories

    private val _recommendedApps = MutableLiveData<List<ContentItem>>()
    val recommendedApps: LiveData<List<ContentItem>> = _recommendedApps

    init {
        loadAppCategories()
        loadRecommendedApps()
    }

    private fun loadAppCategories() {
        // 模拟应用分类数据
        val categories = listOf(
            AppCategory(
                id = 1,
                name = "影音娱乐",
                apps = listOf(
                    ContentItem(
                        id = "app_1",
                        title = "爱奇艺",
                        description = "海量影视内容",
                        imageUrl = "https://example.com/app1.jpg",
                        type = ContentType.APP,
                        rating = java.math.BigDecimal.valueOf(4.8)
                    ),
                    ContentItem(
                        id = "app_2",
                        title = "腾讯视频",
                        description = "精彩视频内容",
                        imageUrl = "https://example.com/app2.jpg",
                        type = ContentType.APP,
                        rating = java.math.BigDecimal.valueOf(4.6)
                    )
                )
            ),
            AppCategory(
                id = 2,
                name = "生活服务",
                apps = listOf(
                    ContentItem(
                        id = "app_3",
                        title = "美团",
                        description = "生活服务平台",
                        imageUrl = "https://example.com/app3.jpg",
                        type = ContentType.APP,
                        rating = java.math.BigDecimal.valueOf(4.9)
                    )
                )
            ),
            AppCategory(
                id = 3,
                name = "教育学习",
                apps = listOf(
                    ContentItem(
                        id = "app_4",
                        title = "学而思网校",
                        description = "在线教育平台",
                        imageUrl = "https://example.com/app4.jpg",
                        type = ContentType.APP,
                        rating = java.math.BigDecimal.valueOf(4.7)
                    )
                )
            ),
            AppCategory(
                id = 4,
                name = "工具效率",
                apps = listOf(
                    ContentItem(
                        id = "app_5",
                        title = "WPS Office",
                        description = "办公软件套件",
                        imageUrl = "https://example.com/app5.jpg",
                        type = ContentType.APP,
                        rating = java.math.BigDecimal.valueOf(4.5)
                    )
                )
            ),
            AppCategory(
                id = 5,
                name = "社交通讯",
                apps = listOf(
                    ContentItem(
                        id = "app_6",
                        title = "微信",
                        description = "社交通讯应用",
                        imageUrl = "https://example.com/app6.jpg",
                        type = ContentType.APP,
                        rating = java.math.BigDecimal.valueOf(4.4)
                    )
                )
            ),
            AppCategory(
                id = 6,
                name = "新闻资讯",
                apps = listOf(
                    ContentItem(
                        id = "app_7",
                        title = "今日头条",
                        description = "个性化资讯推荐",
                        imageUrl = "https://example.com/app7.jpg",
                        type = ContentType.APP,
                        rating = java.math.BigDecimal.valueOf(4.3)
                    )
                )
            )
        )
        _appCategories.value = categories
    }

    private fun loadRecommendedApps() {
        // 模拟推荐应用数据
        val recommendedApps = listOf(
            ContentItem(
                id = "recommended_app_1",
                title = "抖音",
                description = "记录美好生活",
                imageUrl = "https://example.com/recommended_app1.jpg",
                type = ContentType.APP,
                rating = java.math.BigDecimal.valueOf(4.9)
            ),
            ContentItem(
                id = "recommended_app_2",
                title = "支付宝",
                description = "生活好帮手",
                imageUrl = "https://example.com/recommended_app2.jpg",
                type = ContentType.APP,
                rating = java.math.BigDecimal.valueOf(4.8)
            ),
            ContentItem(
                id = "recommended_app_3",
                title = "淘宝",
                description = "随时随地，想淘就淘",
                imageUrl = "https://example.com/recommended_app3.jpg",
                type = ContentType.APP,
                rating = java.math.BigDecimal.valueOf(4.7)
            ),
            ContentItem(
                id = "recommended_app_4",
                title = "高德地图",
                description = "出行导航必备",
                imageUrl = "https://example.com/recommended_app4.jpg",
                type = ContentType.APP,
                rating = java.math.BigDecimal.valueOf(4.8)
            ),
            ContentItem(
                id = "recommended_app_5",
                title = "网易云音乐",
                description = "音乐的力量",
                imageUrl = "https://example.com/recommended_app5.jpg",
                type = ContentType.APP,
                rating = java.math.BigDecimal.valueOf(4.6)
            ),
            ContentItem(
                id = "recommended_app_6",
                title = "QQ",
                description = "每一天，乐在沟通",
                imageUrl = "https://example.com/recommended_app6.jpg",
                type = ContentType.APP,
                rating = java.math.BigDecimal.valueOf(4.5)
            ),
            ContentItem(
                id = "recommended_app_7",
                title = "百度网盘",
                description = "个人云存储",
                imageUrl = "https://example.com/recommended_app7.jpg",
                type = ContentType.APP,
                rating = java.math.BigDecimal.valueOf(4.4)
            ),
            ContentItem(
                id = "recommended_app_8",
                title = "钉钉",
                description = "企业级智能移动办公平台",
                imageUrl = "https://example.com/recommended_app8.jpg",
                type = ContentType.APP,
                rating = java.math.BigDecimal.valueOf(4.3)
            )
        )
        _recommendedApps.value = recommendedApps
    }
}
