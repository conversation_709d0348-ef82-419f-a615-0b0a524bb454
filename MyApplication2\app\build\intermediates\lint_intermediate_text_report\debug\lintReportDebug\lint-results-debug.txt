E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\utils\TestHelper.kt:60: Error: Missing permissions required by ConnectivityManager.getActiveNetwork: android.permission.ACCESS_NETWORK_STATE [MissingPermission]
            val network = connectivityManager.activeNetwork
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\utils\TestHelper.kt:61: Error: Missing permissions required by ConnectivityManager.getNetworkCapabilities: android.permission.ACCESS_NETWORK_STATE [MissingPermission]
            val capabilities = connectivityManager.getNetworkCapabilities(network)
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\utils\TestHelper.kt:71: Error: Missing permissions required by ConnectivityManager.getActiveNetworkInfo: android.permission.ACCESS_NETWORK_STATE [MissingPermission]
            val networkInfo = connectivityManager.activeNetworkInfo
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingPermission":
   This check scans through your code and libraries and looks at the APIs
   being used, and checks this against the set of permissions required to
   access those APIs. If the code using those APIs is called at runtime, then
   the program will crash.

   Furthermore, for permissions that are revocable (with targetSdkVersion 23),
   client code must also be prepared to handle the calls throwing an exception
   if the user rejects the request for permission at runtime.

E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\utils\TestHelper.kt:115: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format("%.2f %s", size, units[unitIndex])
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml: Error: When targeting Android 13 or higher, posting a permission requires holding the POST_NOTIFICATIONS permission (usage from com.bumptech.glide.request.target.NotificationTarget) [NotificationPermission]

   Explanation for issues of type "NotificationPermission":
   When targeting Android 13 and higher, posting permissions requires holding
   the runtime permission android.permission.POST_NOTIFICATIONS.

E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:20: Warning: Attribute usesCleartextTraffic is only used in API level 23 and higher (current min is 21) [UnusedAttribute]
        android:usesCleartextTraffic="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedAttribute":
   This check finds attributes set in XML files that were introduced in a
   version newer than the oldest version targeted by your application (with
   the minSdkVersion attribute).

   This is not an error; the application will simply ignore the attribute.
   However, if the attribute is important to the appearance or functionality
   of your application, you should consider finding an alternative way to
   achieve the same result with only available attributes, and then you can
   optionally create a copy of the layout in a layout-vNN folder which will be
   used on API NN or higher where you can take advantage of the newer
   attribute.

   Note: This check does not only apply to attributes. For example, some tags
   can be unused too, such as the new <tag> element in layouts introduced in
   API 21.

E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\ErrorFragment.kt:21: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
            ContextCompat.getDrawable(activity!!, androidx.leanback.R.drawable.lb_ic_sad_cloud)
                                      ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\ErrorFragment.kt:21: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
            ContextCompat.getDrawable(activity!!, androidx.leanback.R.drawable.lb_ic_sad_cloud)
                                      ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\ErrorFragment.kt:27: Error: Use requireFragmentManager() instead of fragmentManager!! [UseRequireInsteadOfGet from androidx.fragment]
            fragmentManager!!.beginTransaction().remove(this@ErrorFragment).commit()
            ~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\ErrorFragment.kt:27: Error: Use requireFragmentManager() instead of fragmentManager!! [UseRequireInsteadOfGet from androidx.fragment]
            fragmentManager!!.beginTransaction().remove(this@ErrorFragment).commit()
            ~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:76: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        mBackgroundManager.attach(activity!!.window)
                                  ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:77: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        mDefaultBackground = ContextCompat.getDrawable(activity!!, R.drawable.default_background)
                                                       ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:79: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        activity!!.windowManager.defaultDisplay.getMetrics(mMetrics)
        ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:89: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        brandColor = ContextCompat.getColor(activity!!, R.color.fastlane_background)
                                            ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:89: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        brandColor = ContextCompat.getColor(activity!!, R.color.fastlane_background)
                                            ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:91: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        searchAffordanceColor = ContextCompat.getColor(activity!!, R.color.search_opaque)
                                                       ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:91: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        searchAffordanceColor = ContextCompat.getColor(activity!!, R.color.search_opaque)
                                                       ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:221: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
            val intent = Intent(activity!!, SearchActivity::class.java)
                                ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:310: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        Glide.with(activity!!)
                   ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:57: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        mSelectedContentItem = activity!!.intent.getSerializableExtra(DetailsActivity.CONTENT_ITEM) as? ContentItem
                               ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:61: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
            mSelectedMovie = activity!!.intent.getSerializableExtra(DetailsActivity.MOVIE) as? Movie
                             ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:74: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
            val intent = Intent(activity!!, MainActivity::class.java)
                                ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:83: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        Glide.with(activity!!)
                   ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:104: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        row.imageDrawable = ContextCompat.getDrawable(activity!!, R.drawable.default_background)
                                                      ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:104: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        row.imageDrawable = ContextCompat.getDrawable(activity!!, R.drawable.default_background)
                                                      ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:105: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        val width = convertDpToPixel(activity!!, DETAIL_THUMB_WIDTH)
                                     ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:106: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        val height = convertDpToPixel(activity!!, DETAIL_THUMB_HEIGHT)
                                      ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:110: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
        Glide.with(activity!!)
                   ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:234: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
            ContextCompat.getColor(activity!!, R.color.selected_background)
                                   ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:234: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
            ContextCompat.getColor(activity!!, R.color.selected_background)
                                   ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:247: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
                    val intent = Intent(activity!!, PlaybackActivity::class.java)
                                        ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:247: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
                    val intent = Intent(activity!!, PlaybackActivity::class.java)
                                        ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:252: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
                    Toast.makeText(activity!!, "启动游戏: ${mSelectedContentItem?.title}", Toast.LENGTH_SHORT).show()
                                   ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:252: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
                    Toast.makeText(activity!!, "启动游戏: ${mSelectedContentItem?.title}", Toast.LENGTH_SHORT).show()
                                   ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:255: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
                    Toast.makeText(activity!!, "开始下载: ${mSelectedContentItem?.title}", Toast.LENGTH_SHORT).show()
                                   ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:255: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
                    Toast.makeText(activity!!, "开始下载: ${mSelectedContentItem?.title}", Toast.LENGTH_SHORT).show()
                                   ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:258: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
                    Toast.makeText(activity!!, "已添加到收藏: ${mSelectedContentItem?.title}", Toast.LENGTH_SHORT).show()
                                   ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:258: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
                    Toast.makeText(activity!!, "已添加到收藏: ${mSelectedContentItem?.title}", Toast.LENGTH_SHORT).show()
                                   ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:261: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
                    Toast.makeText(activity!!, action.toString(), Toast.LENGTH_SHORT).show()
                                   ~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\VideoDetailsFragment.kt:261: Error: Use requireActivity() instead of activity!! [UseRequireInsteadOfGet from androidx.fragment]
                    Toast.makeText(activity!!, action.toString(), Toast.LENGTH_SHORT).show()
                                   ~~~~~~~~~~

   Explanation for issues of type "UseRequireInsteadOfGet":
   AndroidX added new "require____()" versions of common "get___()" APIs, such
   as getContext/getActivity/getArguments/etc. Rather than wrap these in
   something like requireNotNull(), using these APIs will allow the underlying
   component to try to tell you _why_ it was null, and thus yield a better
   error message.

   Vendor: Android Open Source Project
   Identifier: androidx.fragment
   Feedback: https://issuetracker.google.com/issues/new?component=460964

E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:109: Error: Use viewLifecycleOwner as the LifecycleOwner. [FragmentLiveDataObserve from androidx.fragment]
        viewModel.recommendedMovies.observe(this, Observer { movies ->
                                            ~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:121: Error: Use viewLifecycleOwner as the LifecycleOwner. [FragmentLiveDataObserve from androidx.fragment]
        viewModel.hotMovies.observe(this, Observer { movies ->
                                    ~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:133: Error: Use viewLifecycleOwner as the LifecycleOwner. [FragmentLiveDataObserve from androidx.fragment]
        viewModel.recommendedApps.observe(this, Observer { apps ->
                                          ~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:145: Error: Use viewLifecycleOwner as the LifecycleOwner. [FragmentLiveDataObserve from androidx.fragment]
        viewModel.recommendedGames.observe(this, Observer { games ->
                                           ~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:157: Error: Use viewLifecycleOwner as the LifecycleOwner. [FragmentLiveDataObserve from androidx.fragment]
        viewModel.featuredGames.observe(this, Observer { games ->
                                        ~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:169: Error: Use viewLifecycleOwner as the LifecycleOwner. [FragmentLiveDataObserve from androidx.fragment]
        viewModel.recommendedProducts.observe(this, Observer { products ->
                                              ~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:184: Error: Use viewLifecycleOwner as the LifecycleOwner. [FragmentLiveDataObserve from androidx.fragment]
        viewModel.isLoading.observe(this, Observer { isLoading ->
                                    ~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\MainFragment.kt:190: Error: Use viewLifecycleOwner as the LifecycleOwner. [FragmentLiveDataObserve from androidx.fragment]
        viewModel.error.observe(this, Observer { error ->
                                ~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\UserCenterFragment.kt:48: Error: Use viewLifecycleOwner as the LifecycleOwner. [FragmentLiveDataObserve from androidx.fragment]
        viewModel.favorites.observe(this, Observer { favorites ->
                                    ~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\UserCenterFragment.kt:60: Error: Use viewLifecycleOwner as the LifecycleOwner. [FragmentLiveDataObserve from androidx.fragment]
        viewModel.history.observe(this, Observer { history ->
                                  ~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\UserCenterFragment.kt:75: Error: Use viewLifecycleOwner as the LifecycleOwner. [FragmentLiveDataObserve from androidx.fragment]
        viewModel.isLoading.observe(this, Observer { isLoading ->
                                    ~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\UserCenterFragment.kt:80: Error: Use viewLifecycleOwner as the LifecycleOwner. [FragmentLiveDataObserve from androidx.fragment]
        viewModel.error.observe(this, Observer { error ->
                                ~~~~

   Explanation for issues of type "FragmentLiveDataObserve":
   When observing a LiveData object from a fragment's onCreateView,           
        onViewCreated, onActivityCreated, or onViewStateRestored method       
            getViewLifecycleOwner() should be used as the LifecycleOwner
   rather than the                 Fragment instance. The Fragment lifecycle
   can result in the Fragment being                 active longer than its
   view. This can lead to unexpected behavior from                 LiveData
   objects being observed longer than the Fragment's view is active.

   Vendor: Android Open Source Project
   Identifier: androidx.fragment
   Feedback: https://issuetracker.google.com/issues/new?component=460964

E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_login.xml:2: Warning: Replace the <fragment> tag with FragmentContainerView. [FragmentTagUsage from androidx.fragment]
<fragment xmlns:android="http://schemas.android.com/apk/res/android"
 ~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_search.xml:2: Warning: Replace the <fragment> tag with FragmentContainerView. [FragmentTagUsage from androidx.fragment]
<fragment xmlns:android="http://schemas.android.com/apk/res/android"
 ~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_user_center.xml:2: Warning: Replace the <fragment> tag with FragmentContainerView. [FragmentTagUsage from androidx.fragment]
<fragment xmlns:android="http://schemas.android.com/apk/res/android"
 ~~~~~~~~

   Explanation for issues of type "FragmentTagUsage":
   FragmentContainerView replaces the <fragment> tag as the preferred         
          way of adding fragments via XML. Unlike the <fragment> tag,
   FragmentContainerView                 uses a normal FragmentTransaction
   under the hood to add the initial fragment,                 allowing
   further FragmentTransaction operations on the FragmentContainerView        
           and providing a consistent timing for lifecycle events.

   https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html

   Vendor: Android Open Source Project
   Identifier: androidx.fragment
   Feedback: https://issuetracker.google.com/issues/new?component=460964

E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:27: Warning: Redundant label can be removed [RedundantLabel]
            android:label="@string/app_name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantLabel":
   When an activity does not have a label attribute, it will use the one from
   the application tag. Since the application has already specified the same
   label, the label on this activity can be omitted.

E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts:47: Warning: A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0 [GradleDependency]
    implementation("com.google.code.gson:gson:2.10.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\gradle\libs.versions.toml:3: Warning: A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.1.0 [GradleDependency]
kotlin = "2.0.21"
         ~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:29: Warning: Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed. [DiscouragedApi]
            android:screenOrientation="landscape">
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DiscouragedApi":
   Discouraged APIs are allowed and are not deprecated, but they may be unfit
   for common use (e.g. due to slow performance or subtle behavior).

E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\network\ApiClient.kt:13: Warning: Do not place Android context classes in static fields (static reference to ApiClient which has field context pointing to Context); this is a memory leak [StaticFieldLeak]
/**
^
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\network\ApiClient.kt:22: Warning: Do not place Android context classes in static fields; this is a memory leak [StaticFieldLeak]
    private var context: Context? = null
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "StaticFieldLeak":
   A static field will leak contexts.

   Non-static inner classes have an implicit reference to their outer class.
   If that outer class is for example a Fragment or Activity, then this
   reference means that the long-running handler/loader/task will hold a
   reference to the activity which prevents it from getting garbage
   collected.

   Similarly, direct field references to activities and fragments from these
   longer running instances can cause leaks.

   ViewModel classes should never point to Views or non-application Contexts.

E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_details.xml:2: Warning: This <FrameLayout> can be replaced with a <merge> tag [MergeRootFrame]
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
^

   Explanation for issues of type "MergeRootFrame":
   If a <FrameLayout> is the root of a layout and does not provide background
   or padding etc, it can often be replaced with a <merge> tag which is
   slightly more efficient. Note that this depends on context, so make sure
   you understand how the <merge> tag works before proceeding.

   https://android-developers.googleblog.com/2009/03/android-layout-tricks-3-optimize-by.html

E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml:8: Warning: Possible overdraw: Root element paints background @color/default_background with a theme that also paints a background (inferred theme is @style/Theme.MyApplicationTV) [Overdraw]
    android:background="@color/default_background">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml:8: Warning: Possible overdraw: Root element paints background @color/default_background with a theme that also paints a background (inferred theme is @style/Theme.MyApplicationTV) [Overdraw]
    android:background="@color/default_background">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\app_icon_your_company.png: Warning: Launcher icons should not fill every pixel of their square region; see the design guide for details [IconLauncherShape]

   Explanation for issues of type "IconLauncherShape":
   According to the Android Design Guide
   (https://d.android.com/r/studio-ui/designer/material/iconography) your
   launcher icons should "use a distinct silhouette", a "three-dimensional,
   front view, with a slight perspective as if viewed from above, so that
   users perceive some depth."

   The unique silhouette implies that your launcher icon should not be a
   filled square.

E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\app_icon_your_company.png: Warning: Found bitmap drawable res/drawable/app_icon_your_company.png in densityless folder [IconLocation]
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\movie.png: Warning: Found bitmap drawable res/drawable/movie.png in densityless folder [IconLocation]

   Explanation for issues of type "IconLocation":
   The res/drawable folder is intended for density-independent graphics such
   as shapes defined in XML. For bitmaps, move it to drawable-mdpi and
   consider providing higher and lower resolution versions in drawable-ldpi,
   drawable-hdpi and drawable-xhdpi. If the icon really is density independent
   (for example a solid color) you can place it in drawable-nodpi.

   https://developer.android.com/guide/practices/screens_support.html

E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml:60: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml:73: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml:26: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml:40: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\network\ApiClient.kt:84: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
            prefs.edit().putString("auth_token", token).apply()
            ~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\network\ApiClient.kt:91: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
            prefs.edit().remove("auth_token").apply()
            ~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\PlaybackVideoFragment.kt:100: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
            playerAdapter.setDataSource(Uri.parse(videoUrl))
                                        ~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\utils\UserManager.kt:32: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        val editor = sharedPreferences.edit()
                     ~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\utils\UserManager.kt:73: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        val editor = sharedPreferences.edit()
                     ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseKtx":
   The Android KTX libraries decorates the Android platform SDK as well as
   various libraries with more convenient extension functions available from
   Kotlin, allowing you to use default parameters, named parameters, and
   more.

   Available options:

   **remove-defaults** (default is true):
   Whether to skip arguments that match the defaults provided by the extension.

   Extensions often provide default values for some of the parameters. For example:
   ```kotlin
   fun Path.readLines(charset: Charset = Charsets.UTF_8): List<String> { return Files.readAllLines(this, charset) }
   ```
   This lint check will by default automatically omit parameters that match the default, so if your code was calling

   ```kotlin
   Files.readAllLines(file, Charset.UTF_8)
   ```
   lint would replace this with
   ```kotlin
   file.readLines()
   ```
   rather than

   ```kotlin
   file.readLines(Charset.UTF_8
   ```
   You can turn this behavior off using this option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="remove-defaults" value="true" />
       </issue>
   </lint>
   ```

   **require-present** (default is true):
   Whether to only offer extensions already available.

   This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="require-present" value="true" />
       </issue>
   </lint>
   ```

E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts:44: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts:45: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts:46: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.okhttp3:logging-interceptor:4.11.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts:47: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.code.gson:gson:2.10.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts:50: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts:51: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts:54: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts:55: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts:56: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts:59: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.fragment:fragment-ktx:1.6.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\build.gradle.kts:62: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.recyclerview:recyclerview:1.3.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\LoginFragment.kt:52: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        usernameEditText.setText("test")
                                  ~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\java\com\example\myapplicationtv\LoginFragment.kt:53: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        passwordEditText.setText("123456")
                                  ~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml:13: Warning: Hardcoded string "系统集成测试", should use @string resource [HardcodedText]
        android:text="系统集成测试"
        ~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml:22: Warning: Hardcoded string "正在运行系统测试...", should use @string resource [HardcodedText]
        android:text="正在运行系统测试..."
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml:35: Warning: Hardcoded string "测试项目：", should use @string resource [HardcodedText]
        android:text="测试项目："
        ~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml:43: Warning: Hardcoded string "• 设备兼容性测试n• API连接测试n• 内存性能测试n• 遥控器功能测试n• UI响应时间测试", should use @string resource [HardcodedText]
        android:text="• 设备兼容性测试\n• API连接测试\n• 内存性能测试\n• 遥控器功能测试\n• UI响应时间测试"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml:51: Warning: Hardcoded string "请查看日志获取详细测试结果", should use @string resource [HardcodedText]
        android:text="请查看日志获取详细测试结果"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml:15: Warning: Hardcoded string "App Logo", should use @string resource [HardcodedText]
        android:contentDescription="App Logo" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml:20: Warning: Hardcoded string "MovieTV", should use @string resource [HardcodedText]
        android:text="MovieTV"
        ~~~~~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml:31: Warning: Hardcoded string "用户名", should use @string resource [HardcodedText]
        android:hint="用户名"
        ~~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml:45: Warning: Hardcoded string "密码", should use @string resource [HardcodedText]
        android:hint="密码"
        ~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml:66: Warning: Hardcoded string "登录", should use @string resource [HardcodedText]
            android:text="登录"
            ~~~~~~~~~~~~~~~~~
E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml:79: Warning: Hardcoded string "注册", should use @string resource [HardcodedText]
            android:text="注册"
            ~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

50 errors, 50 warnings
