{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,402,506,610,715,5242", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "198,300,397,501,605,710,826,5338"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,504,629,744,841,935,1058,1178,1287,1412,1568,1693,1815,1918,2012,2136,2226,2327,2439,2542,2644,2763,2880,3004,3125,3234,3349,3470,3593,3709,3827,3914,4002,4119,4258,4424", "endColumns": "106,100,96,93,124,114,96,93,122,119,108,124,155,124,121,102,93,123,89,100,111,102,101,118,116,123,120,108,114,120,122,115,117,86,87,116,138,165,91", "endOffsets": "207,308,405,499,624,739,836,930,1053,1173,1282,1407,1563,1688,1810,1913,2007,2131,2221,2322,2434,2537,2639,2758,2875,2999,3120,3229,3344,3465,3588,3704,3822,3909,3997,4114,4253,4419,4511"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,938,1039,1136,1230,1355,1470,1567,1661,1784,1904,2013,2138,2294,2419,2541,2644,2738,2862,2952,3053,3165,3268,3370,3489,3606,3730,3851,3960,4075,4196,4319,4435,4553,4640,4728,4845,4984,5150", "endColumns": "106,100,96,93,124,114,96,93,122,119,108,124,155,124,121,102,93,123,89,100,111,102,101,118,116,123,120,108,114,120,122,115,117,86,87,116,138,165,91", "endOffsets": "933,1034,1131,1225,1350,1465,1562,1656,1779,1899,2008,2133,2289,2414,2536,2639,2733,2857,2947,3048,3160,3263,3365,3484,3601,3725,3846,3955,4070,4191,4314,4430,4548,4635,4723,4840,4979,5145,5237"}}]}]}