{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-th/values-th.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,304,402,500,603,708,5070", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "196,299,397,495,598,703,815,5166"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,505,632,741,838,931,1054,1177,1283,1405,1531,1647,1763,1877,1975,2102,2191,2296,2398,2507,2601,2712,2819,2932,3045,3152,3266,3380,3493,3602,3710,3797,3880,3978,4112,4261", "endColumns": "106,100,94,96,126,108,96,92,122,122,105,121,125,115,115,113,97,126,88,104,101,108,93,110,106,112,112,106,113,113,112,108,107,86,82,97,133,148,93", "endOffsets": "207,308,403,500,627,736,833,926,1049,1172,1278,1400,1526,1642,1758,1872,1970,2097,2186,2291,2393,2502,2596,2707,2814,2927,3040,3147,3261,3375,3488,3597,3705,3792,3875,3973,4107,4256,4350"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "820,927,1028,1123,1220,1347,1456,1553,1646,1769,1892,1998,2120,2246,2362,2478,2592,2690,2817,2906,3011,3113,3222,3316,3427,3534,3647,3760,3867,3981,4095,4208,4317,4425,4512,4595,4693,4827,4976", "endColumns": "106,100,94,96,126,108,96,92,122,122,105,121,125,115,115,113,97,126,88,104,101,108,93,110,106,112,112,106,113,113,112,108,107,86,82,97,133,148,93", "endOffsets": "922,1023,1118,1215,1342,1451,1548,1641,1764,1887,1993,2115,2241,2357,2473,2587,2685,2812,2901,3006,3108,3217,3311,3422,3529,3642,3755,3862,3976,4090,4203,4312,4420,4507,4590,4688,4822,4971,5065"}}]}]}