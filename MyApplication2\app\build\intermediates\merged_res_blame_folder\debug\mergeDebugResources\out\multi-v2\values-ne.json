{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-29:/values-ne/values-ne.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,418,520,653,763,860,954,1108,1260,1377,1500,1662,1796,1928,2034,2132,2279,2378,2489,2607,2719,2824,2946,3066,3204,3340,3446,3558,3690,3823,3952,4082,4169,4257,4365,4504,4663", "endColumns": "106,100,104,101,132,109,96,93,153,151,116,122,161,133,131,105,97,146,98,110,117,111,104,121,119,137,135,105,111,131,132,128,129,86,87,107,138,158,94", "endOffsets": "207,308,413,515,648,758,855,949,1103,1255,1372,1495,1657,1791,1923,2029,2127,2274,2373,2484,2602,2714,2819,2941,3061,3199,3335,3441,3553,3685,3818,3947,4077,4164,4252,4360,4499,4658,4753"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "825,932,1033,1138,1240,1373,1483,1580,1674,1828,1980,2097,2220,2382,2516,2648,2754,2852,2999,3098,3209,3327,3439,3544,3666,3786,3924,4060,4166,4278,4410,4543,4672,4802,4889,4977,5085,5224,5383", "endColumns": "106,100,104,101,132,109,96,93,153,151,116,122,161,133,131,105,97,146,98,110,117,111,104,121,119,137,135,105,111,131,132,128,129,86,87,107,138,158,94", "endOffsets": "927,1028,1133,1235,1368,1478,1575,1669,1823,1975,2092,2215,2377,2511,2643,2749,2847,2994,3093,3204,3322,3434,3539,3661,3781,3919,4055,4161,4273,4405,4538,4667,4797,4884,4972,5080,5219,5378,5473"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,413,519,617,717,5478", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "203,306,408,514,612,712,820,5574"}}]}]}