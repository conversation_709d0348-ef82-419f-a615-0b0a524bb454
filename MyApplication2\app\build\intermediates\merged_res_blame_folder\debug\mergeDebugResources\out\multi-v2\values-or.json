{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-or/values-or.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,414,515,639,750,851,948,1090,1231,1339,1467,1608,1737,1865,1970,2067,2201,2293,2404,2519,2625,2720,2845,2956,3073,3189,3298,3414,3532,3652,3765,3880,3967,4058,4162,4298,4453", "endColumns": "106,100,100,100,123,110,100,96,141,140,107,127,140,128,127,104,96,133,91,110,114,105,94,124,110,116,115,108,115,117,119,112,114,86,90,103,135,154,86", "endOffsets": "207,308,409,510,634,745,846,943,1085,1226,1334,1462,1603,1732,1860,1965,2062,2196,2288,2399,2514,2620,2715,2840,2951,3068,3184,3293,3409,3527,3647,3760,3875,3962,4053,4157,4293,4448,4535"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,947,1048,1149,1250,1374,1485,1586,1683,1825,1966,2074,2202,2343,2472,2600,2705,2802,2936,3028,3139,3254,3360,3455,3580,3691,3808,3924,4033,4149,4267,4387,4500,4615,4702,4793,4897,5033,5188", "endColumns": "106,100,100,100,123,110,100,96,141,140,107,127,140,128,127,104,96,133,91,110,114,105,94,124,110,116,115,108,115,117,119,112,114,86,90,103,135,154,86", "endOffsets": "942,1043,1144,1245,1369,1480,1581,1678,1820,1961,2069,2197,2338,2467,2595,2700,2797,2931,3023,3134,3249,3355,3450,3575,3686,3803,3919,4028,4144,4262,4382,4495,4610,4697,4788,4892,5028,5183,5270"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,208,310,413,518,619,721,5275", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "203,305,408,513,614,716,835,5371"}}]}]}