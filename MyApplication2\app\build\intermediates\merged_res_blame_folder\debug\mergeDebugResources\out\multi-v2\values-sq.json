{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-sq/values-sq.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,404,501,609,720,5295", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "199,301,399,496,604,715,837,5391"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,505,631,746,842,932,1073,1193,1306,1435,1596,1727,1856,1965,2056,2200,2289,2398,2508,2611,2704,2822,2939,3055,3169,3280,3395,3523,3650,3774,3898,3985,4068,4171,4309,4463", "endColumns": "106,100,95,95,125,114,95,89,140,119,112,128,160,130,128,108,90,143,88,108,109,102,92,117,116,115,113,110,114,127,126,123,123,86,82,102,137,153,94", "endOffsets": "207,308,404,500,626,741,837,927,1068,1188,1301,1430,1591,1722,1851,1960,2051,2195,2284,2393,2503,2606,2699,2817,2934,3050,3164,3275,3390,3518,3645,3769,3893,3980,4063,4166,4304,4458,4553"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,949,1050,1146,1242,1368,1483,1579,1669,1810,1930,2043,2172,2333,2464,2593,2702,2793,2937,3026,3135,3245,3348,3441,3559,3676,3792,3906,4017,4132,4260,4387,4511,4635,4722,4805,4908,5046,5200", "endColumns": "106,100,95,95,125,114,95,89,140,119,112,128,160,130,128,108,90,143,88,108,109,102,92,117,116,115,113,110,114,127,126,123,123,86,82,102,137,153,94", "endOffsets": "944,1045,1141,1237,1363,1478,1574,1664,1805,1925,2038,2167,2328,2459,2588,2697,2788,2932,3021,3130,3240,3343,3436,3554,3671,3787,3901,4012,4127,4255,4382,4506,4630,4717,4800,4903,5041,5195,5290"}}]}]}