{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-tl/values-tl.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,405,502,609,717,5329", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "197,299,400,497,604,712,834,5425"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,508,646,763,862,956,1093,1228,1335,1463,1616,1748,1878,1995,2088,2221,2312,2415,2521,2626,2721,2838,2960,3081,3200,3310,3425,3552,3672,3795,3911,3998,4084,4193,4333,4496", "endColumns": "106,100,99,94,137,116,98,93,136,134,106,127,152,131,129,116,92,132,90,102,105,104,94,116,121,120,118,109,114,126,119,122,115,86,85,108,139,162,98", "endOffsets": "207,308,408,503,641,758,857,951,1088,1223,1330,1458,1611,1743,1873,1990,2083,2216,2307,2410,2516,2621,2716,2833,2955,3076,3195,3305,3420,3547,3667,3790,3906,3993,4079,4188,4328,4491,4590"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "839,946,1047,1147,1242,1380,1497,1596,1690,1827,1962,2069,2197,2350,2482,2612,2729,2822,2955,3046,3149,3255,3360,3455,3572,3694,3815,3934,4044,4159,4286,4406,4529,4645,4732,4818,4927,5067,5230", "endColumns": "106,100,99,94,137,116,98,93,136,134,106,127,152,131,129,116,92,132,90,102,105,104,94,116,121,120,118,109,114,126,119,122,115,86,85,108,139,162,98", "endOffsets": "941,1042,1142,1237,1375,1492,1591,1685,1822,1957,2064,2192,2345,2477,2607,2724,2817,2950,3041,3144,3250,3355,3450,3567,3689,3810,3929,4039,4154,4281,4401,4524,4640,4727,4813,4922,5062,5225,5324"}}]}]}