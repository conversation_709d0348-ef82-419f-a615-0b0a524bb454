&androidx.fragment.app.FragmentActivityandroidx.fragment.app.Fragment"androidx.leanback.widget.Presenter<androidx.leanback.widget.AbstractDetailsDescriptionPresenter*androidx.leanback.app.ErrorSupportFragment+androidx.leanback.app.BrowseSupportFragment2androidx.leanback.widget.OnItemViewClickedListener3androidx.leanback.widget.OnItemViewSelectedListenerjava.util.TimerTaskjava.io.Serializable*androidx.leanback.app.VideoSupportFragment,androidx.leanback.app.DetailsSupportFragment/<EMAIL>android.app.Applicationkotlin.Enumandroidx.lifecycle.ViewModel&androidx.leanback.widget.ImageCardView                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      