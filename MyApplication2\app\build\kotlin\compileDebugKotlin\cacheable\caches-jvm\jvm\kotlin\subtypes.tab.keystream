&androidx.fragment.app.FragmentActivityandroidx.fragment.app.Fragment"androidx.leanback.widget.Presenter/com.example.myapplicationtv.base.BaseTVActivity<androidx.leanback.widget.AbstractDetailsDescriptionPresenter*androidx.leanback.app.ErrorSupportFragment+androidx.leanback.app.BrowseSupportFragment2androidx.leanback.widget.OnItemViewClickedListener3androidx.leanback.widget.OnItemViewSelectedListenerjava.util.TimerTaskjava.io.Serializable*<EMAIL>android.app.Application,androidx.leanback.app.DetailsSupportFragment1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolderkotlin.Enum com.google.gson.JsonDeserializerandroidx.lifecycle.ViewModel&androidx.leanback.widget.ImageCardView                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              