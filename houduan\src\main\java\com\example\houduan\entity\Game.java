package com.example.houduan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 游戏实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "game")
public class Game {

    @Id
    @Column(length = 32)
    private String id;

    /**
     * 游戏名称
     */
    private String name;

    /**
     * 游戏封面URL
     */
    private String cover;

    /**
     * 游戏图标URL
     */
    private String icon;

    /**
     * 游戏分类
     */
    private String category;

    /**
     * 版本号
     */
    private String version;

    /**
     * 游戏大小(字节)
     */
    private Long size;

    /**
     * 评分(0.0-10.0)
     */
    private BigDecimal rating;

    /**
     * 评分人数
     */
    private Integer ratingCount;

    /**
     * 游玩次数
     */
    private Long playCount;

    /**
     * 游戏描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 游戏地址
     */
    private String playUrl;

    /**
     * 截图URLs(JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String screenshots;

    /**
     * 是否推荐(0:否,1:是)
     */
    private Integer isRecommended;

    /**
     * 是否精选(0:否,1:是)
     */
    private Integer isFeatured;

    /**
     * 状态(0:下架,1:上架)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
