<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2">
    <attr format="reference" name="cardViewStyle"/>
    <attr format="reference" name="coordinatorLayoutStyle"/>
    <attr format="reference" name="nestedScrollViewStyle"/>
    <attr format="reference" name="recyclerViewStyle"/>
    <color name="accent_blue">#0066cc</color>
    <color name="accent_cyan">#06b6d4</color>
    <color name="accent_green">#10b981</color>
    <color name="accent_orange">#ff6b35</color>
    <color name="accent_purple">#8b5cf6</color>
    <color name="accent_yellow">#f59e0b</color>
    <color name="androidx_core_ripple_material_light">#1f000000</color>
    <color name="androidx_core_secondary_text_default_material_light">#8a000000</color>
    <color name="background_gradient_end">#DDDDDD</color>
    <color name="background_gradient_start">#000000</color>
    <color name="call_notification_answer_color">#1d873b</color>
    <color name="call_notification_decline_color">#d93025</color>
    <color name="card_background">#0f3460</color>
    <color name="cardview_dark_background">#FF424242</color>
    <color name="cardview_light_background">#FFFFFFFF</color>
    <color name="cardview_shadow_end_color">#03000000</color>
    <color name="cardview_shadow_start_color">#37000000</color>
    <color name="category_focused_background">#3a3a5e</color>
    <color name="category_normal_background">#2a2a3e</color>
    <color name="custom_tv_background">#1a1a2e</color>
    <color name="default_background">#3d3d3d</color>
    <color name="fastlane_background">#0096a6</color>
    <color name="focus_border_color">#0066cc</color>
    <color name="lb_action_text_color">#EEEEEE</color>
    <color name="lb_background_protection">#99000000</color>
    <color name="lb_basic_card_bg_color">#FF263238</color>
    <color name="lb_basic_card_content_text_color">#B3EEEEEE</color>
    <color name="lb_basic_card_info_bg_color">#FF37474F</color>
    <color name="lb_basic_card_title_text_color">#FFEEEEEE</color>
    <color name="lb_browse_header_color">#FFFFFF</color>
    <color name="lb_browse_header_description_color">#AAFFFFFF</color>
    <color name="lb_browse_title_color">#EEEEEE</color>
    <color name="lb_control_button_color">#66EEEEEE</color>
    <color name="lb_control_button_text">#EEEEEE</color>
    <color name="lb_default_brand_color">#FF1B1C1E</color>
    <color name="lb_default_brand_color_dark">#FF263238</color>
    <color name="lb_default_search_color">#FF86C739</color>
    <color name="lb_default_search_icon_color">#FFFFFFFF</color>
    <color name="lb_details_description_body_color">#B2EEEEEE</color>
    <color name="lb_details_description_color">#EEEEEE</color>
    <color name="lb_details_overview_bg_color">#1B1B1B</color>
    <color name="lb_error_background_color_opaque">#262626</color>
    <color name="lb_error_background_color_translucent">#E6000000</color>
    <color name="lb_error_message">#80EEEEEE</color>
    <color name="lb_grey">#888888</color>
    <color name="lb_guidedactions_background">#FF111111</color>
    <color name="lb_guidedactions_background_dark">#FF080808</color>
    <color name="lb_guidedactions_item_unselected_text_color">#FFF1F1F1</color>
    <color name="lb_list_item_unselected_text_color">#FFF1F1F1</color>
    <color name="lb_media_background_color">#FF384248</color>
    <color name="lb_page_indicator_arrow_background">#EEEEEE</color>
    <color name="lb_page_indicator_arrow_shadow">#4C000000</color>
    <color name="lb_page_indicator_dot">#014269</color>
    <color name="lb_playback_background_progress_color">#19FFFFFF</color>
    <color name="lb_playback_controls_background_dark">#c0000000</color>
    <color name="lb_playback_controls_background_light">#80000000</color>
    <color name="lb_playback_controls_time_text_color">#B2EEEEEE</color>
    <color name="lb_playback_icon_highlight_no_theme">#ff40c4ff</color>
    <color name="lb_playback_media_row_highlight_color">#1AFFFFFF</color>
    <color name="lb_playback_media_row_separator_highlight_color">#1AFFFFFF</color>
    <color name="lb_playback_now_playing_bar_color">#FFEEEEEE</color>
    <color name="lb_playback_progress_color_no_theme">#ff40c4ff</color>
    <color name="lb_playback_progress_secondary_color_no_theme">#d3d3d3</color>
    <color name="lb_playback_secondary_progress_color">#33FFFFFF</color>
    <color name="lb_preference_item_category_text_color">#FF51AAFF</color>
    <color name="lb_search_bar_hint">#FF888888</color>
    <color name="lb_search_bar_hint_speech_mode">#66222222</color>
    <color name="lb_search_bar_text">#80EEEEEE</color>
    <color name="lb_search_bar_text_speech_mode">#FF444444</color>
    <color name="lb_search_plate_hint_text_color">#FFCCCCCC</color>
    <color name="lb_speech_orb_not_recording">#CCCCCC</color>
    <color name="lb_speech_orb_not_recording_icon">#555555</color>
    <color name="lb_speech_orb_not_recording_pulsed">#EEEEEE</color>
    <color name="lb_speech_orb_recording">#ff4343</color>
    <color name="lb_tv_white">#FFCCCCCC</color>
    <color name="lb_view_dim_mask_color">#000000</color>
    <color name="navigation_background">#16213e</color>
    <color name="notification_action_color_filter">#ffffffff</color>
    <color name="notification_icon_bg_color">#ff9e9e9e</color>
    <color name="notification_material_background_media_default_color">#ff424242</color>
    <color name="primary_text_default_material_dark">#ffffffff</color>
    <color name="rating_color">#ffd700</color>
    <color name="search_opaque">#ffaa3f</color>
    <color name="secondary_text_default_material_dark">#b3ffffff</color>
    <color name="selected_background">#ffaa3f</color>
    <dimen name="cardview_compat_inset_shadow">1dp</dimen>
    <dimen name="cardview_default_elevation">2dp</dimen>
    <dimen name="cardview_default_radius">2dp</dimen>
    <dimen name="compat_button_inset_horizontal_material">4dp</dimen>
    <dimen name="compat_button_inset_vertical_material">6dp</dimen>
    <dimen name="compat_button_padding_horizontal_material">8dp</dimen>
    <dimen name="compat_button_padding_vertical_material">4dp</dimen>
    <dimen name="compat_control_corner_material">2dp</dimen>
    <dimen name="compat_notification_large_icon_max_height">320dp</dimen>
    <dimen name="compat_notification_large_icon_max_width">320dp</dimen>
    <dimen name="fastscroll_default_thickness">8dp</dimen>
    <dimen name="fastscroll_margin">0dp</dimen>
    <dimen name="fastscroll_minimum_range">50dp</dimen>
    <dimen name="item_touch_helper_max_drag_scroll_per_frame">20dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_max_velocity">800dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_velocity">120dp</dimen>
    <dimen name="lb_action_1_line_height">36dp</dimen>
    <dimen name="lb_action_2_lines_height">56dp</dimen>
    <dimen name="lb_action_button_corner_radius">2dp</dimen>
    <dimen name="lb_action_icon_margin">12dp</dimen>
    <dimen name="lb_action_padding_horizontal">24dp</dimen>
    <dimen name="lb_action_text_size">16sp</dimen>
    <dimen name="lb_action_with_icon_padding_end">20dp</dimen>
    <dimen name="lb_action_with_icon_padding_start">14dp</dimen>
    <dimen name="lb_basic_card_content_text_size">12sp</dimen>
    <dimen name="lb_basic_card_info_badge_margin">4dp</dimen>
    <dimen name="lb_basic_card_info_badge_size">16dp</dimen>
    <dimen name="lb_basic_card_info_height">52dp</dimen>
    <dimen name="lb_basic_card_info_height_no_content">34dp</dimen>
    <dimen name="lb_basic_card_info_padding_bottom">8dp</dimen>
    <dimen name="lb_basic_card_info_padding_horizontal">11dp</dimen>
    <dimen name="lb_basic_card_info_padding_top">7dp</dimen>
    <dimen name="lb_basic_card_info_text_margin">1dp</dimen>
    <dimen name="lb_basic_card_main_height">188dp</dimen>
    <dimen name="lb_basic_card_main_width">140dp</dimen>
    <dimen name="lb_basic_card_title_text_size">14sp</dimen>
    <dimen name="lb_browse_expanded_row_no_hovercard_bottom_padding">28dp</dimen>
    <dimen name="lb_browse_expanded_selected_row_top_padding">16dp</dimen>
    <dimen name="lb_browse_header_description_text_size">14sp</dimen>
    <dimen name="lb_browse_header_fading_length">12dp</dimen>
    <dimen name="lb_browse_header_height">24dp</dimen>
    <dimen name="lb_browse_header_padding_end">8dp</dimen>
    <item format="integer" name="lb_browse_header_select_duration" type="dimen">150</item>
    <item format="float" name="lb_browse_header_select_scale" type="dimen">1.2</item>
    <dimen name="lb_browse_header_text_size">20sp</dimen>
    <dimen name="lb_browse_headers_vertical_spacing">21dp</dimen>
    <dimen name="lb_browse_headers_width">270dp</dimen>
    <dimen name="lb_browse_headers_z">@dimen/lb_material_shadow_focused_z</dimen>
    <dimen name="lb_browse_item_horizontal_spacing">8dp</dimen>
    <dimen name="lb_browse_item_vertical_spacing">8dp</dimen>
    <dimen name="lb_browse_padding_bottom">48dp</dimen>
    <dimen name="lb_browse_padding_end">56dp</dimen>
    <dimen name="lb_browse_padding_start">56dp</dimen>
    <dimen name="lb_browse_padding_top">27dp</dimen>
    <dimen name="lb_browse_row_hovercard_description_font_size">14sp</dimen>
    <dimen name="lb_browse_row_hovercard_max_width">400dp</dimen>
    <dimen name="lb_browse_row_hovercard_title_font_size">18sp</dimen>
    <dimen name="lb_browse_rows_fading_edge">16dp</dimen>
    <dimen name="lb_browse_rows_margin_start">238dp</dimen>
    <dimen name="lb_browse_rows_margin_top">167dp</dimen>
    <dimen name="lb_browse_section_header_text_size">16sp</dimen>
    <dimen name="lb_browse_selected_row_top_padding">20dp</dimen>
    <dimen name="lb_browse_title_height">60dp</dimen>
    <dimen name="lb_browse_title_icon_height">60dp</dimen>
    <dimen name="lb_browse_title_icon_max_width">584dp</dimen>
    <dimen name="lb_browse_title_text_size">44sp</dimen>
    <dimen name="lb_control_button_diameter">90dp</dimen>
    <dimen name="lb_control_button_height">64dp</dimen>
    <dimen name="lb_control_button_secondary_diameter">48dp</dimen>
    <dimen name="lb_control_button_secondary_height">48dp</dimen>
    <dimen name="lb_control_button_text_size">22sp</dimen>
    <dimen name="lb_control_icon_height">32dp</dimen>
    <dimen name="lb_control_icon_width">32dp</dimen>
    <dimen name="lb_details_cover_drawable_parallax_movement">50dip</dimen>
    <dimen name="lb_details_description_body_line_spacing">20dp</dimen>
    <dimen name="lb_details_description_body_text_size">14sp</dimen>
    <dimen name="lb_details_description_subtitle_text_size">16sp</dimen>
    <dimen name="lb_details_description_title_baseline">26dp</dimen>
    <dimen name="lb_details_description_title_line_spacing">40dp</dimen>
    <dimen name="lb_details_description_title_padding_adjust_bottom">2dp</dimen>
    <dimen name="lb_details_description_title_padding_adjust_top">-1dp</dimen>
    <dimen name="lb_details_description_title_resized_text_size">28sp</dimen>
    <dimen name="lb_details_description_title_text_size">34sp</dimen>
    <dimen name="lb_details_description_under_subtitle_baseline_margin">32dp</dimen>
    <dimen name="lb_details_description_under_title_baseline_margin">32dp</dimen>
    <dimen name="lb_details_overview_action_items_spacing">16dp</dimen>
    <item format="integer" name="lb_details_overview_action_select_duration" type="dimen">150</item>
    <dimen name="lb_details_overview_actions_fade_size">16dp</dimen>
    <dimen name="lb_details_overview_actions_height">56dp</dimen>
    <dimen name="lb_details_overview_actions_padding_end">132dp</dimen>
    <dimen name="lb_details_overview_actions_padding_start">294dp</dimen>
    <dimen name="lb_details_overview_description_margin_bottom">12dp</dimen>
    <dimen name="lb_details_overview_description_margin_end">24dp</dimen>
    <dimen name="lb_details_overview_description_margin_start">24dp</dimen>
    <dimen name="lb_details_overview_description_margin_top">24dp</dimen>
    <dimen name="lb_details_overview_height_large">274dp</dimen>
    <dimen name="lb_details_overview_height_small">159dp</dimen>
    <dimen name="lb_details_overview_image_margin_horizontal">24dp</dimen>
    <dimen name="lb_details_overview_image_margin_vertical">24dp</dimen>
    <dimen name="lb_details_overview_margin_bottom">40dp</dimen>
    <dimen name="lb_details_overview_margin_end">132dp</dimen>
    <dimen name="lb_details_overview_margin_start">132dp</dimen>
    <dimen name="lb_details_overview_z">@dimen/lb_material_shadow_details_z</dimen>
    <dimen name="lb_details_rows_align_top">167dp</dimen>
    <dimen name="lb_details_v2_actions_height">56dip</dimen>
    <dimen name="lb_details_v2_align_pos_for_actions">270dp</dimen>
    <dimen name="lb_details_v2_align_pos_for_description">0dp</dimen>
    <dimen name="lb_details_v2_blank_height">160dp</dimen>
    <dimen name="lb_details_v2_card_height">540dp</dimen>
    <dimen name="lb_details_v2_description_margin_end">54dp</dimen>
    <dimen name="lb_details_v2_description_margin_start">24dp</dimen>
    <dimen name="lb_details_v2_description_margin_top">24dp</dimen>
    <dimen name="lb_details_v2_left">270dip</dimen>
    <dimen name="lb_details_v2_logo_margin_start">128dp</dimen>
    <dimen name="lb_details_v2_logo_max_height">210dp</dimen>
    <dimen name="lb_details_v2_logo_max_width">150dp</dimen>
    <dimen name="lb_error_image_max_height">120dp</dimen>
    <dimen name="lb_error_message_max_width">600dp</dimen>
    <dimen name="lb_error_message_text_size">16sp</dimen>
    <dimen name="lb_error_under_image_baseline_margin">36dp</dimen>
    <dimen name="lb_error_under_message_baseline_margin">24dp</dimen>
    <dimen name="lb_guidedactions_elevation">12dp</dimen>
    <dimen name="lb_guidedactions_item_bottom_padding">13dp</dimen>
    <dimen name="lb_guidedactions_item_checkmark_diameter">16dp</dimen>
    <dimen name="lb_guidedactions_item_delimiter_padding">4dp</dimen>
    <dimen name="lb_guidedactions_item_description_font_size">12sp</dimen>
    <item format="float" name="lb_guidedactions_item_disabled_chevron_alpha" type="dimen">0.50</item>
    <item format="float" name="lb_guidedactions_item_disabled_description_text_alpha" type="dimen">0.25</item>
    <item format="float" name="lb_guidedactions_item_disabled_text_alpha" type="dimen">0.25</item>
    <item format="float" name="lb_guidedactions_item_enabled_chevron_alpha" type="dimen">1.00</item>
    <dimen name="lb_guidedactions_item_end_padding">16dp</dimen>
    <dimen name="lb_guidedactions_item_icon_height">32dp</dimen>
    <dimen name="lb_guidedactions_item_icon_width">32dp</dimen>
    <dimen name="lb_guidedactions_item_space_between_title_and_description">2dp</dimen>
    <dimen name="lb_guidedactions_item_start_padding">16dp</dimen>
    <dimen name="lb_guidedactions_item_text_width">248dp</dimen>
    <dimen name="lb_guidedactions_item_text_width_no_icon">284dp</dimen>
    <dimen name="lb_guidedactions_item_title_font_size">14sp</dimen>
    <dimen name="lb_guidedactions_item_top_padding">14dp</dimen>
    <item format="float" name="lb_guidedactions_item_unselected_description_text_alpha" type="dimen">0.50</item>
    <item format="float" name="lb_guidedactions_item_unselected_text_alpha" type="dimen">1.00</item>
    <dimen name="lb_guidedactions_list_padding_end">24dp</dimen>
    <dimen name="lb_guidedactions_list_padding_start">24dp</dimen>
    <dimen name="lb_guidedactions_list_vertical_spacing">8dp</dimen>
    <dimen name="lb_guidedactions_section_shadow_width">32dp</dimen>
    <dimen name="lb_guidedactions_sublist_bottom_margin">28dp</dimen>
    <dimen name="lb_guidedactions_sublist_padding_bottom">8dip</dimen>
    <dimen name="lb_guidedactions_sublist_padding_top">8dip</dimen>
    <dimen name="lb_guidedactions_vertical_padding">14dp</dimen>
    <item format="float" name="lb_guidedactions_width_weight" type="dimen">0.71428571428</item>
    <item format="float" name="lb_guidedactions_width_weight_two_panels" type="dimen">1.191780822</item>
    <item format="float" name="lb_guidedbuttonactions_width_weight" type="dimen">0.45</item>
    <item format="float" name="lb_guidedstep_height_weight" type="dimen">2.0</item>
    <item format="float" name="lb_guidedstep_height_weight_translucent" type="dimen">1.0</item>
    <item format="float" name="lb_guidedstep_keyline" type="dimen">40.0</item>
    <dimen name="lb_guidedstep_slide_ime_distance">-100dp</dimen>
    <dimen name="lb_list_row_height">224dp</dimen>
    <dimen name="lb_material_shadow_details_z">8dp</dimen>
    <dimen name="lb_material_shadow_focused_z">10dp</dimen>
    <dimen name="lb_material_shadow_normal_z">0dp</dimen>
    <dimen name="lb_onboarding_content_margin_bottom">98dp</dimen>
    <dimen name="lb_onboarding_content_margin_top">164dp</dimen>
    <dimen name="lb_onboarding_content_width">536dp</dimen>
    <dimen name="lb_onboarding_header_height">100dp</dimen>
    <dimen name="lb_onboarding_header_margin_top">64dp</dimen>
    <dimen name="lb_onboarding_navigation_height">40dp</dimen>
    <dimen name="lb_onboarding_start_button_height">36dp</dimen>
    <dimen name="lb_onboarding_start_button_margin_bottom">62dp</dimen>
    <dimen name="lb_onboarding_start_button_translation_offset">16dp</dimen>
    <dimen name="lb_page_indicator_arrow_gap">32dp</dimen>
    <dimen name="lb_page_indicator_arrow_radius">18dp</dimen>
    <dimen name="lb_page_indicator_arrow_shadow_offset">1dp</dimen>
    <dimen name="lb_page_indicator_arrow_shadow_radius">2dp</dimen>
    <dimen name="lb_page_indicator_dot_gap">16dp</dimen>
    <dimen name="lb_page_indicator_dot_radius">5dp</dimen>
    <dimen name="lb_playback_controls_card_height">176dp</dimen>
    <dimen name="lb_playback_controls_child_margin_bigger">64dp</dimen>
    <dimen name="lb_playback_controls_child_margin_biggest">88dp</dimen>
    <dimen name="lb_playback_controls_child_margin_default">48dp</dimen>
    <dimen name="lb_playback_controls_margin_bottom">20dp</dimen>
    <dimen name="lb_playback_controls_margin_end">132dp</dimen>
    <dimen name="lb_playback_controls_margin_start">132dp</dimen>
    <dimen name="lb_playback_controls_padding_bottom">28dp</dimen>
    <dimen name="lb_playback_controls_time_text_size">12sp</dimen>
    <dimen name="lb_playback_controls_z">@dimen/lb_material_shadow_details_z</dimen>
    <dimen name="lb_playback_current_time_margin_start">16dp</dimen>
    <dimen name="lb_playback_description_margin_end">24dp</dimen>
    <dimen name="lb_playback_description_margin_start">24dp</dimen>
    <dimen name="lb_playback_description_margin_top">24dp</dimen>
    <dimen name="lb_playback_major_fade_translate_y">200dp</dimen>
    <dimen name="lb_playback_media_item_radio_icon_size">24dp</dimen>
    <dimen name="lb_playback_media_radio_width_with_padding">88dp</dimen>
    <dimen name="lb_playback_media_row_details_selector_width">668dp</dimen>
    <dimen name="lb_playback_media_row_horizontal_padding">32dp</dimen>
    <dimen name="lb_playback_media_row_radio_selector_width">72dp</dimen>
    <dimen name="lb_playback_media_row_selector_round_rect_radius">36dp</dimen>
    <dimen name="lb_playback_media_row_separator_height">1dp</dimen>
    <dimen name="lb_playback_minor_fade_translate_y">16dp</dimen>
    <dimen name="lb_playback_now_playing_bar_height">18dp</dimen>
    <dimen name="lb_playback_now_playing_bar_left_margin">3dp</dimen>
    <dimen name="lb_playback_now_playing_bar_margin">1dp</dimen>
    <dimen name="lb_playback_now_playing_bar_top_margin">3dp</dimen>
    <dimen name="lb_playback_now_playing_bar_width">5dp</dimen>
    <dimen name="lb_playback_now_playing_view_size">28dp</dimen>
    <dimen name="lb_playback_other_rows_center_to_bottom">270dp</dimen>
    <dimen name="lb_playback_play_icon_size">14dp</dimen>
    <dimen name="lb_playback_time_padding_top">8dp</dimen>
    <dimen name="lb_playback_total_time_margin_end">16dp</dimen>
    <dimen name="lb_playback_transport_control_info_margin_bottom">20dp</dimen>
    <dimen name="lb_playback_transport_control_row_padding_bottom">20dp</dimen>
    <dimen name="lb_playback_transport_controlbar_margin_start">-12dp</dimen>
    <dimen name="lb_playback_transport_hero_thumbs_height">192dp</dimen>
    <dimen name="lb_playback_transport_hero_thumbs_width">192dp</dimen>
    <dimen name="lb_playback_transport_image_height">176dp</dimen>
    <dimen name="lb_playback_transport_image_margin_end">24dp</dimen>
    <dimen name="lb_playback_transport_progressbar_active_bar_height">6dp</dimen>
    <dimen name="lb_playback_transport_progressbar_active_radius">6dp</dimen>
    <dimen name="lb_playback_transport_progressbar_bar_height">4dp</dimen>
    <dimen name="lb_playback_transport_progressbar_height">28dp</dimen>
    <dimen name="lb_playback_transport_thumbs_bottom_margin">18dp</dimen>
    <dimen name="lb_playback_transport_thumbs_height">154dp</dimen>
    <dimen name="lb_playback_transport_thumbs_margin">4dp</dimen>
    <dimen name="lb_playback_transport_thumbs_width">154dp</dimen>
    <dimen name="lb_playback_transport_time_margin">8dp</dimen>
    <dimen name="lb_playback_transport_time_margin_top">8dp</dimen>
    <dimen name="lb_rounded_rect_corner_radius">2dp</dimen>
    <dimen name="lb_search_bar_edit_text_margin_start">24dp</dimen>
    <dimen name="lb_search_bar_height">60dp</dimen>
    <dimen name="lb_search_bar_hint_margin_start">52dp</dimen>
    <dimen name="lb_search_bar_icon_height">32dp</dimen>
    <dimen name="lb_search_bar_icon_margin_start">16dp</dimen>
    <dimen name="lb_search_bar_icon_width">32dp</dimen>
    <dimen name="lb_search_bar_inner_margin_bottom">2dp</dimen>
    <dimen name="lb_search_bar_inner_margin_top">2dp</dimen>
    <dimen name="lb_search_bar_items_height">56dp</dimen>
    <dimen name="lb_search_bar_items_layout_margin_top">27dp</dimen>
    <dimen name="lb_search_bar_items_margin_start">70dp</dimen>
    <dimen name="lb_search_bar_items_width">600dp</dimen>
    <dimen name="lb_search_bar_padding_start">56dp</dimen>
    <dimen name="lb_search_bar_padding_top">27dp</dimen>
    <dimen name="lb_search_bar_speech_orb_margin_start">56dp</dimen>
    <dimen name="lb_search_bar_speech_orb_size">52dp</dimen>
    <dimen name="lb_search_bar_text_size">18sp</dimen>
    <dimen name="lb_search_bar_unfocused_text_size">18sp</dimen>
    <dimen name="lb_search_browse_row_padding_start">56dp</dimen>
    <dimen name="lb_search_browse_rows_align_top">147dp</dimen>
    <dimen name="lb_search_orb_focused_z">8dp</dimen>
    <dimen name="lb_search_orb_margin_bottom">4dp</dimen>
    <dimen name="lb_search_orb_margin_end">4dp</dimen>
    <dimen name="lb_search_orb_margin_start">4dp</dimen>
    <dimen name="lb_search_orb_margin_top">4dp</dimen>
    <dimen name="lb_search_orb_size">52dp</dimen>
    <dimen name="lb_search_orb_unfocused_z">2dp</dimen>
    <dimen name="lb_vertical_grid_padding_bottom">87dp</dimen>
    <dimen name="notification_action_icon_size">32dp</dimen>
    <dimen name="notification_action_text_size">13sp</dimen>
    <dimen name="notification_big_circle_margin">12dp</dimen>
    <dimen name="notification_content_margin_start">8dp</dimen>
    <dimen name="notification_large_icon_height">64dp</dimen>
    <dimen name="notification_large_icon_width">64dp</dimen>
    <dimen name="notification_main_column_padding_top">10dp</dimen>
    <dimen name="notification_media_narrow_margin">@dimen/notification_content_margin_start</dimen>
    <dimen name="notification_right_icon_size">16dp</dimen>
    <dimen name="notification_right_side_padding_top">2dp</dimen>
    <dimen name="notification_small_icon_background_padding">3dp</dimen>
    <dimen name="notification_small_icon_size_as_large">24dp</dimen>
    <dimen name="notification_subtext_size">13sp</dimen>
    <dimen name="notification_top_pad">10dp</dimen>
    <dimen name="notification_top_pad_large_text">5dp</dimen>
    <dimen name="picker_column_horizontal_padding">8dp</dimen>
    <dimen name="picker_item_height">32dp</dimen>
    <dimen name="picker_item_spacing">32dp</dimen>
    <dimen name="picker_separator_horizontal_padding">4dp</dimen>
    <dimen name="subtitle_corner_radius">2dp</dimen>
    <dimen name="subtitle_outline_width">2dp</dimen>
    <dimen name="subtitle_shadow_offset">2dp</dimen>
    <dimen name="subtitle_shadow_radius">2dp</dimen>
    <drawable name="notification_template_icon_bg">#3333B5E5</drawable>
    <drawable name="notification_template_icon_low_bg">#0cffffff</drawable>
    <item name="lb_browse_header_unselect_alpha" type="fraction">50%</item>
    <item name="lb_browse_rows_scale" type="fraction">80%</item>
    <item name="lb_focus_zoom_factor_large" type="fraction">118%</item>
    <item name="lb_focus_zoom_factor_medium" type="fraction">114%</item>
    <item name="lb_focus_zoom_factor_small" type="fraction">110%</item>
    <item name="lb_focus_zoom_factor_xsmall" type="fraction">106%</item>
    <item name="lb_search_bar_speech_orb_max_level_zoom" type="fraction">144%</item>
    <item name="lb_search_orb_focused_zoom" type="fraction">120%</item>
    <item name="lb_view_active_level" type="fraction">0%</item>
    <item name="lb_view_dimmed_level" type="fraction">60%</item>
    <item name="accessibility_action_clickable_span" type="id"/>
    <item name="accessibility_custom_action_0" type="id"/>
    <item name="accessibility_custom_action_1" type="id"/>
    <item name="accessibility_custom_action_10" type="id"/>
    <item name="accessibility_custom_action_11" type="id"/>
    <item name="accessibility_custom_action_12" type="id"/>
    <item name="accessibility_custom_action_13" type="id"/>
    <item name="accessibility_custom_action_14" type="id"/>
    <item name="accessibility_custom_action_15" type="id"/>
    <item name="accessibility_custom_action_16" type="id"/>
    <item name="accessibility_custom_action_17" type="id"/>
    <item name="accessibility_custom_action_18" type="id"/>
    <item name="accessibility_custom_action_19" type="id"/>
    <item name="accessibility_custom_action_2" type="id"/>
    <item name="accessibility_custom_action_20" type="id"/>
    <item name="accessibility_custom_action_21" type="id"/>
    <item name="accessibility_custom_action_22" type="id"/>
    <item name="accessibility_custom_action_23" type="id"/>
    <item name="accessibility_custom_action_24" type="id"/>
    <item name="accessibility_custom_action_25" type="id"/>
    <item name="accessibility_custom_action_26" type="id"/>
    <item name="accessibility_custom_action_27" type="id"/>
    <item name="accessibility_custom_action_28" type="id"/>
    <item name="accessibility_custom_action_29" type="id"/>
    <item name="accessibility_custom_action_3" type="id"/>
    <item name="accessibility_custom_action_30" type="id"/>
    <item name="accessibility_custom_action_31" type="id"/>
    <item name="accessibility_custom_action_4" type="id"/>
    <item name="accessibility_custom_action_5" type="id"/>
    <item name="accessibility_custom_action_6" type="id"/>
    <item name="accessibility_custom_action_7" type="id"/>
    <item name="accessibility_custom_action_8" type="id"/>
    <item name="accessibility_custom_action_9" type="id"/>
    <item name="fragment_container_view_tag" type="id"/>
    <item name="glide_custom_view_target_tag" type="id"/>
    <item name="is_pooling_container_tag" type="id"/>
    <item name="item_touch_helper_previous_elevation" type="id"/>
    <item name="lb_control_closed_captioning" type="id"/>
    <item name="lb_control_fast_forward" type="id"/>
    <item name="lb_control_fast_rewind" type="id"/>
    <item name="lb_control_high_quality" type="id"/>
    <item name="lb_control_more_actions" type="id"/>
    <item name="lb_control_picture_in_picture" type="id"/>
    <item name="lb_control_play_pause" type="id"/>
    <item name="lb_control_repeat" type="id"/>
    <item name="lb_control_shuffle" type="id"/>
    <item name="lb_control_skip_next" type="id"/>
    <item name="lb_control_skip_previous" type="id"/>
    <item name="lb_control_thumbs_down" type="id"/>
    <item name="lb_control_thumbs_up" type="id"/>
    <item name="lb_focus_animator" type="id"/>
    <item name="lb_guidedstep_background" type="id"/>
    <item name="lb_parallax_source" type="id"/>
    <item name="lb_shadow_impl" type="id"/>
    <item name="lb_slide_transition_value" type="id"/>
    <item name="line1" type="id"/>
    <item name="line3" type="id"/>
    <item name="pooling_container_listener_holder_tag" type="id"/>
    <item name="report_drawn" type="id"/>
    <item name="special_effects_controller_view_tag" type="id"/>
    <item name="tag_accessibility_actions" type="id"/>
    <item name="tag_accessibility_clickable_spans" type="id"/>
    <item name="tag_accessibility_heading" type="id"/>
    <item name="tag_accessibility_pane_title" type="id"/>
    <item name="tag_on_apply_window_listener" type="id"/>
    <item name="tag_on_receive_content_listener" type="id"/>
    <item name="tag_on_receive_content_mime_types" type="id"/>
    <item name="tag_screen_reader_focusable" type="id"/>
    <item name="tag_state_description" type="id"/>
    <item name="tag_transition_group" type="id"/>
    <item name="tag_unhandled_key_event_manager" type="id"/>
    <item name="tag_unhandled_key_listeners" type="id"/>
    <item name="tag_window_insets_animation_callback" type="id"/>
    <item name="text" type="id"/>
    <item name="text2" type="id"/>
    <item name="title" type="id"/>
    <item name="transitionPosition" type="id"/>
    <id name="view_tree_lifecycle_owner"/>
    <id name="view_tree_on_back_pressed_dispatcher_owner"/>
    <id name="view_tree_saved_state_registry_owner"/>
    <id name="view_tree_view_model_store_owner"/>
    <item name="visible_removing_fragment_view_tag" type="id"/>
    <integer name="cancel_button_image_alpha">127</integer>
    <integer name="lb_browse_headers_transition_delay">150</integer>
    <integer name="lb_browse_headers_transition_duration">250</integer>
    <integer name="lb_browse_rows_anim_duration">250</integer>
    <integer name="lb_card_activated_animation_duration">150</integer>
    <integer name="lb_card_selected_animation_delay">400</integer>
    <integer name="lb_card_selected_animation_duration">150</integer>
    <integer name="lb_details_description_body_max_lines">5</integer>
    <integer name="lb_details_description_body_min_lines">3</integer>
    <integer name="lb_details_description_subtitle_max_lines">1</integer>
    <integer name="lb_details_description_title_max_lines">2</integer>
    <integer name="lb_error_message_max_lines">3</integer>
    <integer name="lb_guidedactions_item_animation_duration">100</integer>
    <integer name="lb_guidedactions_item_description_min_lines">2</integer>
    <integer name="lb_guidedactions_item_title_max_lines">3</integer>
    <integer name="lb_guidedactions_item_title_min_lines">1</integer>
    <integer name="lb_guidedstep_activity_background_fade_duration_ms">350</integer>
    <integer name="lb_onboarding_header_description_delay">33</integer>
    <integer name="lb_onboarding_header_title_delay">33</integer>
    <integer name="lb_playback_bg_fade_in_ms">325</integer>
    <integer name="lb_playback_bg_fade_out_ms">500</integer>
    <integer name="lb_playback_controls_fade_in_ms">250</integer>
    <integer name="lb_playback_controls_fade_out_ms">325</integer>
    <integer name="lb_playback_controls_show_time_ms">3000</integer>
    <integer name="lb_playback_controls_tickle_timeout_ms">0</integer>
    <integer name="lb_playback_description_fade_in_ms">250</integer>
    <integer name="lb_playback_description_fade_out_ms">200</integer>
    <integer name="lb_playback_rows_fade_delay_ms">100</integer>
    <integer name="lb_playback_rows_fade_in_ms">150</integer>
    <integer name="lb_playback_rows_fade_out_ms">250</integer>
    <integer name="lb_search_bar_speech_mode_background_alpha">179</integer>
    <integer name="lb_search_bar_text_mode_background_alpha">51</integer>
    <item name="lb_search_orb_pulse_duration_ms" type="integer">1000</item>
    <item name="lb_search_orb_scale_duration_ms" type="integer">150</item>
    <integer name="slideEdgeEnd">5</integer>
    <integer name="slideEdgeStart">3</integer>
    <integer name="status_bar_notification_info_maxnum">999</integer>
    <string name="androidx_startup" translatable="false">androidx.startup</string>
    <string name="app_name">My Application TV</string>
    <string name="browse_title">Videos by Your Company</string>
    <string name="buy_1">Buy and Own</string>
    <string name="buy_2">AT $9.99</string>
    <string name="call_notification_answer_action">Answer</string>
    <string name="call_notification_answer_video_action">Video</string>
    <string name="call_notification_decline_action">Decline</string>
    <string name="call_notification_hang_up_action">Hang Up</string>
    <string name="call_notification_incoming_text">Incoming call</string>
    <string name="call_notification_ongoing_text">Ongoing call</string>
    <string name="call_notification_screening_text">Screening an incoming call</string>
    <string name="dismiss_error">Dismiss</string>
    <string name="error_fragment">Error Fragment</string>
    <string name="error_fragment_message">An error occurred</string>
    <string name="grid_view">Grid View</string>
    <string name="lb_control_display_fast_forward_multiplier">%1$dX</string>
    <string name="lb_control_display_rewind_multiplier">%1$dX</string>
    <string name="lb_guidedaction_continue_title">Continue</string>
    <string name="lb_guidedaction_finish_title">Finish</string>
    <string name="lb_media_player_error">MediaPlayer error code %1$d extra %2$d</string>
    <string name="lb_navigation_menu_contentDescription">Navigation menu</string>
    <string name="lb_onboarding_accessibility_next">Next</string>
    <string name="lb_onboarding_get_started">GET STARTED</string>
    <string name="lb_playback_controls_closed_captioning_disable">Disable Closed Captioning</string>
    <string name="lb_playback_controls_closed_captioning_enable">Enable Closed Captioning</string>
    <string name="lb_playback_controls_fast_forward">Fast Forward</string>
    <string name="lb_playback_controls_fast_forward_multiplier">Fast Forward %1$dX</string>
    <string name="lb_playback_controls_hidden">Media controls hidden, press d-pad to show</string>
    <string name="lb_playback_controls_high_quality_disable">Disable High Quality</string>
    <string name="lb_playback_controls_high_quality_enable">Enable High Quality</string>
    <string name="lb_playback_controls_more_actions">More Actions</string>
    <string name="lb_playback_controls_pause">Pause</string>
    <string name="lb_playback_controls_picture_in_picture">Enter Picture In Picture Mode</string>
    <string name="lb_playback_controls_play">Play</string>
    <string name="lb_playback_controls_repeat_all">Repeat All</string>
    <string name="lb_playback_controls_repeat_none">Repeat None</string>
    <string name="lb_playback_controls_repeat_one">Repeat One</string>
    <string name="lb_playback_controls_rewind">Rewind</string>
    <string name="lb_playback_controls_rewind_multiplier">Rewind %1$dX</string>
    <string name="lb_playback_controls_shown">Media controls shown</string>
    <string name="lb_playback_controls_shuffle_disable">Disable Shuffle</string>
    <string name="lb_playback_controls_shuffle_enable">Enable Shuffle</string>
    <string name="lb_playback_controls_skip_next">Skip Next</string>
    <string name="lb_playback_controls_skip_previous">Skip Previous</string>
    <string name="lb_playback_controls_thumb_down">Deselect Thumb Down</string>
    <string name="lb_playback_controls_thumb_down_outline">Select Thumb Down</string>
    <string name="lb_playback_controls_thumb_up">Deselect Thumb Up</string>
    <string name="lb_playback_controls_thumb_up_outline">Select Thumb Up</string>
    <string name="lb_playback_time_separator">/</string>
    <string name="lb_search_bar_hint">Search</string>
    <string name="lb_search_bar_hint_speech">Speak to search</string>
    <string name="lb_search_bar_hint_with_title">Search <ns1:g id="search context">%1$s</ns1:g></string>
    <string name="lb_search_bar_hint_with_title_speech">Speak to search <ns1:g id="search context">%1$s</ns1:g></string>
    <string name="movie">Movie</string>
    <string name="orb_search_action">Search Action</string>
    <string name="personal_settings">Personal Settings</string>
    <string name="related_movies">Related Videos</string>
    <string name="rent_1">Rent By Day</string>
    <string name="rent_2">From $1.99</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="watch_trailer_1">Watch trailer</string>
    <string name="watch_trailer_2">FREE</string>
    <style name="Base.CardView" parent="android:Widget">
        <item name="cardCornerRadius">@dimen/cardview_default_radius</item>
        <item name="cardElevation">@dimen/cardview_default_elevation</item>
        <item name="cardMaxElevation">@dimen/cardview_default_elevation</item>
        <item name="cardUseCompatPadding">false</item>
        <item name="cardPreventCornerOverlap">true</item>
    </style>
    <style name="CardView" parent="Base.CardView">
    </style>
    <style name="CardView.Dark">
        <item name="cardBackgroundColor">@color/cardview_dark_background</item>
    </style>
    <style name="CardView.Light">
        <item name="cardBackgroundColor">@color/cardview_light_background</item>
    </style>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent"/>
    <style name="TextAppearance.Compat.Notification.Info">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Info.Media"/>
    <style name="TextAppearance.Compat.Notification.Line2" parent="TextAppearance.Compat.Notification.Info"/>
    <style name="TextAppearance.Compat.Notification.Line2.Media" parent="TextAppearance.Compat.Notification.Info.Media"/>
    <style name="TextAppearance.Compat.Notification.Media"/>
    <style name="TextAppearance.Compat.Notification.Time">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Time.Media"/>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title"/>
    <style name="TextAppearance.Compat.Notification.Title.Media"/>
    <style name="TextAppearance.Leanback" parent="TextAppearance.LeanbackBase">
        
        <item name="android:fontFamily">sans-serif-condensed</item>
    </style>
    <style name="TextAppearance.Leanback.DetailsActionButton">
        <item name="android:textSize">@dimen/lb_action_text_size</item>
        <item name="android:textColor">@color/lb_action_text_color</item>
        <item name="android:textAllCaps">true</item>
    </style>
    <style name="TextAppearance.Leanback.DetailsDescriptionBody">
        <item name="android:textSize">@dimen/lb_details_description_body_text_size</item>
        <item name="android:textColor">@color/lb_details_description_body_color</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.Leanback.DetailsDescriptionSubtitle">
        <item name="android:textSize">@dimen/lb_details_description_subtitle_text_size</item>
        <item name="android:textColor">@color/lb_details_description_color</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.Leanback.DetailsDescriptionTitle">
        <item name="android:textSize">@dimen/lb_details_description_title_text_size</item>
        <item name="android:textColor">@color/lb_details_description_color</item>
        <item name="android:fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.Leanback.ErrorMessage">
        <item name="android:textSize">@dimen/lb_error_message_text_size</item>
        <item name="android:textColor">@color/lb_error_message</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.Leanback.Header" parent="TextAppearance.Leanback">
        <item name="android:textSize">@dimen/lb_browse_header_text_size</item>
        <item name="android:textColor">@color/lb_browse_header_color</item>
    </style>
    <style name="TextAppearance.Leanback.Header.Section">
        <item name="android:textColor">?defaultSectionHeaderColor</item>
        <item name="android:textSize">@dimen/lb_browse_section_header_text_size</item>
    </style>
    <style name="TextAppearance.Leanback.ImageCardView">
    </style>
    <style name="TextAppearance.Leanback.ImageCardView.Content">
        <item name="android:textColor">@color/lb_basic_card_content_text_color</item>
        <item name="android:textSize">@dimen/lb_basic_card_content_text_size</item>
    </style>
    <style name="TextAppearance.Leanback.ImageCardView.Title">
        <item name="android:textColor">@color/lb_basic_card_title_text_color</item>
        <item name="android:textSize">@dimen/lb_basic_card_title_text_size</item>
    </style>
    <style name="TextAppearance.Leanback.PlaybackControlLabel">
        <item name="android:textSize">@dimen/lb_control_button_text_size</item>
        <item name="android:textColor">@color/lb_control_button_text</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.Leanback.PlaybackControlsTime">
        <item name="android:textSize">@dimen/lb_playback_controls_time_text_size</item>
        <item name="android:textColor">@color/lb_playback_controls_time_text_color</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.Leanback.PlaybackMediaItemDuration">
        <item name="android:textColor">#80FFFFFF</item>
        <item name="android:textSize">18sp</item>
        <item name="android:fontFamily">sans-serif-regular</item>
    </style>
    <style name="TextAppearance.Leanback.PlaybackMediaItemName">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">18sp</item>
        <item name="android:fontFamily">sans-serif-regular</item>
    </style>
    <style name="TextAppearance.Leanback.PlaybackMediaItemNumber">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">18sp</item>
        <item name="android:fontFamily">sans-serif-regular</item>
    </style>
    <style name="TextAppearance.Leanback.PlaybackMediaListHeaderTitle">
        <item name="android:textColor">#80EEEEEE</item>
        <item name="android:textSize">18sp</item>
        <item name="android:fontFamily">sans-serif-regular</item>
    </style>
    <style name="TextAppearance.Leanback.Row.Header" parent="TextAppearance.Leanback.Header">
    </style>
    <style name="TextAppearance.Leanback.Row.Header.Description" parent="TextAppearance.Leanback.Header">
        <item name="android:textSize">@dimen/lb_browse_header_description_text_size</item>
        <item name="android:textColor">@color/lb_browse_header_description_color</item>
    </style>
    <style name="TextAppearance.Leanback.Row.HoverCardDescription" parent="TextAppearance.Leanback">
        <item name="android:textSize">@dimen/lb_browse_row_hovercard_description_font_size</item>
    </style>
    <style name="TextAppearance.Leanback.Row.HoverCardTitle" parent="TextAppearance.Leanback">
        <item name="android:textSize">@dimen/lb_browse_row_hovercard_title_font_size</item>
    </style>
    <style name="TextAppearance.Leanback.SearchTextEdit" parent="TextAppearance.Leanback">
        <item name="android:textSize">@dimen/lb_search_bar_text_size</item>
    </style>
    <style name="TextAppearance.Leanback.Title" parent="TextAppearance.Leanback">
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:textSize">@dimen/lb_browse_title_text_size</item>
        <item name="android:textColor">@color/lb_browse_title_color</item>
    </style>
    <style name="TextAppearance.LeanbackBase" parent="android:TextAppearance.Holo">
    </style>
    <style name="Theme.Leanback" parent="Theme.LeanbackBase">

        <item name="baseCardViewStyle">@style/Widget.Leanback.BaseCardViewStyle</item>
        <item name="imageCardViewStyle">@style/Widget.Leanback.ImageCardViewStyle</item>
        <item name="imageCardViewImageStyle">@style/Widget.Leanback.ImageCardView.ImageStyle</item>
        <item name="imageCardViewTitleStyle">@style/Widget.Leanback.ImageCardView.TitleStyle</item>
        <item name="imageCardViewContentStyle">@style/Widget.Leanback.ImageCardView.ContentStyle</item>
        <item name="imageCardViewBadgeStyle">@style/Widget.Leanback.ImageCardView.BadgeStyle</item>
        <item name="imageCardViewInfoAreaStyle">@style/Widget.Leanback.ImageCardView.InfoAreaStyle</item>

        <item name="browsePaddingStart">@dimen/lb_browse_padding_start</item>
        <item name="browsePaddingEnd">@dimen/lb_browse_padding_end</item>
        <item name="browsePaddingTop">@dimen/lb_browse_padding_top</item>
        <item name="browsePaddingBottom">@dimen/lb_browse_padding_bottom</item>
        <item name="browseRowsMarginStart">@dimen/lb_browse_rows_margin_start</item>
        <item name="browseRowsMarginTop">@dimen/lb_browse_rows_margin_top</item>
        <item name="browseRowsFadingEdgeLength">@dimen/lb_browse_rows_fading_edge</item>

        <item name="headersVerticalGridStyle">@style/Widget.Leanback.Headers.VerticalGridView</item>
        <item name="headerStyle">@style/Widget.Leanback.Header</item>
        <item name="sectionHeaderStyle">@style/Widget.Leanback.Header.Section</item>

        <item name="rowsVerticalGridStyle">@style/Widget.Leanback.Rows.VerticalGridView</item>
        <item name="rowHorizontalGridStyle">@style/Widget.Leanback.Row.HorizontalGridView</item>
        <item name="itemsVerticalGridStyle">@style/Widget.Leanback.GridItems.VerticalGridView</item>

        <item name="browseTitleViewLayout">@layout/lb_browse_title</item>
        <item name="browseTitleTextStyle">@style/Widget.Leanback.Title.Text</item>
        <item name="browseTitleIconStyle">@style/Widget.Leanback.Title.Icon</item>
        <item name="browseTitleViewStyle">@style/Widget.Leanback.TitleView</item>

        <item name="rowHeaderStyle">@style/Widget.Leanback.Row.Header</item>
        <item name="rowHeaderDescriptionStyle">@style/Widget.Leanback.Row.Header.Description</item>
        <item name="rowHoverCardTitleStyle">@style/Widget.Leanback.Row.HoverCardTitle</item>
        <item name="rowHoverCardDescriptionStyle">@style/Widget.Leanback.Row.HoverCardDescription</item>
        <item name="rowHeaderDockStyle">@style/Widget.Leanback.Row.HeaderDock</item>

        <item name="searchOrbViewStyle">@style/Widget.Leanback.SearchOrbViewStyle</item>


        <item name="detailsDescriptionTitleStyle">@style/Widget.Leanback.DetailsDescriptionTitleStyle</item>
        <item name="detailsDescriptionSubtitleStyle">@style/Widget.Leanback.DetailsDescriptionSubtitleStyle</item>
        <item name="detailsDescriptionBodyStyle">@style/Widget.Leanback.DetailsDescriptionBodyStyle</item>
        <item name="detailsActionButtonStyle">@style/Widget.Leanback.DetailsActionButtonStyle</item>
        
        <item name="playbackPaddingStart">@dimen/lb_playback_controls_margin_start</item>
        <item name="playbackPaddingEnd">@dimen/lb_playback_controls_margin_end</item>
        <item name="playbackMediaItemPaddingStart">@dimen/lb_playback_media_row_horizontal_padding</item>

        <item name="playbackMediaListHeaderStyle">@style/Widget.Leanback.PlaybackMediaListHeaderStyle</item>
        <item name="playbackMediaItemRowStyle">@style/Widget.Leanback.PlaybackMediaItemRowStyle</item>
        <item name="playbackMediaItemSeparatorStyle">@style/Widget.Leanback.PlaybackMediaItemSeparatorStyle</item>
        <item name="playbackMediaListHeaderTitleStyle">@style/Widget.Leanback.PlaybackMediaListHeaderTitleStyle</item>
        <item name="playbackMediaItemDetailsStyle">@style/Widget.Leanback.PlaybackMediaItemDetailsStyle</item>
        <item name="playbackMediaItemNumberViewFlipperStyle">@style/Widget.Leanback.PlaybackMediaItemNumberViewFlipperStyle</item>
        <item name="playbackMediaItemNumberViewFlipperLayout">@layout/lb_media_item_number_view_flipper</item>
        <item name="playbackMediaItemNumberStyle">@style/Widget.Leanback.PlaybackMediaItemNumberStyle</item>
        <item name="playbackMediaItemNameStyle">@style/Widget.Leanback.PlaybackMediaItemNameStyle</item>
        <item name="playbackMediaItemDurationStyle">@style/Widget.Leanback.PlaybackMediaItemDurationStyle</item>

        <item name="playbackControlsButtonStyle">@style/Widget.Leanback.PlaybackControlsButtonStyle</item>
        <item name="playbackControlButtonLabelStyle">@style/Widget.Leanback.PlaybackControlLabelStyle</item>
        <item name="playbackControlsTimeStyle">@style/Widget.Leanback.PlaybackControlsTimeStyle</item>
        <item name="playbackControlsActionIcons">@style/Widget.Leanback.PlaybackControlsActionIconsStyle</item>
        <item name="playbackControlsAutoHideTimeout">@integer/lb_playback_controls_show_time_ms</item>
        <item name="playbackControlsAutoHideTickleTimeout">@integer/lb_playback_controls_tickle_timeout_ms</item>

        <item name="errorMessageStyle">@style/Widget.Leanback.ErrorMessageStyle</item>

        <item name="defaultSearchColor">@color/lb_default_search_color</item>
        <item name="defaultSearchIconColor">@color/lb_default_search_icon_color</item>
        <item name="defaultSearchBrightColor">?attr/defaultSearchColor</item>
        <item name="defaultSearchIcon">@drawable/lb_ic_in_app_search</item>

        <item name="defaultSectionHeaderColor">?attr/defaultSearchColor</item>

        <item name="overlayDimMaskColor">@color/lb_view_dim_mask_color</item>
        <item name="overlayDimActiveLevel">@fraction/lb_view_active_level</item>
        <item name="overlayDimDimmedLevel">@fraction/lb_view_dimmed_level</item>

    </style>
    <style name="Theme.Leanback.Browse" parent="Theme.Leanback">
    </style>
    <style name="Theme.Leanback.Details" parent="Theme.Leanback">
    </style>
    <style name="Theme.Leanback.Details.NoSharedElementTransition">
    </style>
    <style name="Theme.Leanback.GuidedStep" parent="Theme.Leanback.GuidedStepBase">
        <item name="guidedStepThemeFlag">true</item>
        <item name="guidedStepHeightWeight">@dimen/lb_guidedstep_height_weight</item>


        
        <item name="guidedStepBackground">?android:attr/colorBackground</item>
        
        <item name="android:windowBackground">@android:color/transparent</item>

        <item name="guidedStepImeAppearingAnimation">@animator/lb_guidedstep_slide_up</item>
        <item name="guidedStepImeDisappearingAnimation">@animator/lb_guidedstep_slide_down</item>

        <item name="guidanceContainerStyle">@style/Widget.Leanback.GuidanceContainerStyle</item>
        <item name="guidanceIconStyle">@style/Widget.Leanback.GuidanceIconStyle</item>
        <item name="guidanceTitleStyle">@style/Widget.Leanback.GuidanceTitleStyle</item>
        <item name="guidanceBreadcrumbStyle">@style/Widget.Leanback.GuidanceBreadcrumbStyle</item>
        <item name="guidanceDescriptionStyle">@style/Widget.Leanback.GuidanceDescriptionStyle</item>

        <item name="guidedActionsElevation">@dimen/lb_guidedactions_elevation</item>
        <item name="guidedActionsBackground">@color/lb_guidedactions_background</item>
        <item name="guidedActionsBackgroundDark">@color/lb_guidedactions_background_dark</item>
        <item name="guidedActionsListStyle">@style/Widget.Leanback.GuidedActionsListStyle</item>
        <item name="guidedSubActionsListStyle">@style/Widget.Leanback.GuidedSubActionsListStyle</item>
        <item name="guidedButtonActionsListStyle">@style/Widget.Leanback.GuidedButtonActionsListStyle</item>

        <item name="guidedActionItemContainerStyle">@style/Widget.Leanback.GuidedActionItemContainerStyle</item>
        <item name="guidedActionItemCheckmarkStyle">@style/Widget.Leanback.GuidedActionItemCheckmarkStyle</item>
        <item name="guidedActionItemIconStyle">@style/Widget.Leanback.GuidedActionItemIconStyle</item>
        <item name="guidedActionItemContentStyle">@style/Widget.Leanback.GuidedActionItemContentStyle</item>
        <item name="guidedActionItemTitleStyle">@style/Widget.Leanback.GuidedActionItemTitleStyle</item>
        <item name="guidedActionItemDescriptionStyle">@style/Widget.Leanback.GuidedActionItemDescriptionStyle</item>
        <item name="guidedActionItemChevronStyle">@style/Widget.Leanback.GuidedActionItemChevronStyle</item>

        <item name="guidedActionPressedAnimation">@animator/lb_guidedactions_item_pressed</item>
        <item name="guidedActionUnpressedAnimation">@animator/lb_guidedactions_item_unpressed</item>
        <item name="guidedActionEnabledChevronAlpha">@dimen/lb_guidedactions_item_enabled_chevron_alpha</item>
        <item name="guidedActionDisabledChevronAlpha">@dimen/lb_guidedactions_item_disabled_chevron_alpha</item>
        <item name="guidedActionContentWidthWeight">@dimen/lb_guidedactions_width_weight</item>
        <item name="guidedActionContentWidthWeightTwoPanels">@dimen/lb_guidedactions_width_weight_two_panels</item>
        <item name="guidedButtonActionsWidthWeight">@dimen/lb_guidedbuttonactions_width_weight</item>
        <item name="guidedActionTitleMinLines">@integer/lb_guidedactions_item_title_min_lines</item>
        <item name="guidedActionTitleMaxLines">@integer/lb_guidedactions_item_title_max_lines</item>
        <item name="guidedActionDescriptionMinLines">@integer/lb_guidedactions_item_description_min_lines</item>
        <item name="guidedActionVerticalPadding">@dimen/lb_guidedactions_vertical_padding</item>
        <item name="guidedStepKeyline">@dimen/lb_guidedstep_keyline</item>
    </style>
    <style name="Theme.Leanback.GuidedStep.Half" parent="Theme.Leanback.GuidedStep.HalfBase">
      <item name="guidedStepHeightWeight">@dimen/lb_guidedstep_height_weight_translucent</item>
      <item name="android:windowIsTranslucent">true</item>
      <item name="android:windowBackground">@android:color/transparent</item>
    </style>
    <style name="Theme.Leanback.GuidedStep.HalfBase" parent="Theme.Leanback.GuidedStep">
    </style>
    <style name="Theme.Leanback.GuidedStepBase" parent="Theme.LeanbackBase">
        <item name="guidedActionsSelectorDrawable">?android:attr/selectableItemBackground</item>
    </style>
    <style name="Theme.Leanback.Onboarding" parent="Theme.LeanbackBase">
        <item name="onboardingHeaderStyle">@style/Widget.Leanback.OnboardingHeaderStyle</item>
        <item name="onboardingTitleStyle">@style/Widget.Leanback.OnboardingTitleStyle</item>
        <item name="onboardingDescriptionStyle">@style/Widget.Leanback.OnboardingDescriptionStyle</item>
        <item name="onboardingNavigatorContainerStyle">@style/Widget.Leanback.OnboardingNavigatorContainerStyle</item>
        <item name="onboardingPageIndicatorStyle">@style/Widget.Leanback.OnboardingPageIndicatorStyle</item>
        <item name="onboardingStartButtonStyle">@style/Widget.Leanback.OnboardingStartButtonStyle</item>
        <item name="onboardingLogoStyle">@style/Widget.Leanback.OnboardingLogoStyle</item>
        <item name="onboardingMainIconStyle">@style/Widget.Leanback.OnboardingMainIconStyle</item>
    </style>
    <style name="Theme.Leanback.VerticalGrid" parent="Theme.Leanback">
    </style>
    <style name="Theme.LeanbackBase" parent="android:Theme.Holo.NoActionBar">
        <item name="playbackProgressPrimaryColor">@color/lb_playback_progress_color_no_theme</item>
        <item name="playbackProgressSecondaryColor">@color/lb_playback_progress_secondary_color_no_theme</item>
        <item name="playbackControlsIconHighlightColor">@color/lb_playback_icon_highlight_no_theme</item>
        <item name="defaultBrandColor">@color/lb_default_brand_color</item>
        <item name="defaultBrandColorDark">@color/lb_default_brand_color_dark</item>

        <item name="guidedStepTheme">@style/Theme.Leanback.GuidedStep</item>
    </style>
    <style name="Theme.MyApplicationTV" parent="@style/Theme.Leanback"/>
    <style name="Widget.Compat.NotificationActionContainer" parent=""/>
    <style name="Widget.Compat.NotificationActionText" parent=""/>
    <style name="Widget.Leanback" parent="Widget.LeanbackBase"/>
    <style name="Widget.Leanback.BaseCardViewStyle">
        <item name="cardForeground">@drawable/lb_card_foreground</item>
        <item name="cardBackground">@color/lb_basic_card_bg_color</item>
    </style>
    <style name="Widget.Leanback.DetailsActionButtonStyle" parent="Widget.Leanback.DetailsActionButtonStyleBase">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.DetailsActionButton</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:drawablePadding">@dimen/lb_action_icon_margin</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="android:paddingStart">@dimen/lb_action_padding_horizontal</item>
        <item name="android:paddingEnd">@dimen/lb_action_padding_horizontal</item>
    </style>
    <style name="Widget.Leanback.DetailsActionButtonStyleBase" parent="android:Widget.Holo.Button.Borderless">
    </style>
    <style name="Widget.Leanback.DetailsDescriptionBodyStyle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.DetailsDescriptionBody</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:ellipsize">end</item>
    </style>
    <style name="Widget.Leanback.DetailsDescriptionSubtitleStyle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.DetailsDescriptionSubtitle</item>
        <item name="android:maxLines">@integer/lb_details_description_subtitle_max_lines</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:ellipsize">end</item>
    </style>
    <style name="Widget.Leanback.DetailsDescriptionTitleStyle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.DetailsDescriptionTitle</item>
        <item name="android:maxLines">@integer/lb_details_description_title_max_lines</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:ellipsize">end</item>
        <item name="resizeTrigger">maxLines</item>
        <item name="resizedTextSize">@dimen/lb_details_description_title_resized_text_size</item>
        <item name="resizedPaddingAdjustmentTop">@dimen/lb_details_description_title_padding_adjust_top</item>
        <item name="resizedPaddingAdjustmentBottom">@dimen/lb_details_description_title_padding_adjust_bottom</item>
    </style>
    <style name="Widget.Leanback.ErrorMessageStyle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.ErrorMessage</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:maxLines">@integer/lb_error_message_max_lines</item>
        <item name="android:ellipsize">end</item>
    </style>
    <style name="Widget.Leanback.GridItems"/>
    <style name="Widget.Leanback.GridItems.VerticalGridView">
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="android:paddingStart">?attr/browsePaddingStart</item>
        <item name="android:paddingEnd">?attr/browsePaddingEnd</item>
        <item name="android:paddingBottom">@dimen/lb_vertical_grid_padding_bottom</item>
        <item name="android:paddingTop">?attr/browseRowsMarginTop</item>
        <item name="android:gravity">center_horizontal</item>
        <item name="android:horizontalSpacing">@dimen/lb_browse_item_horizontal_spacing</item>
        <item name="android:verticalSpacing">@dimen/lb_browse_item_vertical_spacing</item>
        <item name="focusOutFront">true</item>
    </style>
    <style name="Widget.Leanback.GuidanceBreadcrumbStyle">
        <item name="android:importantForAccessibility">no</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_toEndOf">@id/guidance_icon</item>
        <item name="android:ellipsize">end</item>
        <item name="android:fontFamily">sans-serif-condensed</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">#88F1F1F1</item>
        <item name="android:textSize">18sp</item>
        <item name="android:gravity">start</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="Widget.Leanback.GuidanceContainerStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:paddingStart">56dp</item>
        <item name="android:paddingEnd">32dp</item>
        <item name="android:clipToPadding">false</item>
    </style>
    <style name="Widget.Leanback.GuidanceDescriptionStyle">
        <item name="android:importantForAccessibility">no</item>
        <item name="android:layout_below">@id/guidance_title</item>
        <item name="android:layout_toEndOf">@id/guidance_icon</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignWithParentIfMissing">true</item>
        <item name="android:ellipsize">end</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:gravity">start</item>
        <item name="android:maxLines">6</item>
        <item name="android:textColor">#88F1F1F1</item>
        <item name="android:textSize">14sp</item>
        <item name="android:lineSpacingExtra">3dp</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="Widget.Leanback.GuidanceIconStyle">
        <item name="android:layout_width">140dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:layout_marginEnd">24dp</item>
        <item name="android:maxHeight">280dp</item>
        <item name="android:scaleType">fitCenter</item>
    </style>
    <style name="Widget.Leanback.GuidanceTitleStyle">
        <item name="android:importantForAccessibility">no</item>
        <item name="android:layout_below">@id/guidance_breadcrumb</item>
        <item name="android:layout_toEndOf">@id/guidance_icon</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:ellipsize">end</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:gravity">start</item>
        <item name="android:maxLines">2</item>
        <item name="android:textColor">#FFF1F1F1</item>
        <item name="android:textSize">36sp</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:paddingBottom">4dp</item>
        <item name="android:paddingTop">2dp</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemCheckmarkStyle">
        <item name="android:layout_width">@dimen/lb_guidedactions_item_checkmark_diameter</item>
        <item name="android:layout_height">@dimen/lb_guidedactions_item_checkmark_diameter</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginEnd">@dimen/lb_guidedactions_item_delimiter_padding</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemChevronStyle">
        <item name="android:layout_width">12dp</item>
        <item name="android:layout_height">12dp</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginStart">@dimen/lb_guidedactions_item_delimiter_padding</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:src">@drawable/lb_ic_guidedactions_item_chevron</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemContainerStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:foreground">?attr/guidedActionsSelectorDrawable</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="android:paddingBottom">@dimen/lb_guidedactions_item_bottom_padding</item>
        <item name="android:paddingStart">@dimen/lb_guidedactions_item_start_padding</item>
        <item name="android:paddingEnd">@dimen/lb_guidedactions_item_end_padding</item>
        <item name="android:paddingTop">@dimen/lb_guidedactions_item_top_padding</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemContentStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">start|center_vertical</item>
        <item name="android:layout_weight">1</item>
        <item name="android:orientation">vertical</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemDescriptionStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:alpha">@dimen/lb_guidedactions_item_unselected_description_text_alpha</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_marginTop">@dimen/lb_guidedactions_item_space_between_title_and_description</item>
        <item name="android:fontFamily">sans-serif-condensed</item>
        <item name="android:maxLines">@integer/lb_guidedactions_item_description_min_lines</item>
        <item name="android:textColor">@color/lb_guidedactions_item_unselected_text_color</item>
        <item name="android:textSize">@dimen/lb_guidedactions_item_description_font_size</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemIconStyle">
        <item name="android:layout_width">@dimen/lb_guidedactions_item_icon_width</item>
        <item name="android:layout_height">@dimen/lb_guidedactions_item_icon_height</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginEnd">@dimen/lb_guidedactions_item_delimiter_padding</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemTitleStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:alpha">@dimen/lb_guidedactions_item_unselected_text_alpha</item>
        <item name="android:ellipsize">end</item>
        <item name="android:fontFamily">sans-serif-condensed</item>
        <item name="android:maxLines">@integer/lb_guidedactions_item_title_min_lines</item>
        <item name="android:textColor">@color/lb_guidedactions_item_unselected_text_color</item>
        <item name="android:textSize">@dimen/lb_guidedactions_item_title_font_size</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="Widget.Leanback.GuidedActionsContainerStyle"/>
    <style name="Widget.Leanback.GuidedActionsListStyle">
        <item name="android:focusable">false</item>
        <item name="android:focusableInTouchMode">false</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:verticalSpacing">@dimen/lb_guidedactions_list_vertical_spacing</item>
        <item name="android:paddingStart">@dimen/lb_guidedactions_list_padding_start</item>
        <item name="android:paddingEnd">@dimen/lb_guidedactions_list_padding_end</item>
        <item name="focusOutEnd">false</item>
        <item name="focusOutFront">false</item>
    </style>
    <style name="Widget.Leanback.GuidedActionsSelectorStyle"/>
    <style name="Widget.Leanback.GuidedButtonActionsListStyle" parent="Widget.Leanback.GuidedActionsListStyle">
    </style>
    <style name="Widget.Leanback.GuidedSubActionsListStyle" parent="Widget.Leanback.GuidedActionsListStyle">
        <item name="android:paddingTop">@dimen/lb_guidedactions_sublist_padding_top</item>
        <item name="android:paddingBottom">@dimen/lb_guidedactions_sublist_padding_bottom</item>
        <item name="android:clipToPadding">false</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="focusOutSideStart">false</item>
        <item name="focusOutSideEnd">false</item>
        <item name="android:layout_marginBottom">@dimen/lb_guidedactions_sublist_bottom_margin</item>
    </style>
    <style name="Widget.Leanback.Header">
        <item name="android:minHeight">@dimen/lb_browse_header_height</item>
        <item name="android:minWidth">1dp</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Header</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:singleLine">false</item>
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">none</item>
    </style>
    <style name="Widget.Leanback.Header.Section">
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Header.Section</item>
        <item name="android:singleLine">true</item>
    </style>
    <style name="Widget.Leanback.Headers"/>
    <style name="Widget.Leanback.Headers.VerticalGridView">
        <item name="android:background">?attr/defaultBrandColor</item>
        <item name="android:paddingStart">?attr/browsePaddingStart</item>
        <item name="focusOutFront">true</item>
        <item name="focusOutEnd">true</item>
        <item name="android:verticalSpacing">@dimen/lb_browse_headers_vertical_spacing</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="android:contentDescription">@string/lb_navigation_menu_contentDescription</item>
    </style>
    <style name="Widget.Leanback.ImageCardView"/>
    <style name="Widget.Leanback.ImageCardView.BadgeStyle">
        <item name="android:id">@id/extra_badge</item>
        <item name="android:layout_width">@dimen/lb_basic_card_info_badge_size</item>
        <item name="android:layout_height">@dimen/lb_basic_card_info_badge_size</item>
        <item name="android:contentDescription">@null</item>
        <item name="android:scaleType">fitCenter</item>
    </style>
    <style name="Widget.Leanback.ImageCardView.ContentStyle">
        <item name="android:id">@id/content_text</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:layout_below">@+id/title_text</item>
        <item name="android:layout_toStartOf">@+id/extra_badge</item>
        <item name="android:maxLines">1</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.ImageCardView.Content</item>
    </style>
    <style name="Widget.Leanback.ImageCardView.ImageStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:adjustViewBounds">true</item>
        <item name="android:contentDescription">@null</item>
        <item name="android:scaleType">centerCrop</item>
        <item name="layout_viewType">main</item>
    </style>
    <style name="Widget.Leanback.ImageCardView.InfoAreaStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="layout_viewType">info</item>
        <item name="android:paddingBottom">@dimen/lb_basic_card_info_padding_bottom</item>
        <item name="android:paddingEnd">@dimen/lb_basic_card_info_padding_horizontal</item>
        <item name="android:paddingStart">@dimen/lb_basic_card_info_padding_horizontal</item>
        <item name="android:paddingTop">@dimen/lb_basic_card_info_padding_top</item>
        <item name="android:background">@color/lb_basic_card_info_bg_color</item>
    </style>
    <style name="Widget.Leanback.ImageCardView.TitleStyle">
        <item name="android:id">@id/title_text</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:maxLines">1</item>
        <item name="android:layout_marginBottom">@dimen/lb_basic_card_info_text_margin</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.ImageCardView.Title</item>
    </style>
    <style name="Widget.Leanback.ImageCardViewStyle" parent="Widget.Leanback.BaseCardViewStyle">
        <item name="cardType">infoUnder</item>
        <item name="infoVisibility">activated</item>
        
        <item name="lbImageCardViewType">Title|Content|IconOnRight</item>
        
        <item name="infoAreaBackground">@null</item>
    </style>
    <style name="Widget.Leanback.OnboardingDescriptionStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">0.5</item>
        <item name="android:layout_marginTop">3dp</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">#B3EEEEEE</item>
        <item name="android:textSize">14sp</item>
        <item name="android:lineSpacingExtra">10sp</item>
    </style>
    <style name="Widget.Leanback.OnboardingHeaderStyle">
        <item name="android:layout_width">@dimen/lb_onboarding_content_width</item>
        <item name="android:layout_height">@dimen/lb_onboarding_header_height</item>
        <item name="android:layout_alignParentTop">true</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:layout_marginTop">@dimen/lb_onboarding_header_margin_top</item>
        <item name="android:clipChildren">false</item>
        <item name="android:clipToPadding">false</item>
        <item name="android:orientation">vertical</item>
    </style>
    <style name="Widget.Leanback.OnboardingLogoStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerInParent">true</item>
        <item name="android:contentDescription">@null</item>
    </style>
    <style name="Widget.Leanback.OnboardingMainIconStyle">
        <item name="android:layout_width">64dp</item>
        <item name="android:layout_height">64dp</item>
        <item name="android:layout_above">@id/page_container</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:layout_marginBottom">16dp</item>
        <item name="android:contentDescription">@null</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.Leanback.OnboardingNavigatorContainerStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">58dp</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:layout_alignParentBottom">true</item>
    </style>
    <style name="Widget.Leanback.OnboardingPageIndicatorStyle">
        <item name="android:layout_width">@dimen/lb_onboarding_content_width</item>
        <item name="android:layout_height">@dimen/lb_onboarding_navigation_height</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:focusable">true</item>
        <item name="android:contentDescription">@string/lb_onboarding_accessibility_next</item>
        <item name="lbDotRadius">@dimen/lb_page_indicator_dot_radius</item>
        <item name="arrowRadius">@dimen/lb_page_indicator_arrow_radius</item>
        <item name="dotToDotGap">@dimen/lb_page_indicator_dot_gap</item>
        <item name="dotToArrowGap">@dimen/lb_page_indicator_arrow_gap</item>
        <item name="dotBgColor">@color/lb_page_indicator_dot</item>
        <item name="arrowBgColor">@color/lb_page_indicator_arrow_background</item>
    </style>
    <style name="Widget.Leanback.OnboardingStartButtonStyle" parent="Widget.Leanback.OnboardingStartButtonStyleBase">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">36dp</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:layout_marginBottom">4dp</item>
        <item name="android:background">@drawable/lb_onboarding_start_button_background</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:text">@string/lb_onboarding_get_started</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">#014269</item>
        <item name="android:textSize">16sp</item>
    </style>
    <style name="Widget.Leanback.OnboardingStartButtonStyleBase">
    </style>
    <style name="Widget.Leanback.OnboardingTitleStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">0.5</item>
        <item name="android:layout_marginBottom">3dp</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">#EEEEEE</item>
        <item name="android:textSize">34sp</item>
        <item name="android:lineSpacingExtra">14sp</item>
    </style>
    <style name="Widget.Leanback.PlaybackControlLabelStyle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.PlaybackControlLabel</item>
    </style>
    <style name="Widget.Leanback.PlaybackControlsActionIconsStyle">
        <item name="play">@drawable/lb_ic_play</item>
        <item name="pause">@drawable/lb_ic_pause</item>
        <item name="fast_forward">@drawable/lb_ic_fast_forward</item>
        <item name="rewind">@drawable/lb_ic_fast_rewind</item>
        <item name="skip_next">@drawable/lb_ic_skip_next</item>
        <item name="skip_previous">@drawable/lb_ic_skip_previous</item>
        <item name="thumb_up_outline">@drawable/lb_ic_thumb_up_outline</item>
        <item name="thumb_up">@drawable/lb_ic_thumb_up</item>
        <item name="thumb_down_outline">@drawable/lb_ic_thumb_down_outline</item>
        <item name="thumb_down">@drawable/lb_ic_thumb_down</item>
        <item name="repeat">@drawable/lb_ic_loop</item>
        <item name="repeat_one">@drawable/lb_ic_loop_one</item>
        <item name="shuffle">@drawable/lb_ic_shuffle</item>
        <item name="high_quality">@drawable/lb_ic_hq</item>
        <item name="closed_captioning">@drawable/lb_ic_cc</item>
        <item name="picture_in_picture">@drawable/lb_ic_pip</item>
    </style>
    <style name="Widget.Leanback.PlaybackControlsButtonStyle">
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
    </style>
    <style name="Widget.Leanback.PlaybackControlsTimeStyle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.PlaybackControlsTime</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemDetailsStyle">
        <item name="android:paddingStart">?attr/playbackMediaItemPaddingStart</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemDurationStyle">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:singleLine">true</item>
        <item name="android:gravity">center_vertical|right</item>
        <item name="android:visibility">gone</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.PlaybackMediaItemDuration</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemNameStyle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:singleLine">true</item>
        <item name="android:gravity">center_vertical</item>"
        <item name="android:textAppearance">@style/TextAppearance.Leanback.PlaybackMediaItemName</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemNumberStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:visibility">gone</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.PlaybackMediaItemNumber</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemNumberViewFlipperStyle">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemRowStyle" parent="Widget.Leanback.PlaybackRow">
        <item name="android:focusable">false</item>
        <item name="android:focusableInTouchMode">false</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemSeparatorStyle" parent="Widget.Leanback.PlaybackRow">
        <item name="android:background">@color/lb_media_background_color</item>
        <item name="android:src">@color/lb_playback_media_row_separator_highlight_color</item>
        <item name="android:layout_height">@dimen/lb_playback_media_row_separator_height</item>"
    </style>
    <style name="Widget.Leanback.PlaybackMediaListHeaderStyle" parent="Widget.Leanback.PlaybackRow">
        <item name="android:background">#263238</item>
        <item name="android:focusable">false</item>
        <item name="android:focusableInTouchMode">false</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaListHeaderTitleStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:layout_alignParentTop">true</item>
        <item name="android:gravity">center_vertical</item>"
        <item name="android:paddingLeft">?attr/playbackMediaItemPaddingStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.PlaybackMediaListHeaderTitle</item>
    </style>
    <style name="Widget.Leanback.PlaybackRow">
        <item name="android:layout_marginStart">?attr/playbackPaddingStart</item>
        <item name="android:layout_marginEnd">?attr/playbackPaddingEnd</item>
        <item name="android:clipChildren">true</item>
        <item name="android:clipToPadding">true</item>
        <item name="android:foreground">@null</item>
        <item name="android:background">#384248</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
    </style>
    <style name="Widget.Leanback.Row">
    </style>
    <style name="Widget.Leanback.Row.Header" parent="Widget.Leanback.Header">
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Row.Header</item>
    </style>
    <style name="Widget.Leanback.Row.Header.Description" parent="Widget.Leanback.Header">
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Row.Header.Description</item>
    </style>
    <style name="Widget.Leanback.Row.HeaderDock">
        <item name="android:paddingStart">?attr/browsePaddingStart</item>
    </style>
    <style name="Widget.Leanback.Row.HorizontalGridView">
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="android:paddingStart">?attr/browsePaddingStart</item>
        <item name="android:paddingEnd">?attr/browsePaddingEnd</item>
        <item name="android:paddingBottom">@dimen/lb_browse_item_vertical_spacing</item>
        <item name="android:paddingTop">@dimen/lb_browse_item_vertical_spacing</item>
        <item name="android:horizontalSpacing">@dimen/lb_browse_item_horizontal_spacing</item>
        <item name="android:verticalSpacing">@dimen/lb_browse_item_vertical_spacing</item>
        <item name="focusOutFront">true</item>
    </style>
    <style name="Widget.Leanback.Row.HoverCardDescription">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Row.HoverCardDescription</item>
        <item name="android:maxWidth">@dimen/lb_browse_row_hovercard_max_width</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">4</item>
    </style>
    <style name="Widget.Leanback.Row.HoverCardTitle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Row.HoverCardTitle</item>
        <item name="android:maxWidth">@dimen/lb_browse_row_hovercard_max_width</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
    </style>
    <style name="Widget.Leanback.Rows">
    </style>
    <style name="Widget.Leanback.Rows.VerticalGridView">
        <item name="android:paddingBottom">?attr/browsePaddingBottom</item>
        <item name="focusOutFront">true</item>
        <item name="focusOutEnd">true</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
    </style>
    <style name="Widget.Leanback.SearchOrbViewStyle">
        <item name="searchOrbIcon">?attr/defaultSearchIcon</item>
        <item name="searchOrbColor">?attr/defaultSearchColor</item>
        <item name="searchOrbIconColor">?attr/defaultSearchIconColor</item>
        <item name="searchOrbBrightColor">?attr/defaultSearchBrightColor</item>
    </style>
    <style name="Widget.Leanback.Title"/>
    <style name="Widget.Leanback.Title.Icon">
    </style>
    <style name="Widget.Leanback.Title.Text">
        <item name="android:singleLine">true</item>
        <item name="android:gravity">end</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Title</item>
    </style>
    <style name="Widget.Leanback.TitleView">
        <item name="android:paddingTop">?attr/browsePaddingTop</item>
        <item name="android:paddingBottom">?attr/browsePaddingTop</item>
        <item name="android:paddingStart">?attr/browsePaddingStart</item>
        <item name="android:paddingEnd">?attr/browsePaddingEnd</item>
    </style>
    <style name="Widget.LeanbackBase" parent="android:Widget.Holo"/>
    <style name="Widget.Support.CoordinatorLayout" parent="android:Widget">
        <item name="statusBarBackground">#000000</item>
    </style>
    <declare-styleable name="Capability">
        
        <attr format="reference" name="queryPatterns"/>
        
        <attr format="boolean" name="shortcutMatchRequired"/>
    </declare-styleable>
    <declare-styleable name="CardView">
        
        <attr format="color" name="cardBackgroundColor"/>
        
        <attr format="dimension" name="cardCornerRadius"/>
        
        <attr format="dimension" name="cardElevation"/>
        
        <attr format="dimension" name="cardMaxElevation"/>
        
        <attr format="boolean" name="cardUseCompatPadding"/>
        
        <attr format="boolean" name="cardPreventCornerOverlap"/>
        
        <attr format="dimension" name="contentPadding"/>
        
        <attr format="dimension" name="contentPaddingLeft"/>
        
        <attr format="dimension" name="contentPaddingRight"/>
        
        <attr format="dimension" name="contentPaddingTop"/>
        
        <attr format="dimension" name="contentPaddingBottom"/>
        
        <attr name="android:minWidth"/>
        
        <attr name="android:minHeight"/>
    </declare-styleable>
    <declare-styleable name="ColorStateListItem">
        
        <attr name="android:color"/>
        
        <attr format="float" name="alpha"/>
        <attr name="android:alpha"/>
        
        <attr format="float" name="lStar"/>
        <attr name="android:lStar"/>
    </declare-styleable>
    <declare-styleable name="CoordinatorLayout">
        
        <attr format="reference" name="keylines"/>
        
        <attr format="color|reference" name="statusBarBackground"/>
    </declare-styleable>
    <declare-styleable name="CoordinatorLayout_Layout">
        <attr name="android:layout_gravity"/>
        
        <attr format="string" name="layout_behavior"/>
        
        <attr format="reference" name="layout_anchor"/>
        
        <attr format="integer" name="layout_keyline"/>

        
        <attr name="layout_anchorGravity">
            <!-- Push object to the top of its container, not changing its size. -->
            <flag name="top" value="0x30"/>
            <!-- Push object to the bottom of its container, not changing its size. -->
            <flag name="bottom" value="0x50"/>
            <!-- Push object to the left of its container, not changing its size. -->
            <flag name="left" value="0x03"/>
            <!-- Push object to the right of its container, not changing its size. -->
            <flag name="right" value="0x05"/>
            <!-- Place object in the vertical center of its container, not changing its size. -->
            <flag name="center_vertical" value="0x10"/>
            <!-- Grow the vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill_vertical" value="0x70"/>
            <!-- Place object in the horizontal center of its container, not changing its size. -->
            <flag name="center_horizontal" value="0x01"/>
            <!-- Grow the horizontal size of the object if needed so it completely fills its container. -->
            <flag name="fill_horizontal" value="0x07"/>
            <!-- Place the object in the center of its container in both the vertical and horizontal axis, not changing its size. -->
            <flag name="center" value="0x11"/>
            <!-- Grow the horizontal and vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill" value="0x77"/>
            <!-- Additional option that can be set to have the top and/or bottom edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the vertical gravity: a top gravity will clip the bottom
                 edge, a bottom gravity will clip the top edge, and neither will clip both edges. -->
            <flag name="clip_vertical" value="0x80"/>
            <!-- Additional option that can be set to have the left and/or right edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the horizontal gravity: a left gravity will clip the right
                 edge, a right gravity will clip the left edge, and neither will clip both edges. -->
            <flag name="clip_horizontal" value="0x08"/>
            <!-- Push object to the beginning of its container, not changing its size. -->
            <flag name="start" value="0x00800003"/>
            <!-- Push object to the end of its container, not changing its size. -->
            <flag name="end" value="0x00800005"/>
        </attr>

        
        <attr format="enum" name="layout_insetEdge">
            <!-- Don't inset. -->
            <enum name="none" value="0x0"/>
            <!-- Inset the top edge. -->
            <enum name="top" value="0x30"/>
            <!-- Inset the bottom edge. -->
            <enum name="bottom" value="0x50"/>
            <!-- Inset the left edge. -->
            <enum name="left" value="0x03"/>
            <!-- Inset the right edge. -->
            <enum name="right" value="0x05"/>
            <!-- Inset the start edge. -->
            <enum name="start" value="0x00800003"/>
            <!-- Inset the end edge. -->
            <enum name="end" value="0x00800005"/>
        </attr>
        
        <attr name="layout_dodgeInsetEdges">
            <!-- Don't dodge any edges -->
            <flag name="none" value="0x0"/>
            <!-- Dodge the top inset edge. -->
            <flag name="top" value="0x30"/>
            <!-- Dodge the bottom inset edge. -->
            <flag name="bottom" value="0x50"/>
            <!-- Dodge the left inset edge. -->
            <flag name="left" value="0x03"/>
            <!-- Dodge the right inset edge. -->
            <flag name="right" value="0x05"/>
            <!-- Dodge the start inset edge. -->
            <flag name="start" value="0x00800003"/>
            <!-- Dodge the end inset edge. -->
            <flag name="end" value="0x00800005"/>
            <!-- Dodge all the inset edges. -->
            <flag name="all" value="0x77"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="FontFamily">
        
        <attr format="string" name="fontProviderAuthority"/>
        
        <attr format="string" name="fontProviderPackage"/>
        
        <attr format="string" name="fontProviderQuery"/>
        
        <attr format="reference" name="fontProviderCerts"/>
        
        <attr name="fontProviderFetchStrategy">
            <!-- The blocking font fetch works as follows.
              First, check the local cache, then if the requested font is not cached, request the
              font from the provider and wait until it is finished.  You can change the length of
              the timeout by modifying fontProviderFetchTimeout.  If the timeout happens, the
              default typeface will be used instead. -->
            <enum name="blocking" value="0"/>
            <!-- The async font fetch works as follows.
              First, check the local cache, then if the requeted font is not cached, trigger a
              request the font and continue with layout inflation. Once the font fetch succeeds, the
              target text view will be refreshed with the downloaded font data. The
              fontProviderFetchTimeout will be ignored if async loading is specified. -->
            <enum name="async" value="1"/>
        </attr>
        
        <attr format="integer" name="fontProviderFetchTimeout">
            <!-- A special value for the timeout. In this case, the blocking font fetching will not
              timeout and wait until a reply is received from the font provider. -->
            <enum name="forever" value="-1"/>
        </attr>
        
        <attr format="string" name="fontProviderSystemFontFamily"/>
    </declare-styleable>
    <declare-styleable name="FontFamilyFont">
        
        <attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr>
        
        <attr format="reference" name="font"/>
        
        <attr format="integer" name="fontWeight"/>
        
        <attr format="string" name="fontVariationSettings"/>
        
        <attr format="integer" name="ttcIndex"/>
        
        <attr name="android:fontStyle"/>
        <attr name="android:font"/>
        <attr name="android:fontWeight"/>
        <attr name="android:fontVariationSettings"/>
        <attr name="android:ttcIndex"/>
    </declare-styleable>
    <declare-styleable name="Fragment">
        <attr name="android:name"/>
        <attr name="android:id"/>
        <attr name="android:tag"/>
    </declare-styleable>
    <declare-styleable name="FragmentContainerView">
        <attr name="android:name"/>
        <attr name="android:tag"/>
    </declare-styleable>
    <declare-styleable name="GradientColor">
        
        <attr name="android:startColor"/>
        
        <attr name="android:centerColor"/>
        
        <attr name="android:endColor"/>
        
        <attr name="android:type"/>

        
        
        <attr name="android:gradientRadius"/>

        
        
        <attr name="android:centerX"/>
        
        <attr name="android:centerY"/>

        
        
        <attr name="android:startX"/>
        
        <attr name="android:startY"/>
        
        <attr name="android:endX"/>
        
        <attr name="android:endY"/>

        
        <attr name="android:tileMode"/>
    </declare-styleable>
    <declare-styleable name="GradientColorItem">
        
        <attr name="android:offset"/>
        
        <attr name="android:color"/>
    </declare-styleable>
    <declare-styleable name="LeanbackGuidedStepTheme">

        
        <attr format="reference" name="guidedStepTheme"/>
        
        <attr format="float" name="guidedStepHeightWeight"/>

        
        <attr format="float" name="guidedStepKeyline"/>

        
        <attr format="boolean" name="guidedStepThemeFlag"/>

        
        <attr format="reference|color" name="guidedStepBackground"/>

        
        <attr format="reference" name="guidedStepImeAppearingAnimation"/>
        
        <attr format="reference" name="guidedStepImeDisappearingAnimation"/>

        
        <attr format="reference" name="guidanceContainerStyle"/>
        
        <attr format="reference" name="guidanceTitleStyle"/>
        
        <attr format="reference" name="guidanceDescriptionStyle"/>
        
        <attr format="reference" name="guidanceBreadcrumbStyle"/>
        
        <attr format="reference" name="guidanceIconStyle"/>

        
        <attr format="reference" name="guidedActionsSelectorDrawable"/>

        
        <attr format="dimension|reference" name="guidedActionsElevation"/>

        
        <attr format="reference" name="guidedActionsBackground"/>

        
        <attr format="reference" name="guidedActionsBackgroundDark"/>

        
        <attr format="reference" name="guidedActionsListStyle"/>

        
        <attr format="reference" name="guidedSubActionsListStyle"/>

        
        <attr format="reference" name="guidedButtonActionsListStyle"/>

        
        <attr format="reference" name="guidedActionItemContainerStyle"/>
        
        <attr format="reference" name="guidedActionItemCheckmarkStyle"/>
        
        <attr format="reference" name="guidedActionItemIconStyle"/>
        
        <attr format="reference" name="guidedActionItemContentStyle"/>
        
        <attr format="reference" name="guidedActionItemTitleStyle"/>
        
        <attr format="reference" name="guidedActionItemDescriptionStyle"/>
        
        <attr format="reference" name="guidedActionItemChevronStyle"/>

        
        <attr format="reference" name="guidedActionPressedAnimation"/>
        
        <attr format="reference" name="guidedActionUnpressedAnimation"/>
        
        <attr format="reference" name="guidedActionEnabledChevronAlpha"/>
        
        <attr format="reference" name="guidedActionDisabledChevronAlpha"/>
        
        <attr format="reference" name="guidedActionContentWidthWeight"/>
        
        <attr format="reference" name="guidedActionContentWidthWeightTwoPanels"/>
        
        <attr format="reference" name="guidedButtonActionsWidthWeight"/>
        
        <attr format="reference" name="guidedActionTitleMinLines"/>
        
        <attr format="reference" name="guidedActionTitleMaxLines"/>
        
        <attr format="reference" name="guidedActionDescriptionMinLines"/>
        
        <attr format="reference" name="guidedActionVerticalPadding"/>

        
        <attr format="reference" name="guidedActionsContainerStyle"/>
        
        <attr format="reference" name="guidedActionsSelectorStyle"/>
        
        <attr format="reference" name="guidedStepEntryAnimation"/>
        
        <attr format="reference" name="guidedStepExitAnimation"/>
        
        <attr format="reference" name="guidedStepReentryAnimation"/>
        
        <attr format="reference" name="guidedStepReturnAnimation"/>
        
        <attr format="reference" name="guidanceEntryAnimation"/>
        
        <attr format="reference" name="guidedActionsEntryAnimation"/>
        
        <attr format="reference" name="guidedActionsSelectorShowAnimation"/>
        
        <attr format="reference" name="guidedActionsSelectorHideAnimation"/>
        
        <attr format="reference" name="guidedActionCheckedAnimation"/>
        
        <attr format="reference" name="guidedActionUncheckedAnimation"/>
        
        <attr format="reference" name="guidedActionContentWidth"/>
        
        <attr format="reference" name="guidedActionContentWidthNoIcon"/>
    </declare-styleable>
    <declare-styleable name="LeanbackOnboardingTheme">
        
        <attr format="reference" name="onboardingTheme"/>

        
        <attr format="reference" name="onboardingHeaderStyle"/>
        
        <attr format="reference" name="onboardingTitleStyle"/>
        
        <attr format="reference" name="onboardingDescriptionStyle"/>

        
        <attr format="reference" name="onboardingNavigatorContainerStyle"/>
        
        <attr format="reference" name="onboardingPageIndicatorStyle"/>
        
        <attr format="reference" name="onboardingStartButtonStyle"/>

        
        <attr format="reference" name="onboardingLogoStyle"/>

        
        <attr format="reference" name="onboardingMainIconStyle"/>
    </declare-styleable>
    <declare-styleable name="LeanbackTheme">

        
        <attr format="dimension" name="browsePaddingStart"/>
        
        <attr format="dimension" name="browsePaddingEnd"/>
        
        <attr format="dimension" name="browsePaddingTop"/>
        
        <attr format="dimension" name="browsePaddingBottom"/>
        
        <attr format="dimension" name="browseRowsMarginStart"/>
        
        <attr format="dimension" name="browseRowsMarginTop"/>
        
        <attr format="dimension" name="browseRowsFadingEdgeLength"/>

        
        <attr format="reference" name="browseTitleTextStyle"/>

        
        <attr format="reference" name="browseTitleIconStyle"/>

        
        <attr format="reference" name="browseTitleViewStyle"/>

        
        <attr format="reference" name="browseTitleViewLayout"/>

        
        <attr format="reference" name="headersVerticalGridStyle"/>
        
        <attr format="reference" name="headerStyle"/>
        
        <attr format="reference" name="sectionHeaderStyle"/>

        
        <attr format="reference" name="rowsVerticalGridStyle"/>

        
        <attr format="reference" name="rowHorizontalGridStyle"/>
        
        <attr format="reference" name="rowHeaderStyle"/>

        
        <attr format="reference" name="rowHeaderDescriptionStyle"/>

        
        <attr format="reference" name="rowHeaderDockStyle"/>

        
        <attr format="reference" name="rowHoverCardTitleStyle"/>
        
        <attr format="reference" name="rowHoverCardDescriptionStyle"/>

        
        <attr format="reference" name="baseCardViewStyle"/>
        <attr format="reference" name="imageCardViewStyle"/>
        <attr format="reference" name="imageCardViewImageStyle"/>
        <attr format="reference" name="imageCardViewTitleStyle"/>
        <attr format="reference" name="imageCardViewContentStyle"/>
        <attr format="reference" name="imageCardViewBadgeStyle"/>
        <attr format="reference" name="imageCardViewInfoAreaStyle"/>

        
        <attr format="reference" name="detailsDescriptionTitleStyle"/>
        <attr format="reference" name="detailsDescriptionSubtitleStyle"/>
        <attr format="reference" name="detailsDescriptionBodyStyle"/>
        <attr format="reference" name="detailsActionButtonStyle"/>

        
        <attr format="dimension" name="playbackPaddingStart"/>
        <attr format="dimension" name="playbackPaddingEnd"/>
        <attr format="dimension" name="playbackMediaItemPaddingStart"/>

        <attr format="reference" name="playbackMediaListHeaderStyle"/>
        <attr format="reference" name="playbackMediaItemRowStyle"/>
        <attr format="reference" name="playbackMediaItemSeparatorStyle"/>

        <attr format="reference" name="playbackMediaListHeaderTitleStyle"/>
        <attr format="reference" name="playbackMediaItemDetailsStyle"/>
        <attr format="reference" name="playbackMediaItemNumberViewFlipperStyle"/>
        <attr format="reference" name="playbackMediaItemNumberViewFlipperLayout"/>
        <attr format="reference" name="playbackMediaItemNumberStyle"/>
        <attr format="reference" name="playbackMediaItemNameStyle"/>
        <attr format="reference" name="playbackMediaItemDurationStyle"/>

        <attr format="reference" name="playbackControlsButtonStyle"/>
        <attr format="reference" name="playbackControlButtonLabelStyle"/>
        <attr format="reference" name="playbackControlsTimeStyle"/>

        
        <attr format="reference" name="itemsVerticalGridStyle"/>

        
        <attr format="reference" name="errorMessageStyle"/>

        
        <attr format="reference|color" name="defaultBrandColor"/>
        
        <attr format="reference|color" name="defaultBrandColorDark"/>

        
        <attr format="reference|color" name="defaultSearchColor"/>
        
        <attr format="reference|color" name="defaultSearchIconColor"/>
        
        <attr format="reference|color" name="defaultSearchBrightColor"/>
        
        <attr format="reference|color" name="defaultSectionHeaderColor"/>

        
        <attr format="reference" name="searchOrbViewStyle"/>
        <attr format="reference" name="defaultSearchIcon"/>

        <attr format="reference|color" name="playbackProgressPrimaryColor"/>
        <attr format="reference|color" name="playbackProgressSecondaryColor"/>
        <attr format="reference|color" name="playbackControlsIconHighlightColor"/>
        <attr format="reference" name="playbackControlsActionIcons"/>
        
        <attr format="reference|integer" name="playbackControlsAutoHideTimeout"/>
        
        <attr format="reference|integer" name="playbackControlsAutoHideTickleTimeout"/>

        
        <attr format="color" name="overlayDimMaskColor"/>
        
        <attr format="fraction" name="overlayDimActiveLevel"/>
        
        <attr format="fraction" name="overlayDimDimmedLevel"/>
    </declare-styleable>
    <declare-styleable name="PagingIndicator">
        
        <attr format="reference" name="lbDotRadius"/>
        
        <attr format="reference" name="arrowRadius"/>
        
        <attr format="reference" name="dotToDotGap"/>
        
        <attr format="reference" name="dotToArrowGap"/>
        
        <attr format="reference" name="dotBgColor"/>
        
        <attr format="reference" name="arrowColor"/>
        
        <attr format="reference" name="arrowBgColor"/>
    </declare-styleable>
    <declare-styleable name="RecyclerView">
        
        <attr format="string" name="layoutManager"/>

        
        
        
        <eat-comment/>

        <attr name="android:orientation"/>
        <attr name="android:descendantFocusability"/>
        <attr name="android:clipToPadding"/>
        <attr format="integer" name="spanCount"/>
        <attr format="boolean" name="reverseLayout"/>
        <attr format="boolean" name="stackFromEnd"/>
        <attr format="boolean" name="fastScrollEnabled"/>
        <attr format="reference" name="fastScrollVerticalThumbDrawable"/>
        <attr format="reference" name="fastScrollVerticalTrackDrawable"/>
        <attr format="reference" name="fastScrollHorizontalThumbDrawable"/>
        <attr format="reference" name="fastScrollHorizontalTrackDrawable"/>
    </declare-styleable>
    <declare-styleable name="lbBaseCardView">
        
        <attr format="reference|color" name="cardForeground"/>
        
        <attr format="reference|color" name="cardBackground"/>
        
        <attr format="enum" name="cardType">
            <!-- A simple card layout with a single layout region. -->
            <enum name="mainOnly" value="0"/>
            <!-- A card layout with two layout regions: a main area which is
                 always visible, and an info region that appears over the lower
                 area of the main region. -->
            <enum name="infoOver" value="1"/>
            <!-- A card layout with two layout regions: a main area which is
                 always visible, and an info region that appears below the main
                 region. -->
            <enum name="infoUnder" value="2"/>
            <!-- A card layout with three layout regions: a main area which is
                 always visible, an info region that appears below the main
                 region, and an extra region that appears below the info region
                 after a small delay. -->
            <enum name="infoUnderWithExtra" value="3"/>
        </attr>
        
        <attr format="enum" name="infoVisibility">
            <!-- Always display the info region. -->
            <enum name="always" value="0"/>
            <!-- Display the info region only when activated. -->
            <enum name="activated" value="1"/>
            <!-- Display the info region only when selected. -->
            <enum name="selected" value="2"/>
        </attr>
        
        <attr format="enum" name="extraVisibility">
            <!-- Always display the extra region. -->
            <enum name="always" value="0"/>
            <!-- Display the extra region only when activated. -->
            <enum name="activated" value="1"/>
            <!-- Display the extra region only when selected. -->
            <enum name="selected" value="2"/>
        </attr>
        
        <attr format="integer" name="selectedAnimationDelay"/>
        
        <attr format="integer" name="selectedAnimationDuration"/>
        
        <attr format="integer" name="activatedAnimationDuration"/>
    </declare-styleable>
    <declare-styleable name="lbBaseCardView_Layout">
        
        <attr format="enum" name="layout_viewType">
            <!-- The main region of the card. -->
            <enum name="main" value="0"/>
            <!-- The info region of the card. -->
            <enum name="info" value="1"/>
            <!-- The extra region of the card. -->
            <enum name="extra" value="2"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="lbBaseGridView">
        
        <attr format="boolean" name="focusOutFront"/>
        
        <attr format="boolean" name="focusOutEnd"/>
        
        <attr format="boolean" name="focusOutSideStart"/>
        
        <attr format="boolean" name="focusOutSideEnd"/>
        
        <attr format="dimension" name="horizontalMargin"/>
        
        <attr format="dimension" name="verticalMargin"/>
        
        <attr name="android:horizontalSpacing"/>
        
        <attr name="android:verticalSpacing"/>
        
        <attr name="android:gravity"/>
    </declare-styleable>
    <declare-styleable name="lbDatePicker">
        <attr name="android:minDate"/>
        <attr name="android:maxDate"/>
        
        <attr format="string" name="datePickerFormat"/>
    </declare-styleable>
    <declare-styleable name="lbHorizontalGridView">
        
        <attr format="dimension" name="rowHeight">
            <enum name="wrap_content" value="-2"/>
        </attr>
        
        <attr format="integer" name="numberOfRows"/>
    </declare-styleable>
    <declare-styleable name="lbImageCardView">
        
        <attr format="reference|color" name="infoAreaBackground"/>
        
        <attr name="lbImageCardViewType">
            <flag name="Title" value="1"/>
            <flag name="Content" value="2"/>
            <flag name="IconOnRight" value="4"/>
            <flag name="IconOnLeft" value="8"/>
            <!-- Only display the main image. -->
            <flag name="ImageOnly" value="0"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="lbPlaybackControlsActionIcons">
        <attr format="reference" name="play"/>
        <attr format="reference" name="pause"/>
        <attr format="reference" name="fast_forward"/>
        <attr format="reference" name="rewind"/>
        <attr format="reference" name="skip_next"/>
        <attr format="reference" name="skip_previous"/>
        <attr format="reference" name="thumb_up_outline"/>
        <attr format="reference" name="thumb_up"/>
        <attr format="reference" name="thumb_down_outline"/>
        <attr format="reference" name="thumb_down"/>
        <attr format="reference" name="repeat"/>
        <attr format="reference" name="repeat_one"/>
        <attr format="reference" name="shuffle"/>
        <attr format="reference" name="high_quality"/>
        <attr format="reference" name="closed_captioning"/>
        <attr format="reference" name="picture_in_picture"/>
    </declare-styleable>
    <declare-styleable name="lbResizingTextView">
        
        <attr name="resizeTrigger">
            <!-- Resize text whenever it lays out into the maximum number of lines -->
            <flag name="maxLines" value="0x01"/>
        </attr>
        
        <attr format="dimension" name="resizedTextSize"/>
        
        <attr format="boolean" name="maintainLineSpacing"/>
        
        <attr format="dimension" name="resizedPaddingAdjustmentTop"/>
        
        <attr format="dimension" name="resizedPaddingAdjustmentBottom"/>
    </declare-styleable>
    <declare-styleable name="lbSearchOrbView">
        
        <attr format="reference" name="searchOrbIcon"/>
        
        <attr format="reference|color" name="searchOrbIconColor"/>
        
        <attr format="reference|color" name="searchOrbColor"/>
        
        <attr format="reference|color" name="searchOrbBrightColor"/>
    </declare-styleable>
    <declare-styleable name="lbSlide">
        
        <attr name="lb_slideEdge">
            <!-- Slide to and from the left edge of the Scene. -->
            <enum name="left" value="0x03"/>
            <!-- Slide to and from the top edge of the Scene. -->
            <enum name="top" value="0x30"/>
            <!-- Slide to and from the right edge of the Scene. -->
            <enum name="right" value="0x05"/>
            <!-- Slide to and from the bottom edge of the Scene. -->
            <enum name="bottom" value="0x50"/>
            <!-- Slide to and from the x-axis position at the start of the Scene root. -->
            <enum name="start" value="0x00800003"/>
            <!-- Slide to and from the x-axis position at the end of the Scene root. -->
            <enum name="end" value="0x00800005"/>
        </attr>
        <attr name="android:duration"/>
        <attr name="android:startDelay"/>
        <attr name="android:interpolator"/>
    </declare-styleable>
    <declare-styleable name="lbTimePicker">
        
        <attr format="boolean" name="is24HourFormat"/>
        
        <attr format="boolean" name="useCurrentTime"/>
    </declare-styleable>
    <declare-styleable name="lbVerticalGridView">
        
        <attr format="dimension" name="columnWidth">
            <enum name="wrap_content" value="-2"/>
        </attr>
        
        <attr format="integer" name="numberOfColumns"/>
    </declare-styleable>
</resources>