<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:focusable="true"
    android:clickable="true"
    android:foreground="@drawable/card_selector">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/card_background">

        <ImageView
            android:id="@+id/iv_app_icon"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="12dp"
            android:src="@drawable/ic_apps"
            android:contentDescription="应用图标" />

        <TextView
            android:id="@+id/tv_app_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="应用名称"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tv_app_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="应用描述"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:alpha="0.8"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginBottom="8dp" />

        <RatingBar
            android:id="@+id/rating_bar"
            style="?android:attr/ratingBarStyleSmall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:numStars="5"
            android:rating="4.5"
            android:stepSize="0.1"
            android:isIndicator="true" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
