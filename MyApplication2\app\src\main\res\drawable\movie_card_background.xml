<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <layer-list>
            <!-- 外层发光效果 -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/accent_blue" />
                    <corners android:radius="20dp" />
                </shape>
            </item>
            <!-- 内层卡片 -->
            <item android:left="4dp" android:top="4dp" android:right="4dp" android:bottom="4dp">
                <shape android:shape="rectangle">
                    <solid android:color="@color/card_background" />
                    <corners android:radius="16dp" />
                    <stroke android:width="2dp" android:color="@android:color/white" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/card_background" />
            <corners android:radius="16dp" />
        </shape>
    </item>
</selector>
