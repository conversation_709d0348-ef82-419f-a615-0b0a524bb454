<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="160dp"
    android:layout_height="120dp"
    android:orientation="vertical"
    android:gravity="center"
    android:layout_margin="8dp"
    android:focusable="true"
    android:clickable="true"
    android:background="@drawable/app_category_background">

    <ImageView
        android:id="@+id/iv_category_icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginBottom="12dp"
        android:scaleType="centerInside" />

    <TextView
        android:id="@+id/tv_category_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:textStyle="bold"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end" />

</LinearLayout>
