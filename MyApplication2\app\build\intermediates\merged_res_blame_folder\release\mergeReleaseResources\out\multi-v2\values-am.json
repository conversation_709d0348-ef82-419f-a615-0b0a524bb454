{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-am/values-am.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,198,298,395,494,590,692,4890", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "193,293,390,489,585,687,787,4986"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,406,497,613,718,813,901,1027,1150,1253,1372,1497,1613,1726,1830,1924,2043,2132,2231,2332,2431,2523,2633,2745,2852,2956,3057,3160,3261,3368,3467,3572,3659,3741,3836,3970,4117", "endColumns": "106,100,92,90,115,104,94,87,125,122,102,118,124,115,112,103,93,118,88,98,100,98,91,109,111,106,103,100,102,100,106,98,104,86,81,94,133,146,85", "endOffsets": "207,308,401,492,608,713,808,896,1022,1145,1248,1367,1492,1608,1721,1825,1919,2038,2127,2226,2327,2426,2518,2628,2740,2847,2951,3052,3155,3256,3363,3462,3567,3654,3736,3831,3965,4112,4198"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,899,1000,1093,1184,1300,1405,1500,1588,1714,1837,1940,2059,2184,2300,2413,2517,2611,2730,2819,2918,3019,3118,3210,3320,3432,3539,3643,3744,3847,3948,4055,4154,4259,4346,4428,4523,4657,4804", "endColumns": "106,100,92,90,115,104,94,87,125,122,102,118,124,115,112,103,93,118,88,98,100,98,91,109,111,106,103,100,102,100,106,98,104,86,81,94,133,146,85", "endOffsets": "894,995,1088,1179,1295,1400,1495,1583,1709,1832,1935,2054,2179,2295,2408,2512,2606,2725,2814,2913,3014,3113,3205,3315,3427,3534,3638,3739,3842,3943,4050,4149,4254,4341,4423,4518,4652,4799,4885"}}]}]}