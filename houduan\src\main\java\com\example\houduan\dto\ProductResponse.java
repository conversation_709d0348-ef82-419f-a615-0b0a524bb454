package com.example.houduan.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 商品响应DTO
 */
@Data
public class ProductResponse {

    /**
     * 商品ID
     */
    private String id;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品主图URL
     */
    private String image;

    /**
     * 商品图片URLs
     */
    private List<String> images;

    /**
     * 商品分类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 库存数量
     */
    private Integer stock;

    /**
     * 销量
     */
    private Integer sales;

    /**
     * 评分(0.0-10.0)
     */
    private BigDecimal rating;

    /**
     * 评分人数
     */
    private Integer ratingCount;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品规格
     */
    private Map<String, Object> specifications;

    /**
     * 是否推荐
     */
    private Boolean isRecommended;

    /**
     * 是否热销
     */
    private Boolean isHot;

    /**
     * 是否有库存
     */
    private Boolean inStock;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
