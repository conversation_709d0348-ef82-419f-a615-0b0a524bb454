package com.example.houduan.controller;

import com.example.houduan.common.Result;
import com.example.houduan.entity.Category;
import com.example.houduan.service.CategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分类控制器
 */
@RestController
@RequestMapping("/categories")
@RequiredArgsConstructor
public class CategoryController {

    private final CategoryService categoryService;

    /**
     * 根据类型获取分类列表
     */
    @GetMapping("/{type}")
    public Result<List<Category>> getCategoriesByType(@PathVariable String type) {
        try {
            List<Category> categories = categoryService.getCategoriesByType(type);
            return Result.success(categories);
        } catch (Exception e) {
            return Result.error("获取分类列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有分类
     */
    @GetMapping("/all")
    public Result<List<Category>> getAllCategories() {
        try {
            List<Category> categories = categoryService.getAllCategories();
            return Result.success(categories);
        } catch (Exception e) {
            return Result.error("获取分类列表失败: " + e.getMessage());
        }
    }
}
