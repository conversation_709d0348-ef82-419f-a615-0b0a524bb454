package com.example.myapplicationtv.widget

import android.content.Context
import android.util.AttributeSet
import androidx.leanback.widget.ImageCardView
import com.example.myapplicationtv.utils.FocusHelper

/**
 * 支持焦点动画的ImageCardView
 */
open class FocusableImageCardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ImageCardView(context, attrs, defStyleAttr) {

    private var onSelectionChangedListener: ((Boolean) -> Unit)? = null

    init {
        // 设置焦点属性
        isFocusable = true
        isFocusableInTouchMode = true

        // 设置焦点变化监听器
        setOnFocusChangeListener { _, hasFocus ->
            FocusHelper.setFocusAnimation(this, hasFocus, 1.1f, 1.1f)
            onSelectionChangedListener?.invoke(hasFocus)
        }
    }

    override fun setSelected(selected: Boolean) {
        super.setSelected(selected)
        // 选中时也应用焦点动画
        FocusHelper.setFocusAnimation(this, selected, 1.05f, 1.05f)
        onSelectionChangedListener?.invoke(selected)
    }

    fun setOnSelectionChangedListener(listener: (Boolean) -> Unit) {
        onSelectionChangedListener = listener
    }
}
