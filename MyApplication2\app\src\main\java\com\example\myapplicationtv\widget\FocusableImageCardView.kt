package com.example.myapplicationtv.widget

import android.content.Context
import android.util.AttributeSet
import androidx.leanback.widget.ImageCardView
import com.example.myapplicationtv.utils.FocusHelper

/**
 * 支持焦点动画的ImageCardView
 */
class FocusableImageCardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ImageCardView(context, attrs, defStyleAttr) {

    init {
        // 设置焦点属性
        isFocusable = true
        isFocusableInTouchMode = true
        
        // 设置焦点变化监听器
        setOnFocusChangeListener { _, hasFocus ->
            FocusHelper.setFocusAnimation(this, hasFocus, 1.1f, 1.1f)
        }
    }

    override fun setSelected(selected: Boolean) {
        super.setSelected(selected)
        // 选中时也应用焦点动画
        FocusHelper.setFocusAnimation(this, selected, 1.05f, 1.05f)
    }
}
