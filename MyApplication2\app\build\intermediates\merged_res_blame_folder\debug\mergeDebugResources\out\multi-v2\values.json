{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values/values.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\9717948c970c19acce1692941c22c532\\transformed\\activity-1.7.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "438,458", "startColumns": "4,4", "startOffsets": "28394,29451", "endColumns": "41,59", "endOffsets": "28431,29506"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\6c7c511ec8b68e45e391fb30b6d5f144\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "415,437", "startColumns": "4,4", "startOffsets": "27209,28327", "endColumns": "53,66", "endOffsets": "27258,28389"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\304761040604b9580785c2d5f3e785f2\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "2,19,20,21,22,91,92,93,562,569,571,574,1416", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,1076,1137,1199,1261,5694,5753,5810,36709,37123,37187,37313,89725", "endLines": "2,19,20,21,22,91,92,93,568,570,573,576,1443", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "152,1132,1194,1256,1320,5748,5805,5859,37118,37182,37308,37436,90644"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\cffe277412d6e71dab1701f200c8c21e\\transformed\\recyclerview-1.3.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "5,101,102,103,104,105,106,416,1937", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "278,6352,6411,6459,6515,6590,6666,27263,109606", "endLines": "5,101,102,103,104,105,106,416,1957", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "329,6406,6454,6510,6585,6661,6733,27324,110441"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\59e3767b96f4ee90f4b57a0c56b92a96\\transformed\\glide-4.11.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "414", "startColumns": "4", "startOffsets": "27151", "endColumns": "57", "endOffsets": "27204"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\b9b3bed66d3a00e5a31ccb8c12557bab\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "459", "startColumns": "4", "startOffsets": "29511", "endColumns": "53", "endOffsets": "29560"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\fc95bcae0ba172be27074a8a115ec3a7\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "499", "startColumns": "4", "startOffsets": "32196", "endColumns": "82", "endOffsets": "32274"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\cec8346c4e2ab21f142e352fe0bfc4ee\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,1407,1454,1460", "startColumns": "4,4,4,4", "startOffsets": "157,89377,90950,91161", "endLines": "3,1409,1459,1543", "endColumns": "60,12,24,24", "endOffsets": "213,89517,91156,95672"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "4,12,13,16,17,83,84,94,95,96,97,98,99,100,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,368,369,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,435,436,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,498,504,505,506,507,508,509,510,559,577,578,583,586,591,858,859,1410,1444,1544,1577,1607,1640", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "218,617,689,895,960,5180,5249,5864,5934,6002,6074,6144,6205,6279,22834,22895,22956,23018,23082,23144,23205,23273,23373,23433,23499,23572,23641,23698,23750,24254,24326,25119,25184,25243,25302,25362,25422,25482,25542,25602,25662,25722,25782,25842,25902,25961,26021,26081,26141,26201,26261,26321,26381,26441,26501,26561,26620,26680,26740,26799,26858,26917,26976,27035,28257,28292,28501,28556,28619,28674,28732,28790,28851,28914,28971,29022,29072,29133,29190,29256,29290,29325,32126,32487,32554,32626,32695,32764,32838,32910,36531,37441,37558,37825,38118,38385,56518,56590,89522,90649,95677,97408,98408,99090", "endLines": "4,12,13,16,17,83,84,94,95,96,97,98,99,100,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,368,369,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,435,436,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,498,504,505,506,507,508,509,510,559,577,581,583,589,591,858,859,1415,1453,1576,1597,1639,1645", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "273,684,772,955,1021,5244,5307,5929,5997,6069,6139,6200,6274,6347,22890,22951,23013,23077,23139,23200,23268,23368,23428,23494,23567,23636,23693,23745,23807,24321,24397,25179,25238,25297,25357,25417,25477,25537,25597,25657,25717,25777,25837,25897,25956,26016,26076,26136,26196,26256,26316,26376,26436,26496,26556,26615,26675,26735,26794,26853,26912,26971,27030,27089,28287,28322,28551,28614,28669,28727,28785,28846,28909,28966,29017,29067,29128,29185,29251,29285,29320,29355,32191,32549,32621,32690,32759,32833,32905,32993,36597,37553,37754,37930,38314,38509,56585,56652,89720,90945,97403,98084,99085,99252"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\9262bdae479e1b658eb70c7bbd10b157\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "457", "startColumns": "4", "startOffsets": "29408", "endColumns": "42", "endOffsets": "29446"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,968,973,978,983,988,993,997,1001,1003,1007,1011,1016,1021,1026,1031,1036,1041,1043,1047,1050,1053,1056,1061,1063,1144,1146,1148,1150,1199,1204,1206,1209,1219,1221,1230,1231,1235,1245,1247,1253,1260,1271,1278,1279,1291,1304,1311,1327,1335,1352,1360,1369,1380,1387,1400,1408,1419,1420,1431,1432,1434,1444,1453,1457,1458,1468,1469,1476,1488,1496,1507,1517,1525,1536,1546,1552,1561,1568,1581,1596,1598,1609,1613,1631,1635,1639,1647,1655,1663,1670,1676,1680,1685,1690,1699,1709,1711,1714,1717,1720,1731,1738,1745,1747,1754,1760,1761,1763,1770,1776,55,231,266,409,425,478,491,514,520,528,541,559,574,584,604,611", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,221,280,349,413,480,537,608,664,724,781,840,904,964,1029,1099,1162,1225,1292,1366,1419,1461,1525,1594,1674,1745,1807,1876,1943,1999,2073,2146,2220,2293,2365,2439,2523,2593,2665,2745,2818,2893,2948,3015,3070,3137,3205,3267,3334,3403,3461,3509,37711,37766,37822,37883,37936,37996,38047,38110,38175,38238,38300,38361,38418,38486,38550,38619,38680,38741,38799,38856,38917,38999,39074,39144,39206,39261,39320,39411,39497,39555,39621,39677,39759,39823,39885,39941,39994,40049,40102,40179,40245,40316,40374,40434,40492,40558,40624,40678,40737,40800,40857,40915,40971,41039,41105,41164,41218,41271,41347,41419,41488,41561,41630,41703,41784,41863,41941,42011,42096,42178,42250,42351,42420,42486,42558,42632,42709,42783,42859,42933,42998,43063,43138,43211,43276,43339,43404,43488,43546,43607,43675,43745,43804,43862,43930,44000,44068,44120,44184,44246,44307,44365,44424,44482,44550,44620,44678,44746,44818,44888,44963,45065,45176,45275,45376,45441,45506,45570,45658,45725,45790,45863,45932,45997,46110,46211,46276,46343,46412,46481,46551,46622,46690,46755,46851,46956,47049,47133,47229,47308,47374,47425,47484,47544,47602,47669,47734,47794,47854,47917,47980,48045,48117,48194,48253,48315,48383,48451,48508,48567,48632,48704,48777,48850,48916,48980,49046,49113,49180,49265,49334,49400,49468,49534,49601,49671,49745,49822,49894,49968,50048,50117,50183,50249,50319,50384,50453,50517,50582,50654,50712,50771,50836,50916,50996,51074,51147,51219,51286,51356,51438,51516,51591,51663,51737,51805,51871,51938,52002,52070,52130,52198,52250,52313,52370,52433,52489,52553,52614,52672,52741,52805,52863,52922,52979,53048,53109,53164,53229,53295,53359,53413,53471,53526,53583,53638,53688,53744,53807,53870,53920,53971,54037,54113,54178,54250,54323,54395,54468,54553,54625,54689,54754,54812,54865,54917,54970,55023,55082,55133,55180,55228,55278,55332,55384,55434,55481,55535,55583,55627,55682,55730,55799,55871,55934,56005,56072,56142,56212,56282,56356,56427,56486,56561,56637,56707,56777,56862,56934,57000,57060,57121,57187,57254,57323,57394,57463,57533,57598,57660,57723,57800,57874,57950,58025,58070,58117,58194,58265,58333,58397,58486,58568,58634,58700,58801,58900,58975,59067,59166,59257,59346,59421,59482,59580,59639,59710,59783,59854,59917,59997,60073,60154,60233,60302,60379,60459,60545,60621,60703,60760,60814,60884,60989,61110,61170,61387,61662,61976,62293,62610,62886,63137,63368,63438,63686,63928,64219,64532,64786,65034,65284,65541,65648,65946,66158,66358,66538,66850,66947,72977,73055,73134,73215,77178,77518,77620,77807,78709,78793,79441,79506,79717,80429,80553,80905,81361,82185,82598,82644,83433,84150,84508,85409,85824,86768,87311,87848,88610,88994,89944,90464,91214,91278,91961,92024,92149,92832,93313,93525,93569,94187,94237,94655,95372,95795,96554,97159,97677,98258,98885,99202,99690,100089,101010,101938,102017,102593,102830,103950,104139,104376,104820,105294,105754,106174,106491,106717,107106,107390,107981,108535,108587,108771,108979,109116,109852,110275,110691,110744,111095,111452,111494,111553,111910,112255,3566,15670,18203,26294,27279,30232,30845,32264,32501,32857,33476,34421,35268,35838,36884,37349", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,967,972,977,982,987,992,996,1000,1002,1006,1010,1015,1020,1025,1030,1035,1040,1042,1046,1049,1052,1055,1060,1062,1143,1145,1147,1149,1198,1203,1205,1208,1218,1220,1229,1230,1234,1244,1246,1252,1259,1270,1277,1278,1290,1303,1310,1326,1334,1351,1359,1368,1379,1386,1399,1407,1418,1419,1430,1431,1433,1443,1452,1456,1457,1467,1468,1475,1487,1495,1506,1516,1524,1535,1545,1551,1560,1567,1580,1595,1597,1608,1612,1630,1634,1638,1646,1654,1662,1669,1675,1679,1684,1689,1698,1708,1710,1713,1716,1719,1730,1737,1744,1746,1753,1759,1760,1762,1769,1775,1776,230,265,408,424,477,490,513,519,527,540,558,573,583,603,610,618", "endColumns": "54,60,58,68,63,66,56,70,55,59,56,58,63,59,64,69,62,62,66,73,52,41,63,68,79,70,61,68,66,55,73,72,73,72,71,73,83,69,71,79,72,74,54,66,54,66,67,61,66,68,57,47,56,54,55,60,52,59,50,62,64,62,61,60,56,67,63,68,60,60,57,56,60,81,74,69,61,54,58,90,85,57,65,55,81,63,61,55,52,54,52,76,65,70,57,59,57,65,65,53,58,62,56,57,55,67,65,58,53,52,75,71,68,72,68,72,80,78,77,69,84,81,71,100,68,65,71,73,76,73,75,73,64,64,74,72,64,62,64,83,57,60,67,69,58,57,67,69,67,51,63,61,60,57,58,57,67,69,57,67,71,69,74,101,110,98,100,64,64,63,87,66,64,72,68,64,112,100,64,66,68,68,69,70,67,64,95,104,92,83,95,78,65,50,58,59,57,66,64,59,59,62,62,64,71,76,58,61,67,67,56,58,64,71,72,72,65,63,65,66,66,84,68,65,67,65,66,69,73,76,71,73,79,68,65,65,69,64,68,63,64,71,57,58,64,79,79,77,72,71,66,69,81,77,74,71,73,67,65,66,63,67,59,67,51,62,56,62,55,63,60,57,68,63,57,58,56,68,60,54,64,65,63,53,57,54,56,54,49,55,62,62,49,50,65,75,64,71,72,71,72,84,71,63,64,57,52,51,52,52,58,50,46,47,49,53,51,49,46,53,47,43,54,47,68,71,62,70,66,69,69,69,73,70,58,74,75,69,69,84,71,65,59,60,65,66,68,70,68,69,64,61,62,76,73,75,74,44,46,76,70,67,63,88,81,65,65,100,98,74,91,98,90,88,74,60,97,58,70,72,70,62,79,75,80,78,68,76,79,85,75,81,56,53,69,104,120,59,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,64,12,12,12,12,12,12,12,45,12,12,12,12,12,12,12,12,12,12,12,12,12,63,12,62,12,12,12,12,43,12,49,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,41,12,12,12,68,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,216,275,344,408,475,532,603,659,719,776,835,899,959,1024,1094,1157,1220,1287,1361,1414,1456,1520,1589,1669,1740,1802,1871,1938,1994,2068,2141,2215,2288,2360,2434,2518,2588,2660,2740,2813,2888,2943,3010,3065,3132,3200,3262,3329,3398,3456,3504,3561,37761,37817,37878,37931,37991,38042,38105,38170,38233,38295,38356,38413,38481,38545,38614,38675,38736,38794,38851,38912,38994,39069,39139,39201,39256,39315,39406,39492,39550,39616,39672,39754,39818,39880,39936,39989,40044,40097,40174,40240,40311,40369,40429,40487,40553,40619,40673,40732,40795,40852,40910,40966,41034,41100,41159,41213,41266,41342,41414,41483,41556,41625,41698,41779,41858,41936,42006,42091,42173,42245,42346,42415,42481,42553,42627,42704,42778,42854,42928,42993,43058,43133,43206,43271,43334,43399,43483,43541,43602,43670,43740,43799,43857,43925,43995,44063,44115,44179,44241,44302,44360,44419,44477,44545,44615,44673,44741,44813,44883,44958,45060,45171,45270,45371,45436,45501,45565,45653,45720,45785,45858,45927,45992,46105,46206,46271,46338,46407,46476,46546,46617,46685,46750,46846,46951,47044,47128,47224,47303,47369,47420,47479,47539,47597,47664,47729,47789,47849,47912,47975,48040,48112,48189,48248,48310,48378,48446,48503,48562,48627,48699,48772,48845,48911,48975,49041,49108,49175,49260,49329,49395,49463,49529,49596,49666,49740,49817,49889,49963,50043,50112,50178,50244,50314,50379,50448,50512,50577,50649,50707,50766,50831,50911,50991,51069,51142,51214,51281,51351,51433,51511,51586,51658,51732,51800,51866,51933,51997,52065,52125,52193,52245,52308,52365,52428,52484,52548,52609,52667,52736,52800,52858,52917,52974,53043,53104,53159,53224,53290,53354,53408,53466,53521,53578,53633,53683,53739,53802,53865,53915,53966,54032,54108,54173,54245,54318,54390,54463,54548,54620,54684,54749,54807,54860,54912,54965,55018,55077,55128,55175,55223,55273,55327,55379,55429,55476,55530,55578,55622,55677,55725,55794,55866,55929,56000,56067,56137,56207,56277,56351,56422,56481,56556,56632,56702,56772,56857,56929,56995,57055,57116,57182,57249,57318,57389,57458,57528,57593,57655,57718,57795,57869,57945,58020,58065,58112,58189,58260,58328,58392,58481,58563,58629,58695,58796,58895,58970,59062,59161,59252,59341,59416,59477,59575,59634,59705,59778,59849,59912,59992,60068,60149,60228,60297,60374,60454,60540,60616,60698,60755,60809,60879,60984,61105,61165,61382,61657,61971,62288,62605,62881,63132,63363,63433,63681,63923,64214,64527,64781,65029,65279,65536,65643,65941,66153,66353,66533,66845,66942,72972,73050,73129,73210,77173,77513,77615,77802,78704,78788,79436,79501,79712,80424,80548,80900,81356,82180,82593,82639,83428,84145,84503,85404,85819,86763,87306,87843,88605,88989,89939,90459,91209,91273,91956,92019,92144,92827,93308,93520,93564,94182,94232,94650,95367,95790,96549,97154,97672,98253,98880,99197,99685,100084,101005,101933,102012,102588,102825,103945,104134,104371,104815,105289,105749,106169,106486,106712,107101,107385,107976,108530,108582,108766,108974,109111,109847,110270,110686,110739,111090,111447,111489,111548,111905,112250,112319,15665,18198,26289,27274,30227,30840,32259,32496,32852,33471,34416,35263,35833,36879,37344,37706"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,360,361,362,363,370,371,372,373,374,375,376,377,378,379,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,456,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,554,593,597,602,607,612,617,622,626,630,632,636,640,645,650,655,660,665,670,672,676,679,682,685,690,692,773,775,777,779,826,831,833,836,846,848,860,861,865,875,877,883,890,901,908,909,921,934,941,957,965,982,990,999,1010,1017,1030,1038,1049,1050,1061,1062,1064,1074,1083,1087,1088,1098,1099,1106,1118,1126,1137,1147,1155,1166,1176,1182,1191,1198,1211,1226,1228,1239,1243,1261,1265,1269,1277,1285,1293,1300,1306,1310,1315,1320,1329,1339,1341,1344,1347,1350,1361,1368,1375,1377,1384,1390,1391,1393,1400,1406,1646,1766,1790,1921,1958,2006,2017,2037,2043,2051,2064,2082,2097,2107,2127,2133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1663,1718,1779,1838,1907,1971,2038,2095,2166,2222,2282,2339,2398,2462,2522,2587,2657,2720,2783,2850,2924,2977,3019,3083,3152,3232,3303,3365,3434,3501,3557,3631,3704,3778,3851,3923,3997,4081,4151,4223,4303,4376,4451,4506,4573,4628,4695,4763,4825,4892,4961,5019,5067,6738,6793,6849,6910,6963,7023,7074,7137,7202,7265,7327,7388,7445,7513,7577,7646,7707,7768,7826,7883,7944,8026,8101,8171,8233,8288,8347,8438,8524,8582,8648,8704,8786,8850,8912,8968,9021,9076,9129,9206,9272,9343,9401,9461,9519,9585,9651,9705,9764,9827,9884,9942,9998,10066,10132,10191,10245,10298,10374,10446,10515,10588,10657,10730,10811,10890,10968,11038,11123,11205,11277,11378,11447,11513,11585,11659,11736,11810,11886,11960,12025,12090,12165,12238,12303,12366,12431,12515,12573,12634,12702,12772,12831,12889,12957,13027,13095,13147,13211,13273,13334,13392,13451,13509,13577,13647,13705,13773,13845,13915,13990,14092,14203,14302,14403,14468,14533,14597,14685,14752,14817,14890,14959,15024,15137,15238,15303,15370,15439,15508,15578,15649,15717,15782,15878,15983,16076,16160,16256,16335,16401,16452,16511,16571,16629,16696,16761,16821,16881,16944,17007,17072,17144,17221,17280,17342,17410,17478,17535,17594,17659,17731,17804,17877,17943,18007,18073,18140,18207,18292,18361,18427,18495,18561,18628,18698,18772,18849,18921,18995,19075,19144,19210,19276,19346,19411,19480,19544,19609,19681,19739,19798,19863,19943,20023,20101,20174,20246,20313,20383,20465,20543,20618,20690,20764,20832,20898,20965,21029,21097,21157,21225,21277,21340,21397,21460,21516,21580,21641,21699,21768,21832,21890,21949,22006,22075,22136,22191,22256,22322,22386,22440,22498,22553,22610,22665,22715,22771,23812,23875,23925,23976,24402,24478,24543,24615,24688,24760,24833,24918,24990,25054,27329,27387,27440,27492,27545,27598,27657,27708,27755,27803,27853,27907,27959,28009,28056,28110,28158,28202,29360,29739,29808,29880,29943,30014,30081,30151,30221,30291,30365,30436,30495,30570,30646,30716,30786,30871,30943,31009,31069,31130,31196,31263,31332,31403,31472,31542,31607,31669,31732,31809,31883,31959,32034,32079,33223,33300,33371,33439,33503,33592,33674,33740,33806,33907,34006,34081,34173,34272,34363,34452,34527,34588,34686,34745,34816,34889,34960,35023,35103,35179,35260,35339,35408,35485,35565,35651,35727,35809,35866,35920,35990,36095,36256,38581,38751,39026,39340,39657,39974,40250,40501,40732,40802,41050,41292,41583,41896,42150,42398,42648,42905,43012,43310,43522,43722,43902,44214,44311,50291,50369,50448,50529,54182,54522,54624,54811,55713,55797,56657,56722,56933,57645,57769,58121,58577,59401,59814,59860,60649,61366,61724,62625,63040,63984,64527,65064,65826,66210,67160,67680,68430,68494,69177,69240,69365,70048,70529,70741,70785,71403,71453,71871,72588,73011,73770,74375,74730,75311,75938,76255,76743,77142,78063,78991,79070,79646,79883,81003,81192,81429,81873,82347,82807,83227,83544,83770,84159,84443,85034,85588,85640,85824,86032,86169,86905,87328,87744,87797,88148,88505,88547,88606,88963,89308,99257,103356,104119,109092,110446,112684,113117,113740,113952,114218,114715,115660,116229,116592,117576,117773", "endLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,360,361,362,363,370,371,372,373,374,375,376,377,378,379,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,456,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,554,596,601,606,611,616,621,625,629,631,635,639,644,649,654,659,664,669,671,675,678,681,684,689,691,772,774,776,778,825,830,832,835,845,847,856,860,864,874,876,882,889,900,907,908,920,933,940,956,964,981,989,998,1009,1016,1029,1037,1048,1049,1060,1061,1063,1073,1082,1086,1087,1097,1098,1105,1117,1125,1136,1146,1154,1165,1175,1181,1190,1197,1210,1225,1227,1238,1242,1260,1264,1268,1276,1284,1292,1299,1305,1309,1314,1319,1328,1338,1340,1343,1346,1349,1360,1367,1374,1376,1383,1389,1390,1392,1399,1405,1406,1765,1789,1920,1936,2005,2016,2036,2042,2050,2063,2081,2096,2106,2126,2132,2140", "endColumns": "54,60,58,68,63,66,56,70,55,59,56,58,63,59,64,69,62,62,66,73,52,41,63,68,79,70,61,68,66,55,73,72,73,72,71,73,83,69,71,79,72,74,54,66,54,66,67,61,66,68,57,47,56,54,55,60,52,59,50,62,64,62,61,60,56,67,63,68,60,60,57,56,60,81,74,69,61,54,58,90,85,57,65,55,81,63,61,55,52,54,52,76,65,70,57,59,57,65,65,53,58,62,56,57,55,67,65,58,53,52,75,71,68,72,68,72,80,78,77,69,84,81,71,100,68,65,71,73,76,73,75,73,64,64,74,72,64,62,64,83,57,60,67,69,58,57,67,69,67,51,63,61,60,57,58,57,67,69,57,67,71,69,74,101,110,98,100,64,64,63,87,66,64,72,68,64,112,100,64,66,68,68,69,70,67,64,95,104,92,83,95,78,65,50,58,59,57,66,64,59,59,62,62,64,71,76,58,61,67,67,56,58,64,71,72,72,65,63,65,66,66,84,68,65,67,65,66,69,73,76,71,73,79,68,65,65,69,64,68,63,64,71,57,58,64,79,79,77,72,71,66,69,81,77,74,71,73,67,65,66,63,67,59,67,51,62,56,62,55,63,60,57,68,63,57,58,56,68,60,54,64,65,63,53,57,54,56,54,49,55,62,62,49,50,65,75,64,71,72,71,72,84,71,63,64,57,52,51,52,52,58,50,46,47,49,53,51,49,46,53,47,43,54,47,68,71,62,70,66,69,69,69,73,70,58,74,75,69,69,84,71,65,59,60,65,66,68,70,68,69,64,61,62,76,73,75,74,44,46,76,70,67,63,88,81,65,65,100,98,74,91,98,90,88,74,60,97,58,70,72,70,62,79,75,80,78,68,76,79,85,75,81,56,53,69,104,120,59,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,64,12,12,12,12,12,12,12,45,12,12,12,12,12,12,12,12,12,12,12,12,12,63,12,62,12,12,12,12,43,12,49,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,41,12,12,12,68,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1713,1774,1833,1902,1966,2033,2090,2161,2217,2277,2334,2393,2457,2517,2582,2652,2715,2778,2845,2919,2972,3014,3078,3147,3227,3298,3360,3429,3496,3552,3626,3699,3773,3846,3918,3992,4076,4146,4218,4298,4371,4446,4501,4568,4623,4690,4758,4820,4887,4956,5014,5062,5119,6788,6844,6905,6958,7018,7069,7132,7197,7260,7322,7383,7440,7508,7572,7641,7702,7763,7821,7878,7939,8021,8096,8166,8228,8283,8342,8433,8519,8577,8643,8699,8781,8845,8907,8963,9016,9071,9124,9201,9267,9338,9396,9456,9514,9580,9646,9700,9759,9822,9879,9937,9993,10061,10127,10186,10240,10293,10369,10441,10510,10583,10652,10725,10806,10885,10963,11033,11118,11200,11272,11373,11442,11508,11580,11654,11731,11805,11881,11955,12020,12085,12160,12233,12298,12361,12426,12510,12568,12629,12697,12767,12826,12884,12952,13022,13090,13142,13206,13268,13329,13387,13446,13504,13572,13642,13700,13768,13840,13910,13985,14087,14198,14297,14398,14463,14528,14592,14680,14747,14812,14885,14954,15019,15132,15233,15298,15365,15434,15503,15573,15644,15712,15777,15873,15978,16071,16155,16251,16330,16396,16447,16506,16566,16624,16691,16756,16816,16876,16939,17002,17067,17139,17216,17275,17337,17405,17473,17530,17589,17654,17726,17799,17872,17938,18002,18068,18135,18202,18287,18356,18422,18490,18556,18623,18693,18767,18844,18916,18990,19070,19139,19205,19271,19341,19406,19475,19539,19604,19676,19734,19793,19858,19938,20018,20096,20169,20241,20308,20378,20460,20538,20613,20685,20759,20827,20893,20960,21024,21092,21152,21220,21272,21335,21392,21455,21511,21575,21636,21694,21763,21827,21885,21944,22001,22070,22131,22186,22251,22317,22381,22435,22493,22548,22605,22660,22710,22766,22829,23870,23920,23971,24037,24473,24538,24610,24683,24755,24828,24913,24985,25049,25114,27382,27435,27487,27540,27593,27652,27703,27750,27798,27848,27902,27954,28004,28051,28105,28153,28197,28252,29403,29803,29875,29938,30009,30076,30146,30216,30286,30360,30431,30490,30565,30641,30711,30781,30866,30938,31004,31064,31125,31191,31258,31327,31398,31467,31537,31602,31664,31727,31804,31878,31954,32029,32074,32121,33295,33366,33434,33498,33587,33669,33735,33801,33902,34001,34076,34168,34267,34358,34447,34522,34583,34681,34740,34811,34884,34955,35018,35098,35174,35255,35334,35403,35480,35560,35646,35722,35804,35861,35915,35985,36090,36211,36311,38746,39021,39335,39652,39969,40245,40496,40727,40797,41045,41287,41578,41891,42145,42393,42643,42900,43007,43305,43517,43717,43897,44209,44306,50286,50364,50443,50524,54177,54517,54619,54806,55708,55792,56440,56717,56928,57640,57764,58116,58572,59396,59809,59855,60644,61361,61719,62620,63035,63979,64522,65059,65821,66205,67155,67675,68425,68489,69172,69235,69360,70043,70524,70736,70780,71398,71448,71866,72583,73006,73765,74370,74725,75306,75933,76250,76738,77137,78058,78986,79065,79641,79878,80998,81187,81424,81868,82342,82802,83222,83539,83765,84154,84438,85029,85583,85635,85819,86027,86164,86900,87323,87739,87792,88143,88500,88542,88601,88958,89303,89372,103351,104114,109087,109601,112679,113112,113735,113947,114213,114710,115655,116224,116587,117571,117768,118037"}}, {"source": "E:\\aikaifa\\MovieTV\\MyApplication2\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "12,17,15,13,14,16,2,1,11,21,20,9,6,3,22,10,23,4,5", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "528,765,670,574,622,717,76,16,478,893,832,367,290,134,955,422,1008,188,236", "endColumns": "45,45,46,47,47,47,57,59,49,61,60,54,52,53,52,55,46,47,53", "endOffsets": "569,806,712,617,665,760,129,71,523,950,888,417,338,183,1003,473,1050,231,285"}, "to": {"startLines": "6,7,8,9,10,11,14,15,18,23,24,25,26,27,28,82,87,88,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "334,380,426,473,521,569,777,835,1026,1325,1387,1448,1503,1556,1610,5124,5472,5519,5640", "endColumns": "45,45,46,47,47,47,57,59,49,61,60,54,52,53,52,55,46,47,53", "endOffsets": "375,421,468,516,564,612,830,890,1071,1382,1443,1498,1551,1605,1658,5175,5514,5562,5689"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\09b2070107f58b91baaaa03824de0d80\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "460", "startColumns": "4", "startOffsets": "29565", "endColumns": "49", "endOffsets": "29610"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\ccfd53c838c812e96e4910096138f89c\\transformed\\fragment-1.6.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "413,439,461,1598,1603", "startColumns": "4,4,4,4,4", "startOffsets": "27094,28436,29615,98089,98259", "endLines": "413,439,461,1602,1606", "endColumns": "56,64,63,24,24", "endOffsets": "27146,28496,29674,98254,98403"}}, {"source": "E:\\aikaifa\\MovieTV\\MyApplication2\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,2,11,12,17,5,16,4,13,6,3,9,10,7,8", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16,71,563,609,790,241,721,193,652,299,135,470,517,363,421", "endColumns": "54,63,45,42,49,57,68,47,39,63,57,46,45,57,48", "endOffsets": "66,130,604,647,835,294,785,236,687,358,188,512,558,416,465"}, "to": {"startLines": "500,501,502,503,511,512,513,514,553,555,556,557,558,560,561", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "32279,32334,32398,32444,32998,33048,33106,33175,36216,36316,36380,36438,36485,36602,36660", "endColumns": "54,63,45,42,49,57,68,47,39,63,57,46,45,57,48", "endOffsets": "32329,32393,32439,32482,33043,33101,33170,33218,36251,36375,36433,36480,36526,36655,36704"}}, {"source": "E:\\aikaifa\\MovieTV\\MyApplication2\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "17", "endColumns": "73", "endOffsets": "86"}, "to": {"startLines": "857", "startColumns": "4", "startOffsets": "56445", "endColumns": "72", "endOffsets": "56513"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\d1cb9debee0ec7aff0e9116da1dc9922\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "85,86,89,364,365,366,367,462,582,584,585,590,592", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5312,5401,5567,24042,24095,24148,24201,29679,37759,37935,38057,38319,38514", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "5396,5467,5635,24090,24143,24196,24249,29734,37820,38052,38113,38380,38576"}}]}]}