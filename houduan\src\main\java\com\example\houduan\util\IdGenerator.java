package com.example.houduan.util;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * ID生成器工具类
 */
public class IdGenerator {
    
    private static final String CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final SecureRandom RANDOM = new SecureRandom();
    
    /**
     * 生成32位长度的唯一ID
     * 格式：时间戳(10位) + 随机字符串(22位)
     */
    public static String generateId() {
        // 时间戳部分 (10位) - 使用秒级时间戳
        long timestamp = System.currentTimeMillis() / 1000;
        String timestampStr = String.valueOf(timestamp);

        // 随机字符串部分 (22位)
        StringBuilder randomPart = new StringBuilder(22);
        for (int i = 0; i < 22; i++) {
            randomPart.append(CHARS.charAt(RANDOM.nextInt(CHARS.length())));
        }

        return timestampStr + randomPart.toString();
    }
    
    /**
     * 生成指定长度的随机ID
     */
    public static String generateId(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("Length must be positive");
        }
        
        StringBuilder id = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            id.append(CHARS.charAt(RANDOM.nextInt(CHARS.length())));
        }
        
        return id.toString();
    }
    
    /**
     * 生成带前缀的ID
     */
    public static String generateIdWithPrefix(String prefix) {
        String randomId = generateId(32 - prefix.length());
        return prefix + randomId;
    }
}
