<template>
  <div class="system-config">
    <div class="page-header">
      <h1>系统设置</h1>
    </div>
    
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card>
          <template #header>
            <span>基础配置</span>
          </template>
          
          <el-form
            ref="formRef"
            :model="form"
            label-width="120px"
            size="large"
          >
            <el-form-item label="系统名称">
              <el-input v-model="form.systemName" placeholder="请输入系统名称" />
            </el-form-item>
            
            <el-form-item label="系统描述">
              <el-input 
                v-model="form.systemDescription" 
                type="textarea" 
                :rows="3"
                placeholder="请输入系统描述"
              />
            </el-form-item>
            
            <el-form-item label="系统版本">
              <el-input v-model="form.systemVersion" placeholder="请输入系统版本" />
            </el-form-item>
            
            <el-form-item label="联系邮箱">
              <el-input v-model="form.contactEmail" placeholder="请输入联系邮箱" />
            </el-form-item>
            
            <el-form-item label="客服电话">
              <el-input v-model="form.servicePhone" placeholder="请输入客服电话" />
            </el-form-item>
            
            <el-form-item label="用户注册">
              <el-switch v-model="form.allowRegister" />
              <span style="margin-left: 10px; color: #666;">
                是否允许用户注册
              </span>
            </el-form-item>
            
            <el-form-item label="内容审核">
              <el-switch v-model="form.contentReview" />
              <span style="margin-left: 10px; color: #666;">
                是否开启内容审核
              </span>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleSave" :loading="loading">
                保存配置
              </el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>系统信息</span>
          </template>
          
          <div class="system-info">
            <div class="info-item">
              <span class="label">服务器时间:</span>
              <span class="value">{{ currentTime }}</span>
            </div>
            
            <div class="info-item">
              <span class="label">运行环境:</span>
              <span class="value">Spring Boot 3.5.3</span>
            </div>
            
            <div class="info-item">
              <span class="label">数据库:</span>
              <span class="value">MySQL 8.0</span>
            </div>
            
            <div class="info-item">
              <span class="label">Java版本:</span>
              <span class="value">17.0.12</span>
            </div>
            
            <div class="info-item">
              <span class="label">系统版本:</span>
              <span class="value">v1.0.0</span>
            </div>
          </div>
        </el-card>
        
        <el-card style="margin-top: 20px;">
          <template #header>
            <span>快捷操作</span>
          </template>
          
          <div class="quick-actions">
            <el-button type="primary" @click="handleClearCache">
              <el-icon><Delete /></el-icon>
              清理缓存
            </el-button>
            
            <el-button type="warning" @click="handleBackupData">
              <el-icon><Download /></el-icon>
              备份数据
            </el-button>
            
            <el-button type="info" @click="handleViewLogs">
              <el-icon><Document /></el-icon>
              查看日志
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete, Download, Document } from '@element-plus/icons-vue'

const formRef = ref()
const loading = ref(false)
const currentTime = ref('')

const form = reactive({
  systemName: 'MovieTV 影视管理系统',
  systemDescription: '专业的TV端影视内容管理平台',
  systemVersion: 'v1.0.0',
  contactEmail: '<EMAIL>',
  servicePhone: '************',
  allowRegister: true,
  contentReview: false
})

let timer = null

// 更新当前时间
const updateCurrentTime = () => {
  currentTime.value = new Date().toLocaleString()
}

// 保存配置
const handleSave = async () => {
  try {
    loading.value = true
    // 这里调用API保存配置
    // await systemAPI.updateConfig(form)
    
    ElMessage.success('配置保存成功')
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    loading.value = false
  }
}

// 重置配置
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 清理缓存
const handleClearCache = () => {
  ElMessage.success('缓存清理成功')
}

// 备份数据
const handleBackupData = () => {
  ElMessage.success('数据备份已开始')
}

// 查看日志
const handleViewLogs = () => {
  ElMessage.info('日志查看功能开发中')
}

onMounted(() => {
  updateCurrentTime()
  timer = setInterval(updateCurrentTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.system-config {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.system-info {
  padding: 10px 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.label {
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.quick-actions .el-button {
  width: 100%;
}
</style>
