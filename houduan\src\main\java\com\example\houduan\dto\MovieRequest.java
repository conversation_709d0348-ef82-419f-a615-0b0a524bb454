package com.example.houduan.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 电影请求DTO
 */
@Data
public class MovieRequest {

    @NotBlank(message = "电影名称不能为空")
    private String title;

    private String originalTitle;

    private String cover;

    private String poster;

    private BigDecimal rating;

    private Integer ratingCount;

    @NotBlank(message = "类型不能为空")
    private String type;

    private String category;

    private String area;

    private String language;

    private Integer year;

    private Integer duration;

    private String director;

    private String actors;

    private String description;

    private String playUrl;

    private String trailerUrl;

    private Integer isRecommended;

    private Integer isHot;

    private Integer isNew;

    @NotNull(message = "状态不能为空")
    private Integer status;
}
