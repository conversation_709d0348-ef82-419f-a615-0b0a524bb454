// 认证工具函数

const TOKEN_KEY = 'token'
const USER_INFO_KEY = 'userInfo'

// 获取token
export function getToken() {
  return localStorage.getItem(TOKEN_KEY)
}

// 设置token
export function setToken(token) {
  localStorage.setItem(TOKEN_KEY, token)
}

// 移除token
export function removeToken() {
  localStorage.removeItem(TOKEN_KEY)
  localStorage.removeItem(USER_INFO_KEY)
}

// 获取用户信息
export function getUserInfo() {
  const userInfo = localStorage.getItem(USER_INFO_KEY)
  if (!userInfo || userInfo === 'undefined' || userInfo === 'null') {
    return null
  }
  try {
    return JSON.parse(userInfo)
  } catch (error) {
    console.warn('解析用户信息失败:', error)
    // 清除无效的用户信息
    localStorage.removeItem(USER_INFO_KEY)
    return null
  }
}

// 设置用户信息
export function setUserInfo(userInfo) {
  localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
}

// 检查是否已登录
export function isLoggedIn() {
  const token = getToken()
  // 只要有有效的token就认为已登录
  return !!token
}

// 检查token是否有效（简单检查）
export function isTokenValid() {
  const token = getToken()
  if (!token) return false

  // 对于模拟token，检查格式
  if (token.startsWith('mock-jwt-token-')) {
    const timestamp = token.replace('mock-jwt-token-', '')
    const tokenTime = parseInt(timestamp)
    const now = Date.now()
    // 模拟token 24小时有效期
    return (now - tokenTime) < 24 * 60 * 60 * 1000
  }

  // 对于真实JWT token，简单检查格式
  if (token.includes('.')) {
    // JWT token 通常包含两个点，分为三部分
    const parts = token.split('.')
    return parts.length === 3
  }

  // 其他情况默认有效
  return true
}

// 登出
export function logout() {
  removeToken()
  window.location.href = '/login'
}

// 清理所有认证相关的存储数据
export function clearAuthData() {
  localStorage.removeItem(TOKEN_KEY)
  localStorage.removeItem(USER_INFO_KEY)
  // 清理其他可能的认证相关数据
  localStorage.removeItem('user')
  localStorage.removeItem('authToken')
}
