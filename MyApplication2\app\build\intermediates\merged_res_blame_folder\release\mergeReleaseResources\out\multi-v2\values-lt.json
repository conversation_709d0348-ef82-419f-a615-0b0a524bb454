{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-lt/values-lt.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,313,412,515,626,736,5282", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "198,308,407,510,621,731,851,5378"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,317,412,508,629,739,836,928,1052,1175,1280,1412,1569,1692,1814,1922,2019,2146,2237,2341,2448,2552,2650,2776,2887,2999,3110,3214,3327,3463,3593,3725,3851,3938,4023,4135,4273,4437", "endColumns": "108,102,94,95,120,109,96,91,123,122,104,131,156,122,121,107,96,126,90,103,106,103,97,125,110,111,110,103,112,135,129,131,125,86,84,111,137,163,93", "endOffsets": "209,312,407,503,624,734,831,923,1047,1170,1275,1407,1564,1687,1809,1917,2014,2141,2232,2336,2443,2547,2645,2771,2882,2994,3105,3209,3322,3458,3588,3720,3846,3933,4018,4130,4268,4432,4526"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "856,965,1068,1163,1259,1380,1490,1587,1679,1803,1926,2031,2163,2320,2443,2565,2673,2770,2897,2988,3092,3199,3303,3401,3527,3638,3750,3861,3965,4078,4214,4344,4476,4602,4689,4774,4886,5024,5188", "endColumns": "108,102,94,95,120,109,96,91,123,122,104,131,156,122,121,107,96,126,90,103,106,103,97,125,110,111,110,103,112,135,129,131,125,86,84,111,137,163,93", "endOffsets": "960,1063,1158,1254,1375,1485,1582,1674,1798,1921,2026,2158,2315,2438,2560,2668,2765,2892,2983,3087,3194,3298,3396,3522,3633,3745,3856,3960,4073,4209,4339,4471,4597,4684,4769,4881,5019,5183,5277"}}]}]}