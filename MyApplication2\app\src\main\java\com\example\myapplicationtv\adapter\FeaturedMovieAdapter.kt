package com.example.myapplicationtv.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.myapplicationtv.R
import com.example.myapplicationtv.model.MovieResponse

/**
 * 精选电影适配器
 */
class FeaturedMovieAdapter(
    private val onItemClick: (MovieResponse) -> Unit
) : RecyclerView.Adapter<FeaturedMovieAdapter.MovieViewHolder>() {

    private var movies = listOf<MovieResponse>()

    fun updateMovies(newMovies: List<MovieResponse>) {
        movies = newMovies
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MovieViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_featured_movie, parent, false)
        return MovieViewHolder(view)
    }

    override fun onBindViewHolder(holder: MovieViewHolder, position: Int) {
        val movie = movies[position]
        holder.bind(movie)
        
        holder.itemView.setOnClickListener {
            onItemClick(movie)
        }
        
        holder.itemView.setOnFocusChangeListener { _, hasFocus ->
            holder.focusIndicator.visibility = if (hasFocus) View.VISIBLE else View.GONE
        }
    }

    override fun getItemCount(): Int = movies.size

    class MovieViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivPoster: ImageView = itemView.findViewById(R.id.iv_movie_poster)
        private val tvTitle: TextView = itemView.findViewById(R.id.tv_movie_title)
        private val tvRating: TextView = itemView.findViewById(R.id.tv_movie_rating)
        private val tvScore: TextView = itemView.findViewById(R.id.tv_movie_score)
        val focusIndicator: View = itemView.findViewById(R.id.focus_indicator)

        fun bind(movie: MovieResponse) {
            tvTitle.text = movie.title
            tvScore.text = movie.rating?.toString() ?: "0.0"

            // 根据评分显示星级
            val rating = movie.rating?.toDouble() ?: 0.0
            val stars = when {
                rating >= 9.0 -> "★★★★★"
                rating >= 8.0 -> "★★★★☆"
                rating >= 7.0 -> "★★★☆☆"
                rating >= 6.0 -> "★★☆☆☆"
                rating >= 5.0 -> "★☆☆☆☆"
                else -> "☆☆☆☆☆"
            }
            tvRating.text = stars

            // 加载海报图片
            if (!movie.poster.isNullOrEmpty()) {
                Glide.with(itemView.context)
                    .load(movie.poster)
                    .into(ivPoster)
            } else {
                ivPoster.setImageResource(R.drawable.app_icon_your_company)
            }
        }
    }
}
