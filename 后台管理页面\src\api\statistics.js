import request from '@/utils/request'

// 获取统计概览
export function getStatisticsOverview(params) {
  return request({
    url: '/statistics/overview',
    method: 'get',
    params
  })
}

// 获取内容统计
export function getContentStatistics(params) {
  return request({
    url: '/statistics/content',
    method: 'get',
    params
  })
}

// 获取用户统计
export function getUserStatistics(params) {
  return request({
    url: '/statistics/user',
    method: 'get',
    params
  })
}

// 获取实时统计
export function getRealTimeStatistics() {
  return request({
    url: '/statistics/realtime',
    method: 'get'
  })
}

// 获取趋势数据
export function getTrendData(params) {
  return request({
    url: '/statistics/trend',
    method: 'get',
    params
  })
}

// 获取热门内容
export function getPopularContent(params) {
  return request({
    url: '/statistics/popular',
    method: 'get',
    params
  })
}

// 获取用户行为分析
export function getUserBehaviorAnalysis(params) {
  return request({
    url: '/statistics/user-behavior',
    method: 'get',
    params
  })
}

// 导出统计报告
export function exportStatisticsReport(params) {
  return request({
    url: '/statistics/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
