{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-29:/values-tr/values-tr.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,402,499,601,707,5202", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "197,299,397,494,596,702,813,5298"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,499,618,729,828,918,1052,1180,1282,1400,1552,1695,1832,1939,2033,2165,2255,2361,2473,2579,2674,2786,2902,3027,3146,3250,3357,3473,3587,3699,3809,3896,3985,4095,4237,4400", "endColumns": "106,100,94,90,118,110,98,89,133,127,101,117,151,142,136,106,93,131,89,105,111,105,94,111,115,124,118,103,106,115,113,111,109,86,88,109,141,162,88", "endOffsets": "207,308,403,494,613,724,823,913,1047,1175,1277,1395,1547,1690,1827,1934,2028,2160,2250,2356,2468,2574,2669,2781,2897,3022,3141,3245,3352,3468,3582,3694,3804,3891,3980,4090,4232,4395,4484"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "818,925,1026,1121,1212,1331,1442,1541,1631,1765,1893,1995,2113,2265,2408,2545,2652,2746,2878,2968,3074,3186,3292,3387,3499,3615,3740,3859,3963,4070,4186,4300,4412,4522,4609,4698,4808,4950,5113", "endColumns": "106,100,94,90,118,110,98,89,133,127,101,117,151,142,136,106,93,131,89,105,111,105,94,111,115,124,118,103,106,115,113,111,109,86,88,109,141,162,88", "endOffsets": "920,1021,1116,1207,1326,1437,1536,1626,1760,1888,1990,2108,2260,2403,2540,2647,2741,2873,2963,3069,3181,3287,3382,3494,3610,3735,3854,3958,4065,4181,4295,4407,4517,4604,4693,4803,4945,5108,5197"}}]}]}