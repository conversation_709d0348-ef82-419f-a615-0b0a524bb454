<dependencies>
  <compile
      roots=":@@:app::debug,androidx.leanback:leanback:1.0.0@aar,com.github.bumptech.glide:glide:4.11.0@aar,androidx.fragment:fragment:1.6.2@aar,androidx.fragment:fragment:1.6.2@aar,androidx.fragment:fragment-ktx:1.6.2@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.vectordrawable:vectordrawable-animated:1.0.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.activity:activity-ktx:1.7.2@aar,androidx.activity:activity:1.7.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,androidx.recyclerview:recyclerview:1.3.2@aar,androidx.recyclerview:recyclerview:1.3.2@aar,androidx.media:media:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.vectordrawable:vectordrawable:1.0.0@aar,androidx.core:core:1.10.1@aar,androidx.core:core:1.10.1@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.core:core-ktx:1.10.1@aar,com.squareup.retrofit2:converter-gson:2.9.0@jar,com.squareup.retrofit2:retrofit:2.9.0@jar,com.squareup.okhttp3:logging-interceptor:4.11.0@jar,com.squareup.okhttp3:okhttp:4.11.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.squareup.okio:okio-jvm:3.2.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar,androidx.collection:collection-ktx:1.1.0@jar,androidx.savedstate:savedstate:1.2.1@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.annotation:annotation-experimental:1.3.0@aar,com.github.bumptech.glide:gifdecoder:4.11.0@aar,androidx.exifinterface:exifinterface:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.collection:collection:1.1.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,com.google.code.gson:gson:2.10.1@jar,org.jetbrains:annotations:23.0.0@jar,com.github.bumptech.glide:disklrucache:4.11.0@jar,com.github.bumptech.glide:annotations:4.11.0@jar">
    <dependency
        name=":@@:app::debug"
        simpleName="artifacts::app"/>
    <dependency
        name="androidx.leanback:leanback:1.0.0@aar"
        simpleName="androidx.leanback:leanback"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.11.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.fragment:fragment:1.6.2@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.2@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.0.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.activity:activity-ktx:1.7.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.7.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.2@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.media:media:1.0.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.0.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.core:core:1.10.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.10.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.11.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.11.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.2.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.11.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.0.0@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.6.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.11.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.11.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
  </compile>
</dependencies>
