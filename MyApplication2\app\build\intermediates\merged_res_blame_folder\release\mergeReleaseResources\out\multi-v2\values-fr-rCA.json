{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,404,506,610,714,5409", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "198,300,399,501,605,709,823,5505"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,631,746,845,939,1071,1199,1305,1429,1607,1743,1875,1982,2073,2209,2298,2408,2517,2626,2727,2846,2972,3099,3222,3339,3465,3600,3741,3867,3999,4086,4174,4282,4426,4590", "endColumns": "106,100,98,95,122,114,98,93,131,127,105,123,177,135,131,106,90,135,88,109,108,108,100,118,125,126,122,116,125,134,140,125,131,86,87,107,143,163,95", "endOffsets": "207,308,407,503,626,741,840,934,1066,1194,1300,1424,1602,1738,1870,1977,2068,2204,2293,2403,2512,2621,2722,2841,2967,3094,3217,3334,3460,3595,3736,3862,3994,4081,4169,4277,4421,4585,4681"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "828,935,1036,1135,1231,1354,1469,1568,1662,1794,1922,2028,2152,2330,2466,2598,2705,2796,2932,3021,3131,3240,3349,3450,3569,3695,3822,3945,4062,4188,4323,4464,4590,4722,4809,4897,5005,5149,5313", "endColumns": "106,100,98,95,122,114,98,93,131,127,105,123,177,135,131,106,90,135,88,109,108,108,100,118,125,126,122,116,125,134,140,125,131,86,87,107,143,163,95", "endOffsets": "930,1031,1130,1226,1349,1464,1563,1657,1789,1917,2023,2147,2325,2461,2593,2700,2791,2927,3016,3126,3235,3344,3445,3564,3690,3817,3940,4057,4183,4318,4459,4585,4717,4804,4892,5000,5144,5308,5404"}}]}]}