-- TV端影视软件数据库创建脚本
-- 数据库：movietv
-- 字符集：utf8mb4
-- 排序规则：utf8mb4_unicode_ci

-- 创建数据库
CREATE DATABASE IF NOT EXISTS movietv 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE movietv;

-- 1. 用户表
CREATE TABLE `user` (
    `id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '昵称',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
    `member_level` VARCHAR(20) DEFAULT 'NORMAL' COMMENT '会员等级(NORMAL,VIP,SVIP)',
    `member_expire_time` DATETIME DEFAULT NULL COMMENT '会员到期时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态(0:禁用,1:正常)',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_phone` (`phone`),
    UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 电影表
CREATE TABLE `movie` (
    `id` VARCHAR(32) NOT NULL COMMENT '电影ID',
    `title` VARCHAR(200) NOT NULL COMMENT '电影名称',
    `original_title` VARCHAR(200) DEFAULT NULL COMMENT '原名',
    `cover` VARCHAR(500) DEFAULT NULL COMMENT '封面图片URL',
    `poster` VARCHAR(500) DEFAULT NULL COMMENT '海报图片URL',
    `rating` DECIMAL(3,1) DEFAULT 0.0 COMMENT '评分(0.0-10.0)',
    `rating_count` INT DEFAULT 0 COMMENT '评分人数',
    `type` VARCHAR(50) NOT NULL COMMENT '类型(电影,电视剧,综艺,动漫)',
    `category` VARCHAR(100) DEFAULT NULL COMMENT '分类(动作,科幻,爱情等)',
    `area` VARCHAR(50) DEFAULT NULL COMMENT '地区',
    `language` VARCHAR(50) DEFAULT NULL COMMENT '语言',
    `year` INT DEFAULT NULL COMMENT '上映年份',
    `duration` INT DEFAULT NULL COMMENT '时长(分钟)',
    `director` VARCHAR(200) DEFAULT NULL COMMENT '导演',
    `actors` VARCHAR(500) DEFAULT NULL COMMENT '主演',
    `description` TEXT COMMENT '简介',
    `play_url` VARCHAR(1000) DEFAULT NULL COMMENT '播放地址',
    `trailer_url` VARCHAR(1000) DEFAULT NULL COMMENT '预告片地址',
    `view_count` BIGINT DEFAULT 0 COMMENT '播放次数',
    `favorite_count` INT DEFAULT 0 COMMENT '收藏次数',
    `is_recommended` TINYINT DEFAULT 0 COMMENT '是否推荐(0:否,1:是)',
    `is_hot` TINYINT DEFAULT 0 COMMENT '是否热门(0:否,1:是)',
    `is_new` TINYINT DEFAULT 0 COMMENT '是否最新(0:否,1:是)',
    `status` TINYINT DEFAULT 1 COMMENT '状态(0:下架,1:上架)',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`),
    KEY `idx_category` (`category`),
    KEY `idx_year` (`year`),
    KEY `idx_rating` (`rating`),
    KEY `idx_view_count` (`view_count`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电影表';

-- 3. 电影章节表(用于电视剧分集)
CREATE TABLE `movie_chapter` (
    `id` VARCHAR(32) NOT NULL COMMENT '章节ID',
    `movie_id` VARCHAR(32) NOT NULL COMMENT '电影ID',
    `chapter_number` INT NOT NULL COMMENT '集数',
    `title` VARCHAR(200) DEFAULT NULL COMMENT '章节标题',
    `duration` INT DEFAULT NULL COMMENT '时长(分钟)',
    `play_url` VARCHAR(1000) DEFAULT NULL COMMENT '播放地址',
    `view_count` BIGINT DEFAULT 0 COMMENT '播放次数',
    `status` TINYINT DEFAULT 1 COMMENT '状态(0:禁用,1:正常)',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_movie_id` (`movie_id`),
    KEY `idx_chapter_number` (`chapter_number`),
    CONSTRAINT `fk_chapter_movie` FOREIGN KEY (`movie_id`) REFERENCES `movie` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电影章节表';

-- 4. 用户收藏表
CREATE TABLE `user_favorite` (
    `id` VARCHAR(32) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `movie_id` VARCHAR(32) NOT NULL COMMENT '电影ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_movie` (`user_id`, `movie_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_movie_id` (`movie_id`),
    CONSTRAINT `fk_favorite_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_favorite_movie` FOREIGN KEY (`movie_id`) REFERENCES `movie` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏表';

-- 5. 用户观看历史表
CREATE TABLE `user_history` (
    `id` VARCHAR(32) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `movie_id` VARCHAR(32) NOT NULL COMMENT '电影ID',
    `chapter_id` VARCHAR(32) DEFAULT NULL COMMENT '章节ID',
    `progress` INT DEFAULT 0 COMMENT '播放进度(秒)',
    `duration` INT DEFAULT 0 COMMENT '总时长(秒)',
    `watch_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '观看时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_movie_chapter` (`user_id`, `movie_id`, `chapter_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_movie_id` (`movie_id`),
    KEY `idx_watch_time` (`watch_time`),
    CONSTRAINT `fk_history_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_history_movie` FOREIGN KEY (`movie_id`) REFERENCES `movie` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户观看历史表';

-- 6. 用户评分表
CREATE TABLE `user_rating` (
    `id` VARCHAR(32) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `movie_id` VARCHAR(32) NOT NULL COMMENT '电影ID',
    `rating` DECIMAL(3,1) NOT NULL COMMENT '评分(0.0-10.0)',
    `comment` TEXT COMMENT '评论内容',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '评分时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_movie` (`user_id`, `movie_id`),
    KEY `idx_movie_id` (`movie_id`),
    KEY `idx_rating` (`rating`),
    CONSTRAINT `fk_rating_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_rating_movie` FOREIGN KEY (`movie_id`) REFERENCES `movie` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户评分表';

-- 7. 应用表
CREATE TABLE `application` (
    `id` VARCHAR(32) NOT NULL COMMENT '应用ID',
    `name` VARCHAR(100) NOT NULL COMMENT '应用名称',
    `icon` VARCHAR(500) DEFAULT NULL COMMENT '应用图标URL',
    `category` VARCHAR(50) DEFAULT NULL COMMENT '应用分类',
    `version` VARCHAR(20) DEFAULT NULL COMMENT '版本号',
    `size` BIGINT DEFAULT NULL COMMENT '应用大小(字节)',
    `rating` DECIMAL(3,1) DEFAULT 0.0 COMMENT '评分',
    `rating_count` INT DEFAULT 0 COMMENT '评分人数',
    `download_count` BIGINT DEFAULT 0 COMMENT '下载次数',
    `description` TEXT COMMENT '应用描述',
    `download_url` VARCHAR(1000) DEFAULT NULL COMMENT '下载地址',
    `screenshots` TEXT COMMENT '截图URLs(JSON格式)',
    `is_recommended` TINYINT DEFAULT 0 COMMENT '是否推荐',
    `status` TINYINT DEFAULT 1 COMMENT '状态(0:下架,1:上架)',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_category` (`category`),
    KEY `idx_rating` (`rating`),
    KEY `idx_download_count` (`download_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用表';

-- 8. 游戏表
CREATE TABLE `game` (
    `id` VARCHAR(32) NOT NULL COMMENT '游戏ID',
    `name` VARCHAR(100) NOT NULL COMMENT '游戏名称',
    `cover` VARCHAR(500) DEFAULT NULL COMMENT '游戏封面URL',
    `icon` VARCHAR(500) DEFAULT NULL COMMENT '游戏图标URL',
    `category` VARCHAR(50) DEFAULT NULL COMMENT '游戏分类',
    `version` VARCHAR(20) DEFAULT NULL COMMENT '版本号',
    `size` BIGINT DEFAULT NULL COMMENT '游戏大小(字节)',
    `rating` DECIMAL(3,1) DEFAULT 0.0 COMMENT '评分',
    `rating_count` INT DEFAULT 0 COMMENT '评分人数',
    `play_count` BIGINT DEFAULT 0 COMMENT '游玩次数',
    `description` TEXT COMMENT '游戏描述',
    `play_url` VARCHAR(1000) DEFAULT NULL COMMENT '游戏地址',
    `screenshots` TEXT COMMENT '截图URLs(JSON格式)',
    `is_recommended` TINYINT DEFAULT 0 COMMENT '是否推荐',
    `is_featured` TINYINT DEFAULT 0 COMMENT '是否精选',
    `status` TINYINT DEFAULT 1 COMMENT '状态(0:下架,1:上架)',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_category` (`category`),
    KEY `idx_rating` (`rating`),
    KEY `idx_play_count` (`play_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏表';

-- 9. 商品表
CREATE TABLE `product` (
    `id` VARCHAR(32) NOT NULL COMMENT '商品ID',
    `name` VARCHAR(200) NOT NULL COMMENT '商品名称',
    `image` VARCHAR(500) DEFAULT NULL COMMENT '商品主图URL',
    `images` TEXT COMMENT '商品图片URLs(JSON格式)',
    `category` VARCHAR(50) DEFAULT NULL COMMENT '商品分类',
    `brand` VARCHAR(100) DEFAULT NULL COMMENT '品牌',
    `price` DECIMAL(10,2) NOT NULL COMMENT '价格',
    `original_price` DECIMAL(10,2) DEFAULT NULL COMMENT '原价',
    `stock` INT DEFAULT 0 COMMENT '库存数量',
    `sales` INT DEFAULT 0 COMMENT '销量',
    `rating` DECIMAL(3,1) DEFAULT 0.0 COMMENT '评分',
    `rating_count` INT DEFAULT 0 COMMENT '评分人数',
    `description` TEXT COMMENT '商品描述',
    `specifications` TEXT COMMENT '商品规格(JSON格式)',
    `is_recommended` TINYINT DEFAULT 0 COMMENT '是否推荐',
    `is_hot` TINYINT DEFAULT 0 COMMENT '是否热销',
    `status` TINYINT DEFAULT 1 COMMENT '状态(0:下架,1:上架)',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_category` (`category`),
    KEY `idx_price` (`price`),
    KEY `idx_sales` (`sales`),
    KEY `idx_rating` (`rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 10. 订单表
CREATE TABLE `order_info` (
    `id` VARCHAR(32) NOT NULL COMMENT '订单ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
    `total_amount` DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    `pay_amount` DECIMAL(10,2) DEFAULT NULL COMMENT '实付金额',
    `status` VARCHAR(20) DEFAULT 'PENDING' COMMENT '订单状态(PENDING,PAID,SHIPPED,COMPLETED,CANCELLED)',
    `pay_method` VARCHAR(20) DEFAULT NULL COMMENT '支付方式',
    `pay_time` DATETIME DEFAULT NULL COMMENT '支付时间',
    `ship_time` DATETIME DEFAULT NULL COMMENT '发货时间',
    `complete_time` DATETIME DEFAULT NULL COMMENT '完成时间',
    `receiver_name` VARCHAR(50) DEFAULT NULL COMMENT '收货人姓名',
    `receiver_phone` VARCHAR(20) DEFAULT NULL COMMENT '收货人电话',
    `receiver_address` VARCHAR(500) DEFAULT NULL COMMENT '收货地址',
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '订单备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_order_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 11. 订单详情表
CREATE TABLE `order_item` (
    `id` VARCHAR(32) NOT NULL COMMENT '主键ID',
    `order_id` VARCHAR(32) NOT NULL COMMENT '订单ID',
    `product_id` VARCHAR(32) NOT NULL COMMENT '商品ID',
    `product_name` VARCHAR(200) NOT NULL COMMENT '商品名称',
    `product_image` VARCHAR(500) DEFAULT NULL COMMENT '商品图片',
    `price` DECIMAL(10,2) NOT NULL COMMENT '商品单价',
    `quantity` INT NOT NULL COMMENT '购买数量',
    `total_price` DECIMAL(10,2) NOT NULL COMMENT '小计金额',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_product_id` (`product_id`),
    CONSTRAINT `fk_item_order` FOREIGN KEY (`order_id`) REFERENCES `order_info` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_item_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单详情表';

-- 12. Banner表
CREATE TABLE `banner` (
    `id` VARCHAR(32) NOT NULL COMMENT 'Banner ID',
    `title` VARCHAR(200) DEFAULT NULL COMMENT '标题',
    `image` VARCHAR(500) NOT NULL COMMENT '图片URL',
    `link_type` VARCHAR(20) DEFAULT 'NONE' COMMENT '链接类型(NONE,MOVIE,APP,GAME,PRODUCT,URL)',
    `link_id` VARCHAR(32) DEFAULT NULL COMMENT '链接ID',
    `link_url` VARCHAR(1000) DEFAULT NULL COMMENT '链接URL',
    `position` VARCHAR(20) DEFAULT 'HOME' COMMENT '位置(HOME,MOVIE,APP,GAME,MALL)',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
    `start_time` DATETIME DEFAULT NULL COMMENT '开始时间',
    `end_time` DATETIME DEFAULT NULL COMMENT '结束时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_position` (`position`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Banner表';

-- 13. 分类表
CREATE TABLE `category` (
    `id` VARCHAR(32) NOT NULL COMMENT '分类ID',
    `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
    `type` VARCHAR(20) NOT NULL COMMENT '分类类型(MOVIE,APP,GAME,PRODUCT)',
    `parent_id` VARCHAR(32) DEFAULT NULL COMMENT '父分类ID',
    `icon` VARCHAR(500) DEFAULT NULL COMMENT '分类图标',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';

-- 14. 系统配置表
CREATE TABLE `system_config` (
    `id` VARCHAR(32) NOT NULL COMMENT '配置ID',
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `config_value` TEXT COMMENT '配置值',
    `config_desc` VARCHAR(200) DEFAULT NULL COMMENT '配置描述',
    `config_type` VARCHAR(20) DEFAULT 'STRING' COMMENT '配置类型(STRING,NUMBER,BOOLEAN,JSON)',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 15. 管理员表
CREATE TABLE `admin` (
    `id` VARCHAR(32) NOT NULL COMMENT '管理员ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '昵称',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    `role` VARCHAR(20) DEFAULT 'ADMIN' COMMENT '角色(SUPER_ADMIN,ADMIN,EDITOR)',
    `permissions` TEXT COMMENT '权限列表(JSON格式)',
    `status` TINYINT DEFAULT 1 COMMENT '状态(0:禁用,1:正常)',
    `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    KEY `idx_role` (`role`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 16. 操作日志表
CREATE TABLE `operation_log` (
    `id` VARCHAR(32) NOT NULL COMMENT '日志ID',
    `user_id` VARCHAR(32) DEFAULT NULL COMMENT '用户ID',
    `user_type` VARCHAR(20) DEFAULT 'USER' COMMENT '用户类型(USER,ADMIN)',
    `operation` VARCHAR(100) NOT NULL COMMENT '操作类型',
    `method` VARCHAR(10) DEFAULT NULL COMMENT '请求方法',
    `url` VARCHAR(500) DEFAULT NULL COMMENT '请求URL',
    `ip` VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` VARCHAR(1000) DEFAULT NULL COMMENT '用户代理',
    `params` TEXT COMMENT '请求参数',
    `result` TEXT COMMENT '操作结果',
    `error_msg` TEXT COMMENT '错误信息',
    `execute_time` INT DEFAULT NULL COMMENT '执行时间(毫秒)',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_operation` (`operation`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- ========================================
-- 初始数据插入
-- ========================================

-- 插入默认管理员账号 (用户名: admin, 密码: admin123)
INSERT INTO `admin` (`id`, `username`, `password`, `nickname`, `role`, `status`) VALUES
('admin001', 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaVIE0y9rNOhK', '超级管理员', 'SUPER_ADMIN', 1);

-- 插入默认分类数据
INSERT INTO `category` (`id`, `name`, `type`, `sort_order`) VALUES
-- 电影分类
('cat_movie_001', '全部', 'MOVIE', 1),
('cat_movie_002', '动作', 'MOVIE', 2),
('cat_movie_003', '科幻', 'MOVIE', 3),
('cat_movie_004', '爱情', 'MOVIE', 4),
('cat_movie_005', '喜剧', 'MOVIE', 5),
('cat_movie_006', '恐怖', 'MOVIE', 6),
('cat_movie_007', '剧情', 'MOVIE', 7),
('cat_movie_008', '儿童', 'MOVIE', 8),
-- 应用分类
('cat_app_001', '推荐', 'APP', 1),
('cat_app_002', '播放器', 'APP', 2),
('cat_app_003', '音乐', 'APP', 3),
('cat_app_004', '教育', 'APP', 4),
('cat_app_005', '儿童', 'APP', 5),
('cat_app_006', '生活服务', 'APP', 6),
('cat_app_007', '新闻资讯', 'APP', 7),
-- 游戏分类
('cat_game_001', '热门', 'GAME', 1),
('cat_game_002', '动作', 'GAME', 2),
('cat_game_003', '策略', 'GAME', 3),
('cat_game_004', '竞速', 'GAME', 4),
('cat_game_005', '儿童', 'GAME', 5),
('cat_game_006', '益智', 'GAME', 6),
('cat_game_007', '体育', 'GAME', 7),
-- 商品分类
('cat_prod_001', '推荐', 'PRODUCT', 1),
('cat_prod_002', '智能电视', 'PRODUCT', 2),
('cat_prod_003', '音响设备', 'PRODUCT', 3),
('cat_prod_004', '手机平板', 'PRODUCT', 4),
('cat_prod_005', '电脑办公', 'PRODUCT', 5),
('cat_prod_006', '智能家居', 'PRODUCT', 6),
('cat_prod_007', '服饰', 'PRODUCT', 7);

-- 插入系统配置
INSERT INTO `system_config` (`id`, `config_key`, `config_value`, `config_desc`, `config_type`) VALUES
('cfg_001', 'site_name', 'TV影视平台', '网站名称', 'STRING'),
('cfg_002', 'site_logo', '/images/logo.png', '网站Logo', 'STRING'),
('cfg_003', 'default_page_size', '20', '默认分页大小', 'NUMBER'),
('cfg_004', 'max_page_size', '100', '最大分页大小', 'NUMBER'),
('cfg_005', 'upload_max_size', '10485760', '上传文件最大大小(字节)', 'NUMBER'),
('cfg_006', 'video_play_timeout', '30', '视频播放超时时间(秒)', 'NUMBER'),
('cfg_007', 'enable_user_register', 'true', '是否允许用户注册', 'BOOLEAN'),
('cfg_008', 'enable_guest_browse', 'true', '是否允许游客浏览', 'BOOLEAN');

-- ========================================
-- 创建视图
-- ========================================

-- 电影统计视图
CREATE VIEW `v_movie_stats` AS
SELECT
    m.id,
    m.title,
    m.type,
    m.category,
    m.rating,
    m.view_count,
    m.favorite_count,
    COUNT(DISTINCT uf.user_id) as actual_favorite_count,
    COUNT(DISTINCT ur.user_id) as rating_count,
    AVG(ur.rating) as avg_rating
FROM movie m
LEFT JOIN user_favorite uf ON m.id = uf.movie_id
LEFT JOIN user_rating ur ON m.id = ur.movie_id
WHERE m.status = 1
GROUP BY m.id;

-- 用户统计视图
CREATE VIEW `v_user_stats` AS
SELECT
    u.id,
    u.username,
    u.nickname,
    u.member_level,
    COUNT(DISTINCT uf.movie_id) as favorite_count,
    COUNT(DISTINCT uh.movie_id) as history_count,
    COUNT(DISTINCT ur.movie_id) as rating_count
FROM user u
LEFT JOIN user_favorite uf ON u.id = uf.user_id
LEFT JOIN user_history uh ON u.id = uh.user_id
LEFT JOIN user_rating ur ON u.id = ur.user_id
WHERE u.status = 1
GROUP BY u.id;
