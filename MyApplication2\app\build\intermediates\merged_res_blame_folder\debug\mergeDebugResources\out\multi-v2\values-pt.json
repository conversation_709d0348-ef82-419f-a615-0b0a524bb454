{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-pt/values-pt.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,403,503,610,720,5201", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "197,299,398,498,605,715,835,5297"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,634,748,847,948,1081,1210,1310,1426,1592,1717,1838,1941,2033,2165,2260,2363,2466,2572,2669,2783,2911,3032,3149,3252,3360,3471,3587,3692,3802,3889,3976,4080,4218,4373", "endColumns": "106,100,98,95,125,113,98,100,132,128,99,115,165,124,120,102,91,131,94,102,102,105,96,113,127,120,116,102,107,110,115,104,109,86,86,103,137,154,92", "endOffsets": "207,308,407,503,629,743,842,943,1076,1205,1305,1421,1587,1712,1833,1936,2028,2160,2255,2358,2461,2567,2664,2778,2906,3027,3144,3247,3355,3466,3582,3687,3797,3884,3971,4075,4213,4368,4461"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,947,1048,1147,1243,1369,1483,1582,1683,1816,1945,2045,2161,2327,2452,2573,2676,2768,2900,2995,3098,3201,3307,3404,3518,3646,3767,3884,3987,4095,4206,4322,4427,4537,4624,4711,4815,4953,5108", "endColumns": "106,100,98,95,125,113,98,100,132,128,99,115,165,124,120,102,91,131,94,102,102,105,96,113,127,120,116,102,107,110,115,104,109,86,86,103,137,154,92", "endOffsets": "942,1043,1142,1238,1364,1478,1577,1678,1811,1940,2040,2156,2322,2447,2568,2671,2763,2895,2990,3093,3196,3302,3399,3513,3641,3762,3879,3982,4090,4201,4317,4422,4532,4619,4706,4810,4948,5103,5196"}}]}]}