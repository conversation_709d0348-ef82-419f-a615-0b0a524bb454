package com.example.houduan.controller;

import com.example.houduan.common.PageResult;
import com.example.houduan.common.Result;
import com.example.houduan.dto.MovieResponse;
import com.example.houduan.dto.PlayHistoryRequest;
import com.example.houduan.service.UserHistoryService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

/**
 * 用户观看历史控制器
 */
@RestController
@RequestMapping("/history")
@RequiredArgsConstructor
public class UserHistoryController {

    private final UserHistoryService userHistoryService;

    /**
     * 保存播放历史
     */
    @PostMapping("/save")
    public Result<String> savePlayHistory(@Valid @RequestBody PlayHistoryRequest request, 
                                         Authentication authentication) {
        try {
            String userId = getUserId(authentication);
            userHistoryService.savePlayHistory(userId, request);
            return Result.success("保存播放历史成功");
        } catch (Exception e) {
            return Result.error("保存播放历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户观看历史
     */
    @GetMapping("/list")
    public Result<PageResult<MovieResponse>> getHistoryList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
        try {
            String userId = getUserId(authentication);
            PageResult<MovieResponse> result = userHistoryService.getUserHistory(userId, page, size);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("获取观看历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户观看历史数量
     */
    @GetMapping("/count")
    public Result<Long> getHistoryCount(Authentication authentication) {
        try {
            String userId = getUserId(authentication);
            long count = userHistoryService.getUserHistoryCount(userId);
            return Result.success(count);
        } catch (Exception e) {
            return Result.error("获取观看历史数量失败: " + e.getMessage());
        }
    }

    /**
     * 清空用户观看历史
     */
    @DeleteMapping("/clear")
    public Result<String> clearHistory(Authentication authentication) {
        try {
            String userId = getUserId(authentication);
            userHistoryService.clearUserHistory(userId);
            return Result.success("清空观看历史成功");
        } catch (Exception e) {
            return Result.error("清空观看历史失败: " + e.getMessage());
        }
    }

    /**
     * 删除特定的观看记录
     */
    @DeleteMapping("/{movieId}")
    public Result<String> deleteHistory(@PathVariable String movieId, Authentication authentication) {
        try {
            String userId = getUserId(authentication);
            userHistoryService.deleteHistory(userId, movieId);
            return Result.success("删除观看记录成功");
        } catch (Exception e) {
            return Result.error("删除观看记录失败: " + e.getMessage());
        }
    }

    /**
     * 从认证信息中获取用户ID
     */
    private String getUserId(Authentication authentication) {
        if (authentication == null || authentication.getPrincipal() == null) {
            throw new RuntimeException("用户未登录");
        }
        
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        return userDetails.getUsername(); // 这里需要根据实际情况调整获取用户ID的方式
    }
}
