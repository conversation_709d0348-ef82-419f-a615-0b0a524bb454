{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,296,390,484,577,670,4677", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "192,291,385,479,572,665,761,4773"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,405,495,608,709,803,892,1005,1117,1214,1325,1428,1536,1643,1740,1828,1936,2023,2122,2219,2318,2407,2513,2607,2709,2810,2907,3008,3105,3208,3302,3402,3489,3569,3660,3792,3935", "endColumns": "106,100,91,89,112,100,93,88,112,111,96,110,102,107,106,96,87,107,86,98,96,98,88,105,93,101,100,96,100,96,102,93,99,86,79,90,131,142,80", "endOffsets": "207,308,400,490,603,704,798,887,1000,1112,1209,1320,1423,1531,1638,1735,1823,1931,2018,2117,2214,2313,2402,2508,2602,2704,2805,2902,3003,3100,3203,3297,3397,3484,3564,3655,3787,3930,4011"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,873,974,1066,1156,1269,1370,1464,1553,1666,1778,1875,1986,2089,2197,2304,2401,2489,2597,2684,2783,2880,2979,3068,3174,3268,3370,3471,3568,3669,3766,3869,3963,4063,4150,4230,4321,4453,4596", "endColumns": "106,100,91,89,112,100,93,88,112,111,96,110,102,107,106,96,87,107,86,98,96,98,88,105,93,101,100,96,100,96,102,93,99,86,79,90,131,142,80", "endOffsets": "868,969,1061,1151,1264,1365,1459,1548,1661,1773,1870,1981,2084,2192,2299,2396,2484,2592,2679,2778,2875,2974,3063,3169,3263,3365,3466,3563,3664,3761,3864,3958,4058,4145,4225,4316,4448,4591,4672"}}]}]}