package com.example.houduan.controller;

import com.example.houduan.dto.ProductQueryRequest;
import com.example.houduan.dto.ProductResponse;
import com.example.houduan.common.PageResult;
import com.example.houduan.common.Result;
import com.example.houduan.entity.Product;
import com.example.houduan.service.ProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品控制器
 */
@RestController
@RequestMapping("/products")
@RequiredArgsConstructor
public class ProductController {

    private final ProductService productService;

    /**
     * 分页查询商品列表
     */
    @GetMapping("/list")
    public Result<PageResult<ProductResponse>> getProductList(ProductQueryRequest request) {
        try {
            PageResult<ProductResponse> result = productService.getProductList(request);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("查询商品列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取商品详情
     */
    @GetMapping("/{id}")
    public Result<ProductResponse> getProductById(@PathVariable String id) {
        try {
            ProductResponse product = productService.getProductById(id);
            return Result.success(product);
        } catch (Exception e) {
            return Result.error("获取商品详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取推荐商品
     */
    @GetMapping("/recommended")
    public Result<List<ProductResponse>> getRecommendedProducts(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<ProductResponse> products = productService.getRecommendedProducts(limit);
            return Result.success(products);
        } catch (Exception e) {
            return Result.error("获取推荐商品失败: " + e.getMessage());
        }
    }

    /**
     * 获取热销商品
     */
    @GetMapping("/hot")
    public Result<List<ProductResponse>> getHotProducts(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<ProductResponse> products = productService.getHotProducts(limit);
            return Result.success(products);
        } catch (Exception e) {
            return Result.error("获取热销商品失败: " + e.getMessage());
        }
    }

    /**
     * 创建商品
     */
    @PostMapping
    public Result<ProductResponse> createProduct(@RequestBody Product product) {
        try {
            ProductResponse response = productService.createProduct(product);
            return Result.success("创建成功", response);
        } catch (Exception e) {
            return Result.error("创建商品失败: " + e.getMessage());
        }
    }

    /**
     * 更新商品
     */
    @PutMapping("/{id}")
    public Result<ProductResponse> updateProduct(@PathVariable String id, @RequestBody Product product) {
        try {
            ProductResponse response = productService.updateProduct(id, product);
            return Result.success("更新成功", response);
        } catch (Exception e) {
            return Result.error("更新商品失败: " + e.getMessage());
        }
    }

    /**
     * 删除商品
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteProduct(@PathVariable String id) {
        try {
            productService.deleteProduct(id);
            return Result.success("删除成功");
        } catch (Exception e) {
            return Result.error("删除商品失败: " + e.getMessage());
        }
    }
}
