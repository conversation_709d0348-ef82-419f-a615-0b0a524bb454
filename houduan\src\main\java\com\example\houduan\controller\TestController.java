package com.example.houduan.controller;

import com.example.houduan.common.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/test")
public class TestController {

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("message", "MovieTV Backend Service is running");
        
        return Result.success("服务运行正常", data);
    }

    /**
     * 公开接口测试
     */
    @GetMapping("/public")
    public Result<String> publicTest() {
        return Result.success("公开接口访问成功");
    }

    /**
     * 需要认证的接口测试
     */
    @GetMapping("/auth")
    public Result<String> authTest() {
        return Result.success("认证接口访问成功");
    }

}
