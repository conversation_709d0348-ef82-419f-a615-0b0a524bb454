<template>
  <div class="dashboard">
    <h1>仪表盘</h1>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon movies">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.movieCount || 0 }}</h3>
              <p>影视总数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon users">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.userCount || 0 }}</h3>
              <p>用户总数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon categories">
              <el-icon><Folder /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.categoryCount || 0 }}</h3>
              <p>分类总数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon views">
              <el-icon><View /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.totalViews || 0 }}</h3>
              <p>总播放量</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快捷操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>快捷操作</span>
          </template>
          <div class="action-buttons">
            <el-button type="primary" @click="$router.push('/movies/add')">
              <el-icon><Plus /></el-icon>
              添加影视
            </el-button>
            <el-button type="success" @click="$router.push('/categories/list')">
              <el-icon><Folder /></el-icon>
              管理分类
            </el-button>
            <el-button type="info" @click="$router.push('/users/list')">
              <el-icon><User /></el-icon>
              用户管理
            </el-button>
            <el-button type="warning" @click="$router.push('/system/config')">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>系统信息</span>
          </template>
          <div class="system-info">
            <p><strong>系统版本:</strong> v1.0.0</p>
            <p><strong>运行时间:</strong> {{ uptime }}</p>
            <p><strong>数据库:</strong> MySQL 8.0</p>
            <p><strong>后端框架:</strong> Spring Boot 3.5.3</p>
            <p><strong>前端框架:</strong> Vue 3 + Element Plus</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近活动 -->
    <el-card class="recent-activity">
      <template #header>
        <span>最近活动</span>
      </template>
      <el-timeline>
        <el-timeline-item timestamp="2025-07-03 10:30" color="#0bbd87">
          系统启动成功
        </el-timeline-item>
        <el-timeline-item timestamp="2025-07-03 10:28" color="#409eff">
          用户 testuser2 注册成功
        </el-timeline-item>
        <el-timeline-item timestamp="2025-07-03 10:25" color="#409eff">
          数据库连接正常
        </el-timeline-item>
        <el-timeline-item timestamp="2025-07-03 10:20" color="#909399">
          后台管理系统初始化完成
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { VideoPlay, User, Folder, View, Plus, Setting } from '@element-plus/icons-vue'
import { systemAPI } from '@/api'

const stats = ref({
  movieCount: 0,
  userCount: 1,
  categoryCount: 8,
  totalViews: 0
})

const uptime = ref('刚刚启动')

onMounted(async () => {
  try {
    // 模拟数据，避免API调用失败
    stats.value = {
      movieCount: 156,
      userCount: 1,
      categoryCount: 8,
      totalViews: 12580
    }

    // 计算运行时间
    const startTime = new Date('2025-07-03 10:20:00')
    const now = new Date()
    const diff = Math.floor((now - startTime) / 1000 / 60)
    uptime.value = diff > 0 ? `${diff} 分钟` : '刚刚启动'

    console.log('仪表盘数据加载成功')
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 设置默认值
    stats.value = {
      movieCount: 0,
      userCount: 1,
      categoryCount: 8,
      totalViews: 0
    }
  }
})
</script>

<style scoped>
.dashboard h1 {
  margin-bottom: 20px;
  color: #333;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.movies {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.users {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.categories {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.views {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
  margin: 0;
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.stat-info p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 14px;
}

.quick-actions {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.action-buttons .el-button {
  flex: 1;
  min-width: 120px;
}

.system-info p {
  margin: 10px 0;
  color: #666;
}

.recent-activity {
  margin-top: 20px;
}
</style>
