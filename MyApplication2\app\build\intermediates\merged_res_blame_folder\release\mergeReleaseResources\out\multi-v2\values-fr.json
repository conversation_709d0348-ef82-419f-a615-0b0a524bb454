{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-fr/values-fr.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,404,506,610,714,5351", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "198,300,399,501,605,709,827,5447"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,631,746,846,940,1072,1200,1306,1431,1608,1736,1860,1967,2058,2191,2283,2393,2507,2623,2724,2845,2971,3106,3237,3353,3475,3588,3714,3818,3935,4022,4110,4218,4362,4526", "endColumns": "106,100,98,95,122,114,99,93,131,127,105,124,176,127,123,106,90,132,91,109,113,115,100,120,125,134,130,115,121,112,125,103,116,86,87,107,143,163,97", "endOffsets": "207,308,407,503,626,741,841,935,1067,1195,1301,1426,1603,1731,1855,1962,2053,2186,2278,2388,2502,2618,2719,2840,2966,3101,3232,3348,3470,3583,3709,3813,3930,4017,4105,4213,4357,4521,4619"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,939,1040,1139,1235,1358,1473,1573,1667,1799,1927,2033,2158,2335,2463,2587,2694,2785,2918,3010,3120,3234,3350,3451,3572,3698,3833,3964,4080,4202,4315,4441,4545,4662,4749,4837,4945,5089,5253", "endColumns": "106,100,98,95,122,114,99,93,131,127,105,124,176,127,123,106,90,132,91,109,113,115,100,120,125,134,130,115,121,112,125,103,116,86,87,107,143,163,97", "endOffsets": "934,1035,1134,1230,1353,1468,1568,1662,1794,1922,2028,2153,2330,2458,2582,2689,2780,2913,3005,3115,3229,3345,3446,3567,3693,3828,3959,4075,4197,4310,4436,4540,4657,4744,4832,4940,5084,5248,5346"}}]}]}