<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="12dp"
    android:focusable="true"
    android:clickable="true"
    android:background="@drawable/navigation_item_background">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginBottom="8dp"
        android:scaleType="centerInside" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end" />

</LinearLayout>
