<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>MovieTV 后台管理系统</h2>
        <p>影视内容管理平台</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleLogin"
            class="login-btn"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-footer">
        <p>默认账号：admin / admin123</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { authAPI } from '@/api'
import { setToken, setUserInfo, clearAuthData } from '@/utils/auth'

const router = useRouter()

const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive({
  username: 'admin',
  password: 'admin123'
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    // 清理之前的认证数据
    clearAuthData()

    console.log('开始登录，用户名:', loginForm.username)

    // 使用API登录（包含模拟逻辑）
    const response = await authAPI.login(loginForm)

    console.log('登录响应:', response)

    if (response && response.success) {
      // 确保数据存在且格式正确
      if (response.data && response.data.token) {
        setToken(response.data.token)

        // 检查用户数据的结构
        if (response.data.user) {
          setUserInfo(response.data.user)
        } else if (response.data.id) {
          // 如果用户信息直接在 data 中，而不是在 user 字段中
          const userInfo = {
            id: response.data.id,
            username: response.data.username,
            nickname: response.data.nickname,
            email: response.data.email,
            memberLevel: response.data.memberLevel
          }
          setUserInfo(userInfo)
        }

        ElMessage.success('登录成功')

        // 延迟跳转，确保数据已保存
        setTimeout(() => {
          router.push('/dashboard').catch(() => {
            // 如果路由跳转失败，尝试直接修改location
            window.location.href = '/'
          })
        }, 100)
      } else {
        ElMessage.error('登录响应数据格式错误')
      }
    } else {
      ElMessage.error('登录失败：响应格式错误')
    }

  } catch (error) {
    console.error('登录失败详细信息:', error)
    ElMessage.error(error.message || '登录失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.login-box {
  width: 400px;
  max-width: 90vw;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
  font-weight: 600;
}

.login-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.login-form {
  margin-bottom: 20px;
}

.login-btn {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.login-footer {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin-top: 20px;
}

.login-footer p {
  margin: 0;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
}
</style>
