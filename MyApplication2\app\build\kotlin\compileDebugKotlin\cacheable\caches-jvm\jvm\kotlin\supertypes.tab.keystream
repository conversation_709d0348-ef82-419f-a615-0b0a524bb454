/com.example.myapplicationtv.BrowseErrorActivity?com.example.myapplicationtv.BrowseErrorActivity.SpinnerFragment)com.example.myapplicationtv.CardPresenter+com.example.myapplicationtv.DetailsActivity7com.example.myapplicationtv.DetailsDescriptionPresenter)com.example.myapplicationtv.ErrorFragment(com.example.myapplicationtv.GameActivity(com.example.myapplicationtv.GameFragment)com.example.myapplicationtv.LoginActivity)com.example.myapplicationtv.LoginFragment(com.example.myapplicationtv.MainActivity/com.example.myapplicationtv.MainContentFragment(<EMAIL>=com.example.myapplicationtv.MainFragment.UpdateBackgroundTask:com.example.myapplicationtv.MainFragment.GridItemPresenter!com.example.myapplicationtv.Movie'com.example.myapplicationtv.ContentItem,com.example.myapplicationtv.PlaybackActivity1com.example.myapplicationtv.PlaybackVideoFragment*com.example.myapplicationtv.SearchActivity*com.example.myapplicationtv.SearchFragmentBcom.example.myapplicationtv.SearchFragment.ItemViewClickedListener(com.example.myapplicationtv.ShopActivity(com.example.myapplicationtv.ShopFragment)com.example.myapplicationtv.TVApplication(<EMAIL>=com.example.myapplicationtv.adapter.HotGameAdapter.ViewHolder5com.example.myapplicationtv.adapter.HotProductAdapterGcom.example.myapplicationtv.adapter.HotProductAdapter.ProductViewHolder5com.example.myapplicationtv.adapter.NavigationAdapterJcom.example.myapplicationtv.adapter.NavigationAdapter.NavigationViewHolder:com.example.myapplicationtv.adapter.ProductCategoryAdapterMcom.example.myapplicationtv.adapter.ProductCategoryAdapter.CategoryViewHolder/com.example.myapplicationtv.base.BaseTVActivity/com.example.myapplicationtv.model.MovieResponse-com.example.myapplicationtv.model.AppResponse.com.example.myapplicationtv.model.GameResponse1com.example.myapplicationtv.model.ProductResponse2com.example.myapplicationtv.model.CategoryResponse.com.example.myapplicationtv.model.UserResponse/com.example.myapplicationtv.model.LoginResponse2com.example.myapplicationtv.model.FavoriteResponse1com.example.myapplicationtv.model.HistoryResponse-com.example.myapplicationtv.model.ContentType?com.example.myapplicationtv.network.FlexibleBooleanDeserializer3com.example.myapplicationtv.viewmodel.GameViewModel4com.example.myapplicationtv.viewmodel.LoginViewModel3com.example.myapplicationtv.viewmodel.MainViewModel7com.example.myapplicationtv.viewmodel.PlaybackViewModel5com.example.myapplicationtv.viewmodel.SearchViewModel3com.example.myapplicationtv.viewmodel.ShopViewModel9com.example.myapplicationtv.viewmodel.UserCenterViewModel9com.example.myapplicationtv.widget.FocusableImageCardView/com.example.myapplicationtv.ApplicationActivity/com.example.myapplicationtv.ApplicationFragment9com.example.myapplicationtv.adapter.RecommendedAppAdapterGcom.example.myapplicationtv.adapter.RecommendedAppAdapter.AppViewHolder:com.example.myapplicationtv.viewmodel.ApplicationViewModel               