/com.example.myapplicationtv.BrowseErrorActivity?com.example.myapplicationtv.BrowseErrorActivity.SpinnerFragment)com.example.myapplicationtv.CardPresenter+com.example.myapplicationtv.DetailsActivity7com.example.myapplicationtv.DetailsDescriptionPresenter)com.example.myapplicationtv.ErrorFragment)com.example.myapplicationtv.LoginActivity)com.example.myapplicationtv.LoginFragment(com.example.myapplicationtv.MainActivity/com.example.myapplicationtv.MainContentFragment(<EMAIL>=com.example.myapplicationtv.MainFragment.UpdateBackgroundTask:com.example.myapplicationtv.MainFragment.GridItemPresenter!com.example.myapplicationtv.Movie'com.example.myapplicationtv.ContentItem,com.example.myapplicationtv.PlaybackActivity1com.example.myapplicationtv.PlaybackVideoFragment*com.example.myapplicationtv.SearchActivity*com.example.myapplicationtv.SearchFragmentBcom.example.myapplicationtv.SearchFragment.ItemViewClickedListener)com.example.myapplicationtv.TVApplication(<EMAIL>/com.example.myapplicationtv.base.BaseTVActivity/com.example.myapplicationtv.model.MovieResponse-com.example.myapplicationtv.model.AppResponse.com.example.myapplicationtv.model.GameResponse1com.example.myapplicationtv.model.ProductResponse2com.example.myapplicationtv.model.CategoryResponse.com.example.myapplicationtv.model.UserResponse/com.example.myapplicationtv.model.LoginResponse2com.example.myapplicationtv.model.FavoriteResponse1com.example.myapplicationtv.model.HistoryResponse-com.example.myapplicationtv.model.ContentType?com.example.myapplicationtv.network.FlexibleBooleanDeserializer4com.example.myapplicationtv.viewmodel.LoginViewModel3com.example.myapplicationtv.viewmodel.MainViewModel7com.example.myapplicationtv.viewmodel.PlaybackViewModel5com.example.myapplicationtv.viewmodel.SearchViewModel9com.example.myapplicationtv.viewmodel.UserCenterViewModel9com.example.myapplicationtv.widget.FocusableImageCardView(com.example.myapplicationtv.GameActivity(<EMAIL>(com.example.myapplicationtv.ShopActivity(<EMAIL>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    