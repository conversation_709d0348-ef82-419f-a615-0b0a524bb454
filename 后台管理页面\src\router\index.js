import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'
import { isLoggedIn, isTokenValid } from '@/utils/auth'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '仪表盘', icon: 'dashboard' }
      }
    ]
  },
  {
    path: '/movies',
    component: Layout,
    redirect: '/movies/list',
    meta: { title: '影视管理', icon: 'film' },
    children: [
      {
        path: 'list',
        name: 'MovieList',
        component: () => import('@/views/movies/List.vue'),
        meta: { title: '影视列表' }
      },
      {
        path: 'add',
        name: 'MovieAdd',
        component: () => import('@/views/movies/Edit.vue'),
        meta: { title: '添加影视' }
      },
      {
        path: 'edit/:id',
        name: 'MovieEdit',
        component: () => import('@/views/movies/Edit.vue'),
        meta: { title: '编辑影视' }
      }
    ]
  },
  {
    path: '/apps',
    component: Layout,
    redirect: '/apps/list',
    meta: { title: '应用管理', icon: 'mobile' },
    children: [
      {
        path: 'list',
        name: 'AppList',
        component: () => import('@/views/apps/List.vue'),
        meta: { title: '应用列表' }
      },
      {
        path: 'add',
        name: 'AppAdd',
        component: () => import('@/views/apps/Edit.vue'),
        meta: { title: '添加应用' }
      },
      {
        path: 'edit/:id',
        name: 'AppEdit',
        component: () => import('@/views/apps/Edit.vue'),
        meta: { title: '编辑应用' }
      }
    ]
  },
  {
    path: '/games',
    component: Layout,
    redirect: '/games/list',
    meta: { title: '游戏管理', icon: 'game' },
    children: [
      {
        path: 'list',
        name: 'GameList',
        component: () => import('@/views/games/List.vue'),
        meta: { title: '游戏列表' }
      },
      {
        path: 'add',
        name: 'GameAdd',
        component: () => import('@/views/games/Edit.vue'),
        meta: { title: '添加游戏' }
      },
      {
        path: 'edit/:id',
        name: 'GameEdit',
        component: () => import('@/views/games/Edit.vue'),
        meta: { title: '编辑游戏' }
      }
    ]
  },
  {
    path: '/products',
    component: Layout,
    redirect: '/products/list',
    meta: { title: '商品管理', icon: 'shopping' },
    children: [
      {
        path: 'list',
        name: 'ProductList',
        component: () => import('@/views/products/List.vue'),
        meta: { title: '商品列表' }
      },
      {
        path: 'add',
        name: 'ProductAdd',
        component: () => import('@/views/products/Edit.vue'),
        meta: { title: '添加商品' }
      },
      {
        path: 'edit/:id',
        name: 'ProductEdit',
        component: () => import('@/views/products/Edit.vue'),
        meta: { title: '编辑商品' }
      }
    ]
  },
  {
    path: '/categories',
    component: Layout,
    redirect: '/categories/list',
    meta: { title: '分类管理', icon: 'folder' },
    children: [
      {
        path: 'list',
        name: 'CategoryList',
        component: () => import('@/views/categories/List.vue'),
        meta: { title: '分类列表' }
      }
    ]
  },
  {
    path: '/users',
    component: Layout,
    redirect: '/users/list',
    meta: { title: '用户管理', icon: 'user' },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('@/views/users/List.vue'),
        meta: { title: '用户列表' }
      },
      {
        path: 'favorites',
        name: 'UserFavorites',
        component: () => import('@/views/users/Favorites.vue'),
        meta: { title: '用户收藏' }
      },
      {
        path: 'history',
        name: 'UserHistory',
        component: () => import('@/views/users/History.vue'),
        meta: { title: '观看历史' }
      }
    ]
  },
  {
    path: '/statistics',
    component: Layout,
    redirect: '/statistics/overview',
    meta: { title: '数据统计', icon: 'chart' },
    children: [
      {
        path: 'overview',
        name: 'StatisticsOverview',
        component: () => import('@/views/statistics/Overview.vue'),
        meta: { title: '数据概览' }
      },
      {
        path: 'content',
        name: 'ContentStatistics',
        component: () => import('@/views/statistics/Content.vue'),
        meta: { title: '内容统计' }
      },
      {
        path: 'user',
        name: 'UserStatistics',
        component: () => import('@/views/statistics/User.vue'),
        meta: { title: '用户统计' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/config',
    meta: { title: '系统管理', icon: 'setting' },
    children: [
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('@/views/system/Config.vue'),
        meta: { title: '系统配置' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const loggedIn = isLoggedIn()
  const tokenValid = isTokenValid()

  if (to.path === '/login') {
    if (loggedIn && tokenValid) {
      next('/')
    } else {
      next()
    }
  } else {
    if (loggedIn && tokenValid) {
      next()
    } else {
      next('/login')
    }
  }
})

export default router
