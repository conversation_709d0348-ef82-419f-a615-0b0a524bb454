{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,296,390,484,577,670,4695", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "192,291,385,479,572,665,761,4791"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,405,495,608,709,803,892,1007,1121,1216,1327,1435,1543,1650,1747,1835,1942,2029,2128,2225,2324,2413,2519,2613,2715,2816,2913,3014,3114,3220,3317,3420,3507,3587,3678,3810,3953", "endColumns": "106,100,91,89,112,100,93,88,114,113,94,110,107,107,106,96,87,106,86,98,96,98,88,105,93,101,100,96,100,99,105,96,102,86,79,90,131,142,80", "endOffsets": "207,308,400,490,603,704,798,887,1002,1116,1211,1322,1430,1538,1645,1742,1830,1937,2024,2123,2220,2319,2408,2514,2608,2710,2811,2908,3009,3109,3215,3312,3415,3502,3582,3673,3805,3948,4029"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,873,974,1066,1156,1269,1370,1464,1553,1668,1782,1877,1988,2096,2204,2311,2408,2496,2603,2690,2789,2886,2985,3074,3180,3274,3376,3477,3574,3675,3775,3881,3978,4081,4168,4248,4339,4471,4614", "endColumns": "106,100,91,89,112,100,93,88,114,113,94,110,107,107,106,96,87,106,86,98,96,98,88,105,93,101,100,96,100,99,105,96,102,86,79,90,131,142,80", "endOffsets": "868,969,1061,1151,1264,1365,1459,1548,1663,1777,1872,1983,2091,2199,2306,2403,2491,2598,2685,2784,2881,2980,3069,3175,3269,3371,3472,3569,3670,3770,3876,3973,4076,4163,4243,4334,4466,4609,4690"}}]}]}