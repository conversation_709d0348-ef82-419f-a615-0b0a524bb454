<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="搜索游戏名称"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.category"
        placeholder="选择分类"
        clearable
        style="width: 150px"
        class="filter-item"
      >
        <el-option
          v-for="item in categoryOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="Plus"
        @click="handleCreate"
      >
        添加游戏
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="{ row }">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="游戏封面" width="100" align="center">
        <template #default="{ row }">
          <el-image
            style="width: 50px; height: 50px"
            :src="row.cover || row.icon"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column label="游戏名称" min-width="150">
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分类" width="100" align="center">
        <template #default="{ row }">
          <span>{{ row.category }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开发商" width="120" align="center">
        <template #default="{ row }">
          <span>{{ row.developer }}</span>
        </template>
      </el-table-column>
      <el-table-column label="大小" width="100" align="center">
        <template #default="{ row }">
          <span>{{ row.sizeFormatted }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评分" width="100" align="center">
        <template #default="{ row }">
          <el-rate
            v-model="row.rating"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}"
          />
        </template>
      </el-table-column>
      <el-table-column label="下载量" width="120" align="center">
        <template #default="{ row }">
          <span>{{ row.downloadCountFormatted }}</span>
        </template>
      </el-table-column>
      <el-table-column label="推荐" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.isRecommended ? 'success' : 'info'">
            {{ row.isRecommended ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="精选" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.isFeatured ? 'warning' : 'info'">
            {{ row.isFeatured ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'danger'">
            {{ row.status === 'ACTIVE' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button
            v-if="row.status === 'ACTIVE'"
            size="small"
            type="warning"
            @click="handleModifyStatus(row, 'INACTIVE')"
          >
            禁用
          </el-button>
          <el-button
            v-else
            size="small"
            type="success"
            @click="handleModifyStatus(row, 'ACTIVE')"
          >
            启用
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getGames, deleteGame, updateGameStatus } from '@/api/games'
import { getCategories } from '@/api/categories'
import Pagination from '@/components/Pagination/index.vue'

const router = useRouter()

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = reactive({
  page: 1,
  size: 20,
  keyword: undefined,
  category: undefined,
  sort: 'latest'
})
const categoryOptions = ref([])

onMounted(() => {
  getList()
  getCategoryList()
})
const getList = async () => {
  try {
    listLoading.value = true
    const response = await getGames(listQuery)
    list.value = response.data.list || []
    total.value = response.data.total || 0
  } catch (error) {
    console.error('获取游戏列表失败:', error)
    // 如果API失败，使用模拟数据
    const mockData = {
      data: {
        list: [
          {
            id: 1,
            name: '王者荣耀',
            icon: '/images/games/wzry.jpg',
            category: '竞技',
            developer: '腾讯游戏',
            rating: 4.8,
            downloads: 500000000,
            status: 'ACTIVE',
            featured: true,
            createdAt: '2025-01-01'
          },
          {
            id: 2,
            name: '和平精英',
            icon: '/images/games/hpjy.jpg',
            category: '射击',
            developer: '腾讯游戏',
            rating: 4.6,
            downloads: 300000000,
            status: 'ACTIVE',
            featured: false,
            createdAt: '2025-01-02'
          }
        ],
        total: 2
      }
    }
    list.value = mockData.data.list
    total.value = mockData.data.total
    ElMessage.warning('API调用失败，显示模拟数据')
  } finally {
    listLoading.value = false
  }
}

const getCategoryList = async () => {
  try {
    const response = await getCategories({ type: 'GAME' })
    categoryOptions.value = response.data || []
  } catch (error) {
    console.error('获取分类失败:', error)
    // 如果API失败，使用模拟数据
    categoryOptions.value = [
      { id: 1, name: '竞技' },
      { id: 2, name: '射击' },
      { id: 3, name: '角色扮演' },
      { id: 4, name: '策略' }
    ]
  }
}

const handleFilter = () => {
  listQuery.page = 1
  getList()
}

const handleCreate = () => {
  router.push('/games/add')
}

const handleUpdate = (row) => {
  router.push(`/games/edit/${row.id}`)
}

const handleModifyStatus = async (row, status) => {
  try {
    await updateGameStatus(row.id, status)
    ElMessage.success('操作成功')
    row.status = status
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('操作失败')
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('此操作将永久删除该游戏, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteGame(row.id)
    ElMessage.success('删除成功!')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
</style>
