package com.example.houduan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 电影实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "movie")
public class Movie {

    @Id
    @Column(length = 32)
    private String id;

    /**
     * 电影名称
     */
    private String title;

    /**
     * 原名
     */
    private String originalTitle;

    /**
     * 封面图片URL
     */
    private String cover;

    /**
     * 海报图片URL
     */
    private String poster;

    /**
     * 评分(0.0-10.0)
     */
    private BigDecimal rating;

    /**
     * 评分人数
     */
    private Integer ratingCount;

    /**
     * 类型(电影,电视剧,综艺,动漫)
     */
    private String type;

    /**
     * 分类(动作,科幻,爱情等)
     */
    private String category;

    /**
     * 地区
     */
    private String area;

    /**
     * 语言
     */
    private String language;

    /**
     * 上映年份
     */
    private Integer year;

    /**
     * 时长(分钟)
     */
    private Integer duration;

    /**
     * 导演
     */
    private String director;

    /**
     * 主演
     */
    private String actors;

    /**
     * 简介
     */
    private String description;

    /**
     * 播放地址
     */
    private String playUrl;

    /**
     * 预告片地址
     */
    private String trailerUrl;

    /**
     * 播放次数
     */
    private Long viewCount;

    /**
     * 收藏次数
     */
    private Integer favoriteCount;

    /**
     * 是否推荐(0:否,1:是)
     */
    private Integer isRecommended;

    /**
     * 是否热门(0:否,1:是)
     */
    private Integer isHot;

    /**
     * 是否最新(0:否,1:是)
     */
    private Integer isNew;

    /**
     * 状态(0:下架,1:上架)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
