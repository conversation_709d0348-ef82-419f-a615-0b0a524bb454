@echo off
echo ========================================
echo MovieTV 后端服务启动脚本
echo ========================================
echo.

echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请确保已安装Java 17
    pause
    exit /b 1
)

echo.
echo 检查MySQL服务...
sc query mysql >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告：MySQL服务未运行，尝试启动...
    net start mysql
)

echo.
echo 进入后端目录...
cd /d "%~dp0houduan"

echo.
echo 启动后端服务...
echo 使用已编译的JAR文件启动...
java -jar target/houduan-0.0.1-SNAPSHOT.jar

echo.
echo 后端服务已停止
pause
