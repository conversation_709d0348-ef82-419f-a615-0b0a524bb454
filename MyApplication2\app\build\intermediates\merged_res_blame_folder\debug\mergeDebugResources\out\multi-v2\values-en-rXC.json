{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-29:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,301,506,707,908,1115,1320,9733", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "296,501,702,903,1110,1315,1527,9932"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,313,516,713,909,1130,1344,1541,1739,1972,2202,2409,2629,2856,3078,3297,3503,3694,3924,4113,4316,4521,4724,4917,5129,5337,5550,5761,5962,6170,6382,6599,6807,7018,7207,7393,7594,7846,8114", "endColumns": "207,202,196,195,220,213,196,197,232,229,206,219,226,221,218,205,190,229,188,202,204,202,192,211,207,212,210,200,207,211,216,207,210,188,185,200,251,267,191", "endOffsets": "308,511,708,904,1125,1339,1536,1734,1967,2197,2404,2624,2851,3073,3292,3498,3689,3919,4108,4311,4516,4719,4912,5124,5332,5545,5756,5957,6165,6377,6594,6802,7013,7202,7388,7589,7841,8109,8301"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1532,1740,1943,2140,2336,2557,2771,2968,3166,3399,3629,3836,4056,4283,4505,4724,4930,5121,5351,5540,5743,5948,6151,6344,6556,6764,6977,7188,7389,7597,7809,8026,8234,8445,8634,8820,9021,9273,9541", "endColumns": "207,202,196,195,220,213,196,197,232,229,206,219,226,221,218,205,190,229,188,202,204,202,192,211,207,212,210,200,207,211,216,207,210,188,185,200,251,267,191", "endOffsets": "1735,1938,2135,2331,2552,2766,2963,3161,3394,3624,3831,4051,4278,4500,4719,4925,5116,5346,5535,5738,5943,6146,6339,6551,6759,6972,7183,7384,7592,7804,8021,8229,8440,8629,8815,9016,9268,9536,9728"}}]}]}