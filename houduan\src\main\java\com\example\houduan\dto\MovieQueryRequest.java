package com.example.houduan.dto;

import lombok.Data;

/**
 * 电影查询请求DTO
 */
@Data
public class MovieQueryRequest {

    private String keyword; // 搜索关键词
    private String type; // 类型(电影,电视剧,综艺,动漫)
    private String category; // 分类
    private String area; // 地区
    private String language; // 语言
    private Integer year; // 年份
    private Integer isRecommended; // 是否推荐
    private Integer isHot; // 是否热门
    private Integer isNew; // 是否最新
    private Integer status; // 状态
    private String sortBy; // 排序字段(rating,viewCount,createTime)
    private String sortOrder; // 排序方向(asc,desc)
    private Integer page = 1; // 页码
    private Integer size = 20; // 每页大小
}
