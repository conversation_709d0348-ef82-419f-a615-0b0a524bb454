package com.example.myapplicationtv.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable
import java.math.BigDecimal

/**
 * API通用响应格式
 */
data class ApiResponse<T>(
    val code: Int,
    val message: String,
    val data: T?,
    val success: Boolean,
    val timestamp: Long
)

/**
 * 分页结果
 */
data class PageResult<T>(
    val list: List<T>,
    val total: Long,
    val page: Int,
    val size: Int,
    val pages: Int
)

/**
 * 电影响应模型
 */
data class MovieResponse(
    val id: String,
    val title: String,
    val originalTitle: String?,
    val type: String,
    val category: String,
    val area: String?,
    val language: String?,
    val year: Int?,
    val duration: Int?,
    val director: String?,
    val actors: String?,
    val cover: String?,
    val poster: String?,
    val playUrl: String?,
    val trailerUrl: String?,
    val description: String?,
    val rating: BigDecimal?,
    val ratingCount: Int?,
    val viewCount: Long?,
    val isRecommended: Boolean?,
    val isHot: Boolean?,
    val isNew: Boolean?,
    val status: Int,
    val createTime: String?,
    val updateTime: String?
) : Serializable

/**
 * 应用响应模型
 */
data class AppResponse(
    val id: String,
    val name: String,
    val icon: String?,
    val category: String,
    val version: String?,
    val size: Long?,
    val sizeFormatted: String?,
    val rating: BigDecimal?,
    val ratingCount: Int?,
    val downloadCount: Long?,
    val downloadCountFormatted: String?,
    val description: String?,
    val downloadUrl: String?,
    val screenshots: List<String>?,
    val isRecommended: Boolean?,
    val status: Int,
    val createTime: String?,
    val updateTime: String?
) : Serializable

/**
 * 游戏响应模型
 */
data class GameResponse(
    val id: String,
    val name: String,
    val cover: String?,
    val icon: String?,
    val category: String,
    val version: String?,
    val size: Long?,
    val sizeFormatted: String?,
    val rating: BigDecimal?,
    val ratingCount: Int?,
    val playCount: Long?,
    val playCountFormatted: String?,
    val description: String?,
    val playUrl: String?,
    val screenshots: List<String>?,
    val isRecommended: Boolean?,
    val isFeatured: Boolean?,
    val status: Int,
    val createTime: String?,
    val updateTime: String?
) : Serializable

/**
 * 商品响应模型
 */
data class ProductResponse(
    val id: String,
    val name: String,
    val image: String?,
    val images: List<String>?,
    val category: String,
    val brand: String?,
    val price: BigDecimal,
    val originalPrice: BigDecimal?,
    val discount: BigDecimal?,
    val stock: Int?,
    val sales: Int?,
    val rating: BigDecimal?,
    val ratingCount: Int?,
    val description: String?,
    val specifications: Map<String, Any>?,
    val isRecommended: Boolean?,
    val isHot: Boolean?,
    val inStock: Boolean?,
    val status: Int,
    val createTime: String?,
    val updateTime: String?
) : Serializable

/**
 * 分类响应模型
 */
data class CategoryResponse(
    val id: String,
    val name: String,
    val type: String,
    val icon: String?,
    val sortOrder: Int,
    val status: Int,
    val createTime: String?,
    val updateTime: String?
) : Serializable

/**
 * 用户响应模型
 */
data class UserResponse(
    val id: String,
    val username: String,
    val nickname: String?,
    val phone: String?,
    val email: String?,
    val avatar: String?,
    val memberLevel: String,
    val status: Int,
    val createTime: String?,
    val updateTime: String?
) : Serializable

/**
 * 登录请求模型
 */
data class LoginRequest(
    val username: String,
    val password: String
)

/**
 * 登录响应模型
 */
data class LoginResponse(
    val token: String,
    val user: UserResponse
) : Serializable

/**
 * 注册请求模型
 */
data class RegisterRequest(
    val username: String,
    val password: String,
    val confirmPassword: String,
    val phone: String?,
    val email: String?,
    val nickname: String?
)

/**
 * 收藏请求模型
 */
data class FavoriteRequest(
    val contentId: String,
    val contentType: String // MOVIE, APP, GAME, PRODUCT
)

/**
 * 收藏响应模型
 */
data class FavoriteResponse(
    val id: String,
    val userId: String,
    val contentId: String,
    val contentType: String,
    val createTime: String?
) : Serializable

/**
 * 历史记录请求模型
 */
data class HistoryRequest(
    val contentId: String,
    val contentType: String,
    val watchTime: Int?,
    val totalTime: Int?
)

/**
 * 历史记录响应模型
 */
data class HistoryResponse(
    val id: String,
    val userId: String,
    val contentId: String,
    val contentType: String,
    val watchTime: Int?,
    val totalTime: Int?,
    val createTime: String?,
    val updateTime: String?
) : Serializable

/**
 * 评分请求模型
 */
data class RatingRequest(
    val contentId: String,
    val contentType: String,
    val rating: BigDecimal,
    val comment: String?
)

/**
 * 内容类型枚举
 */
enum class ContentType(val value: String) {
    MOVIE("MOVIE"),
    APP("APP"),
    GAME("GAME"),
    PRODUCT("PRODUCT")
}
