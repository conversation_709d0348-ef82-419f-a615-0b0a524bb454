package com.example.houduan.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品查询请求DTO
 */
@Data
public class ProductQueryRequest {

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页数量
     */
    private Integer size = 10;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 分类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 最低价格
     */
    private BigDecimal minPrice;

    /**
     * 最高价格
     */
    private BigDecimal maxPrice;

    /**
     * 排序方式(latest:最新, hot:热销, price_asc:价格升序, price_desc:价格降序, rating:评分, sales:销量)
     */
    private String sort = "latest";

    /**
     * 是否推荐
     */
    private Integer isRecommended;

    /**
     * 是否热销
     */
    private Integer isHot;

    /**
     * 状态
     */
    private Integer status = 1;
}
