<template>
  <div class="app-container">
    <div class="filter-container">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="filter-item"
        @change="handleDateChange"
      />
      <el-select
        v-model="selectedMetric"
        placeholder="选择指标"
        style="width: 150px"
        class="filter-item"
        @change="handleMetricChange"
      >
        <el-option label="注册用户" value="register" />
        <el-option label="活跃用户" value="active" />
        <el-option label="收藏行为" value="favorite" />
        <el-option label="观看行为" value="watch" />
      </el-select>
    </div>

    <!-- 用户统计卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-total-users">
              <el-icon class="card-panel-icon"><User /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总用户数</div>
              <div class="card-panel-num">{{ userStats.totalUsers }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-active-users">
              <el-icon class="card-panel-icon"><UserFilled /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">活跃用户</div>
              <div class="card-panel-num">{{ userStats.activeUsers }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-new-users">
              <el-icon class="card-panel-icon"><Plus /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">新增用户</div>
              <div class="card-panel-num">{{ userStats.newUsers }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-retention">
              <el-icon class="card-panel-icon"><TrendCharts /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">留存率</div>
              <div class="card-panel-num">{{ userStats.retentionRate }}%</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>用户增长趋势</span>
            </div>
          </template>
          <div ref="growthChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>用户活跃度分布</span>
            </div>
          </template>
          <div ref="activityChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户行为统计 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>用户偏好分析</span>
            </div>
          </template>
          <div ref="preferenceChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>会员等级分布</span>
            </div>
          </template>
          <div ref="memberChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 活跃用户列表 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>活跃用户排行</span>
            </div>
          </template>
          <el-table :data="activeUsers" style="width: 100%">
            <el-table-column prop="rank" label="排名" width="80" align="center" />
            <el-table-column prop="name" label="用户名" width="150" />
            <el-table-column prop="email" label="邮箱" min-width="200" />
            <el-table-column prop="memberLevel" label="会员等级" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="getMemberLevelColor(row.memberLevel)">
                  {{ row.memberLevel }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="favoriteCount" label="收藏数" width="100" align="center" />
            <el-table-column prop="historyCount" label="观看数" width="100" align="center" />
            <el-table-column prop="lastLoginTime" label="最后登录" width="180" align="center">
              <template #default="{ row }">
                <span>{{ formatDate(row.lastLoginTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'danger'">
                  {{ row.status === 'ACTIVE' ? '正常' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getUserStatistics } from '@/api/statistics'

export default {
  name: 'UserStatistics',
  data() {
    return {
      dateRange: [],
      selectedMetric: 'register',
      userStats: {
        totalUsers: 0,
        activeUsers: 0,
        newUsers: 0,
        retentionRate: 0
      },
      activeUsers: []
    }
  },
  mounted() {
    this.getStatistics()
    this.initCharts()
  },
  methods: {
    getStatistics() {
      const params = {
        startDate: this.dateRange?.[0],
        endDate: this.dateRange?.[1],
        metric: this.selectedMetric
      }
      getUserStatistics(params).then(response => {
        this.userStats = response.data.stats
        this.activeUsers = response.data.activeUsers
        this.updateCharts(response.data)
      })
    },
    handleDateChange() {
      this.getStatistics()
    },
    handleMetricChange() {
      this.getStatistics()
    },
    initCharts() {
      // 初始化图表
    },
    updateCharts(data) {
      // 更新图表数据
    },
    getMemberLevelColor(level) {
      const colorMap = {
        'VIP': 'warning',
        'PREMIUM': 'danger',
        'NORMAL': 'info'
      }
      return colorMap[level] || 'info'
    },
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleString()
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.card-panel {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
}

.card-panel-icon-wrapper {
  float: left;
  overflow: hidden;
  color: #fff;
  padding: 16px;
  border-radius: 6px;
  margin-right: 16px;
}

.card-panel-icon {
  font-size: 48px;
}

.icon-total-users {
  background: linear-gradient(315deg, #4fc3f7 0%, #2196f3 74%);
}

.icon-active-users {
  background: linear-gradient(315deg, #4caf50 0%, #388e3c 74%);
}

.icon-new-users {
  background: linear-gradient(315deg, #ff9800 0%, #f57c00 74%);
}

.icon-retention {
  background: linear-gradient(315deg, #9c27b0 0%, #7b1fa2 74%);
}

.card-panel-description {
  float: right;
  font-weight: bold;
  margin: 26px 0 26px 0;
}

.card-panel-text {
  line-height: 18px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;
  margin-bottom: 12px;
}

.card-panel-num {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.85);
}
</style>
