package com.example.houduan.service;

import com.example.houduan.common.PageResult;
import com.example.houduan.dto.MovieResponse;
import com.example.houduan.dto.PlayHistoryRequest;
import com.example.houduan.entity.Movie;
import com.example.houduan.entity.UserHistory;
import com.example.houduan.mapper.UserHistoryMapper;
import com.example.houduan.mapper.MovieMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户观看历史服务类
 */
@Service
@RequiredArgsConstructor
public class UserHistoryService {

    private final UserHistoryMapper userHistoryMapper;
    private final MovieMapper movieMapper;

    /**
     * 保存播放历史
     */
    public void savePlayHistory(String userId, PlayHistoryRequest request) {
        // 检查电影是否存在
        Movie movie = movieMapper.findById(request.getMovieId()).orElse(null);
        if (movie == null) {
            throw new RuntimeException("影片不存在");
        }

        // 查找是否已有观看记录
        UserHistory history;
        if (request.getChapterId() != null) {
            history = userHistoryMapper.findByUserIdAndMovieIdAndChapterId(userId, request.getMovieId(), request.getChapterId()).orElse(null);
        } else {
            history = userHistoryMapper.findByUserIdAndMovieIdAndChapterIdIsNull(userId, request.getMovieId()).orElse(null);
        }

        if (history == null) {
            // 创建新的观看记录
            history = new UserHistory();
            history.setUserId(userId);
            history.setMovieId(request.getMovieId());
            history.setChapterId(request.getChapterId());
            history.setProgress(request.getProgress());
            history.setDuration(request.getDuration());
            history.setWatchTime(LocalDateTime.now());
            history.setUpdateTime(LocalDateTime.now());
            userHistoryMapper.save(history);
        } else {
            // 更新现有记录
            history.setProgress(request.getProgress());
            history.setDuration(request.getDuration());
            history.setWatchTime(LocalDateTime.now());
            history.setUpdateTime(LocalDateTime.now());
            userHistoryMapper.save(history);
        }
    }

    /**
     * 获取用户观看历史
     */
    public PageResult<MovieResponse> getUserHistory(String userId, int page, int size) {
        // 分页查询用户观看历史
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<UserHistory> historyResult = userHistoryMapper.findByUserIdOrderByWatchTimeDesc(userId, pageable);

        // 获取电影信息
        List<String> movieIds = historyResult.getContent().stream()
                .map(UserHistory::getMovieId)
                .distinct()
                .collect(Collectors.toList());

        if (movieIds.isEmpty()) {
            return PageResult.empty(page, size);
        }

        List<Movie> movies = movieMapper.findAllById(movieIds);
        List<MovieResponse> movieResponses = movies.stream()
                .map(movie -> {
                    MovieResponse response = new MovieResponse();
                    BeanUtils.copyProperties(movie, response);

                    // 查找对应的观看记录
                    UserHistory userHistory = historyResult.getContent().stream()
                            .filter(h -> h.getMovieId().equals(movie.getId()))
                            .findFirst()
                            .orElse(null);

                    if (userHistory != null) {
                        // 可以添加观看进度等信息到扩展字段
                        // response.setWatchProgress(userHistory.getProgress());
                        // response.setWatchDuration(userHistory.getDuration());
                    }

                    return response;
                })
                .collect(Collectors.toList());

        return PageResult.of(movieResponses, historyResult.getTotalElements(), page, size);
    }

    /**
     * 获取用户观看历史数量
     */
    public long getUserHistoryCount(String userId) {
        return userHistoryMapper.countByUserId(userId);
    }

    /**
     * 清空用户观看历史
     */
    public void clearUserHistory(String userId) {
        userHistoryMapper.deleteByUserId(userId);
    }

    /**
     * 删除特定的观看记录
     */
    public void deleteHistory(String userId, String movieId) {
        userHistoryMapper.deleteByUserIdAndMovieId(userId, movieId);
    }
}
