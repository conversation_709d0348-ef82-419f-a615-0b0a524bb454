package com.example.houduan.mapper;

import com.example.houduan.entity.UserHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 用户观看历史Repository接口
 */
@Repository
public interface UserHistoryMapper extends JpaRepository<UserHistory, String> {

    Optional<UserHistory> findByUserIdAndMovieIdAndChapterId(String userId, String movieId, String chapterId);
    Optional<UserHistory> findByUserIdAndMovieIdAndChapterIdIsNull(String userId, String movieId);
    Page<UserHistory> findByUserIdOrderByWatchTimeDesc(String userId, Pageable pageable);
    long countByUserId(String userId);
    void deleteByUserId(String userId);
    void deleteByUserIdAndMovieId(String userId, String movieId);
}
