<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/category_focused_background" />
            <corners android:radius="8dp" />
            <stroke android:width="2dp" android:color="@color/focus_border_color" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/category_normal_background" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>
