package com.example.myapplicationtv.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplicationtv.ContentItem
import com.example.myapplicationtv.repository.UserRepository
import com.example.myapplicationtv.utils.UserManager
import com.example.myapplicationtv.model.ContentType
import kotlinx.coroutines.launch

/**
 * 用户中心ViewModel
 */
class UserCenterViewModel : ViewModel() {
    
    private val userRepository = UserRepository()
    
    // 收藏列表
    private val _favorites = MutableLiveData<List<ContentItem>>()
    val favorites: LiveData<List<ContentItem>> = _favorites
    
    // 历史记录
    private val _history = MutableLiveData<List<ContentItem>>()
    val history: LiveData<List<ContentItem>> = _history
    
    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    // 错误信息
    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    
    /**
     * 加载用户数据
     */
    fun loadUserData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = ""
                
                // 并行加载收藏和历史记录
                loadFavorites()
                loadHistory()
                
            } catch (e: Exception) {
                _error.value = "加载用户数据失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 加载收藏列表
     */
    private suspend fun loadFavorites() {
        userRepository.getFavorites(size = 50).fold(
            onSuccess = { pageResult ->
                // 这里需要根据收藏的内容类型和ID获取具体的内容信息
                // 为了简化，这里创建模拟数据
                val favoriteItems = pageResult.list.map { favorite ->
                    ContentItem(
                        id = favorite.contentId,
                        title = "收藏内容 ${favorite.contentId}",
                        description = "收藏的${favorite.contentType}内容",
                        type = when (favorite.contentType) {
                            "MOVIE" -> ContentType.MOVIE
                            "APP" -> ContentType.APP
                            "GAME" -> ContentType.GAME
                            "PRODUCT" -> ContentType.PRODUCT
                            else -> ContentType.MOVIE
                        }
                    )
                }
                _favorites.value = favoriteItems
            },
            onFailure = { e ->
                _error.value = "加载收藏列表失败: ${e.message}"
            }
        )
    }
    
    /**
     * 加载历史记录
     */
    private suspend fun loadHistory() {
        userRepository.getHistory(size = 50).fold(
            onSuccess = { pageResult ->
                // 这里需要根据历史记录的内容类型和ID获取具体的内容信息
                // 为了简化，这里创建模拟数据
                val historyItems = pageResult.list.map { history ->
                    ContentItem(
                        id = history.contentId,
                        title = "历史内容 ${history.contentId}",
                        description = "观看的${history.contentType}内容",
                        type = when (history.contentType) {
                            "MOVIE" -> ContentType.MOVIE
                            "APP" -> ContentType.APP
                            "GAME" -> ContentType.GAME
                            "PRODUCT" -> ContentType.PRODUCT
                            else -> ContentType.MOVIE
                        }
                    )
                }
                _history.value = historyItems
            },
            onFailure = { e ->
                _error.value = "加载历史记录失败: ${e.message}"
            }
        )
    }
    
    /**
     * 添加收藏
     */
    fun addFavorite(contentId: String, contentType: String) {
        viewModelScope.launch {
            try {
                userRepository.addFavorite(contentId, contentType).fold(
                    onSuccess = {
                        // 重新加载收藏列表
                        loadFavorites()
                    },
                    onFailure = { e ->
                        _error.value = "添加收藏失败: ${e.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "添加收藏失败: ${e.message}"
            }
        }
    }
    
    /**
     * 取消收藏
     */
    fun removeFavorite(favoriteId: String) {
        viewModelScope.launch {
            try {
                userRepository.removeFavorite(favoriteId).fold(
                    onSuccess = {
                        // 重新加载收藏列表
                        loadFavorites()
                    },
                    onFailure = { e ->
                        _error.value = "取消收藏失败: ${e.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "取消收藏失败: ${e.message}"
            }
        }
    }
    
    /**
     * 添加历史记录
     */
    fun addHistory(contentId: String, contentType: String, watchTime: Int? = null) {
        viewModelScope.launch {
            try {
                userRepository.addHistory(contentId, contentType, watchTime).fold(
                    onSuccess = {
                        // 重新加载历史记录
                        loadHistory()
                    },
                    onFailure = { e ->
                        _error.value = "添加历史记录失败: ${e.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "添加历史记录失败: ${e.message}"
            }
        }
    }
    
    /**
     * 退出登录
     */
    fun logout() {
        UserManager.logout()
    }
    
    /**
     * 刷新数据
     */
    fun refresh() {
        loadUserData()
    }
}
