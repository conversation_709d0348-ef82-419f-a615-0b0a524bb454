{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-hi/values-hi.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,411,512,625,731,5311", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "198,301,406,507,620,726,853,5407"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,509,631,743,843,937,1069,1201,1313,1441,1594,1738,1882,1989,2080,2211,2301,2406,2511,2616,2715,2840,2949,3073,3197,3299,3406,3536,3657,3783,3905,3992,4075,4171,4306,4460", "endColumns": "106,100,98,96,121,111,99,93,131,131,111,127,152,143,143,106,90,130,89,104,104,104,98,124,108,123,123,101,106,129,120,125,121,86,82,95,134,153,97", "endOffsets": "207,308,407,504,626,738,838,932,1064,1196,1308,1436,1589,1733,1877,1984,2075,2206,2296,2401,2506,2611,2710,2835,2944,3068,3192,3294,3401,3531,3652,3778,3900,3987,4070,4166,4301,4455,4553"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "858,965,1066,1165,1262,1384,1496,1596,1690,1822,1954,2066,2194,2347,2491,2635,2742,2833,2964,3054,3159,3264,3369,3468,3593,3702,3826,3950,4052,4159,4289,4410,4536,4658,4745,4828,4924,5059,5213", "endColumns": "106,100,98,96,121,111,99,93,131,131,111,127,152,143,143,106,90,130,89,104,104,104,98,124,108,123,123,101,106,129,120,125,121,86,82,95,134,153,97", "endOffsets": "960,1061,1160,1257,1379,1491,1591,1685,1817,1949,2061,2189,2342,2486,2630,2737,2828,2959,3049,3154,3259,3364,3463,3588,3697,3821,3945,4047,4154,4284,4405,4531,4653,4740,4823,4919,5054,5208,5306"}}]}]}