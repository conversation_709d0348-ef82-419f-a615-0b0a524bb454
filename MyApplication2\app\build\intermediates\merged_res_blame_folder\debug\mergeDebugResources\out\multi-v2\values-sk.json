{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-29:/values-sk/values-sk.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,404,502,612,720,5292", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "196,298,399,497,607,715,837,5388"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,509,627,740,837,930,1057,1183,1292,1417,1578,1711,1843,1948,2044,2174,2266,2372,2474,2587,2687,2804,2924,3046,3167,3281,3407,3517,3635,3741,3855,3942,4026,4142,4285,4460", "endColumns": "106,100,99,95,117,112,96,92,126,125,108,124,160,132,131,104,95,129,91,105,101,112,99,116,119,121,120,113,125,109,117,105,113,86,83,115,142,174,94", "endOffsets": "207,308,408,504,622,735,832,925,1052,1178,1287,1412,1573,1706,1838,1943,2039,2169,2261,2367,2469,2582,2682,2799,2919,3041,3162,3276,3402,3512,3630,3736,3850,3937,4021,4137,4280,4455,4550"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,949,1050,1150,1246,1364,1477,1574,1667,1794,1920,2029,2154,2315,2448,2580,2685,2781,2911,3003,3109,3211,3324,3424,3541,3661,3783,3904,4018,4144,4254,4372,4478,4592,4679,4763,4879,5022,5197", "endColumns": "106,100,99,95,117,112,96,92,126,125,108,124,160,132,131,104,95,129,91,105,101,112,99,116,119,121,120,113,125,109,117,105,113,86,83,115,142,174,94", "endOffsets": "944,1045,1145,1241,1359,1472,1569,1662,1789,1915,2024,2149,2310,2443,2575,2680,2776,2906,2998,3104,3206,3319,3419,3536,3656,3778,3899,4013,4139,4249,4367,4473,4587,4674,4758,4874,5017,5192,5287"}}]}]}