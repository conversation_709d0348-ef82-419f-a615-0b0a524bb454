package com.example.myapplicationtv

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.Observer
import com.example.myapplicationtv.base.BaseTVActivity
import com.example.myapplicationtv.viewmodel.LoginViewModel

/**
 * 登录Activity
 */
class LoginActivity : BaseTVActivity() {

    private lateinit var viewModel: LoginViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login)
        
        initViewModel()
        
        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .replace(R.id.login_fragment, LoginFragment())
                .commitNow()
        }
    }

    private fun initViewModel() {
        viewModel = ViewModelProvider(this)[LoginViewModel::class.java]
        
        // 观察登录状态
        viewModel.loginResult.observe(this, Observer { result ->
            result.fold(
                onSuccess = { user ->
                    Toast.makeText(this, "登录成功，欢迎 ${user.name}", Toast.LENGTH_SHORT).show()
                    
                    // 登录成功，返回主页面
                    val intent = Intent(this, MainActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
                    startActivity(intent)
                    finish()
                },
                onFailure = { error ->
                    Toast.makeText(this, "登录失败: ${error.message}", Toast.LENGTH_LONG).show()
                    Log.e(TAG, "Login failed", error)
                }
            )
        })
        
        // 观察加载状态
        viewModel.isLoading.observe(this, Observer { isLoading ->
            Log.d(TAG, "Loading: $isLoading")
        })
    }

    companion object {
        private const val TAG = "LoginActivity"
    }
}
