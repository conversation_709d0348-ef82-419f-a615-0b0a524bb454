<template>
  <div class="movie-edit">
    <div class="page-header">
      <h1>{{ isEdit ? '编辑影视' : '添加影视' }}</h1>
      <el-button @click="$router.back()">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
    </div>
    
    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        size="large"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="影视名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入影视名称" />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="原名" prop="originalTitle">
              <el-input v-model="form.originalTitle" placeholder="请输入原名" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择类型">
                <el-option label="电影" value="电影" />
                <el-option label="电视剧" value="电视剧" />
                <el-option label="综艺" value="综艺" />
                <el-option label="动漫" value="动漫" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="分类" prop="category">
              <el-input v-model="form.category" placeholder="请输入分类" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="地区" prop="area">
              <el-input v-model="form.area" placeholder="请输入地区" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="语言" prop="language">
              <el-input v-model="form.language" placeholder="请输入语言" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="年份" prop="year">
              <el-input-number 
                v-model="form.year" 
                :min="1900" 
                :max="2030" 
                placeholder="请输入年份"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="时长(分钟)" prop="duration">
              <el-input-number 
                v-model="form.duration" 
                :min="1" 
                placeholder="请输入时长"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="导演" prop="director">
              <el-input v-model="form.director" placeholder="请输入导演" />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="主演" prop="actors">
              <el-input v-model="form.actors" placeholder="请输入主演" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="封面图片" prop="cover">
          <el-input v-model="form.cover" placeholder="请输入封面图片URL" />
        </el-form-item>
        
        <el-form-item label="海报图片" prop="poster">
          <el-input v-model="form.poster" placeholder="请输入海报图片URL" />
        </el-form-item>
        
        <el-form-item label="播放地址" prop="playUrl">
          <el-input v-model="form.playUrl" placeholder="请输入播放地址" />
        </el-form-item>
        
        <el-form-item label="预告片地址" prop="trailerUrl">
          <el-input v-model="form.trailerUrl" placeholder="请输入预告片地址" />
        </el-form-item>
        
        <el-form-item label="简介" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="4"
            placeholder="请输入影视简介"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否推荐">
              <el-switch v-model="form.isRecommended" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="是否热门">
              <el-switch v-model="form.isHot" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="是否最新">
              <el-switch v-model="form.isNew" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio :value="1">上架</el-radio>
            <el-radio :value="0">下架</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { movieAPI } from '@/api'

const route = useRoute()
const router = useRouter()

const formRef = ref()
const loading = ref(false)

const isEdit = computed(() => !!route.params.id)

const form = reactive({
  title: '',
  originalTitle: '',
  type: '',
  category: '',
  area: '',
  language: '',
  year: new Date().getFullYear(),
  duration: null,
  director: '',
  actors: '',
  cover: '',
  poster: '',
  playUrl: '',
  trailerUrl: '',
  description: '',
  isRecommended: 0,
  isHot: 0,
  isNew: 0,
  status: 1
})

const rules = {
  title: [
    { required: true, message: '请输入影视名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  playUrl: [
    { required: true, message: '请输入播放地址', trigger: 'blur' }
  ]
}

// 获取影视详情
const getMovieDetail = async (id) => {
  try {
    const response = await movieAPI.getById(id)
    Object.assign(form, response.data)
  } catch (error) {
    console.error('获取影视详情失败:', error)
    ElMessage.error('获取影视详情失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (isEdit.value) {
      // 更新
      await movieAPI.update(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      // 创建
      await movieAPI.create(form)
      ElMessage.success('创建成功')
    }
    
    router.push('/movies/list')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

onMounted(() => {
  if (isEdit.value) {
    getMovieDetail(route.params.id)
  }
})
</script>

<style scoped>
.movie-edit {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}
</style>
