package com.example.houduan.mapper;

import com.example.houduan.entity.App;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 应用数据访问层
 */
@Repository
public interface AppMapper extends JpaRepository<App, String> {

    /**
     * 根据分类查询应用列表
     */
    Page<App> findByCategoryAndStatus(String category, Integer status, Pageable pageable);

    /**
     * 根据状态查询应用列表
     */
    Page<App> findByStatus(Integer status, Pageable pageable);

    /**
     * 根据名称模糊查询应用
     */
    @Query("SELECT a FROM App a WHERE a.name LIKE %:keyword% AND a.status = :status")
    Page<App> findByNameContainingAndStatus(@Param("keyword") String keyword, @Param("status") Integer status, Pageable pageable);

    /**
     * 查询推荐应用
     */
    List<App> findByIsRecommendedAndStatusOrderByCreateTimeDesc(Integer isRecommended, Integer status);

    /**
     * 根据分类查询应用数量
     */
    long countByCategoryAndStatus(String category, Integer status);

    /**
     * 查询热门应用(按下载量排序)
     */
    @Query("SELECT a FROM App a WHERE a.status = :status ORDER BY a.downloadCount DESC")
    List<App> findHotApps(@Param("status") Integer status, Pageable pageable);

    /**
     * 查询最新应用
     */
    List<App> findByStatusOrderByCreateTimeDesc(Integer status, Pageable pageable);

    /**
     * 根据评分查询应用
     */
    @Query("SELECT a FROM App a WHERE a.rating >= :minRating AND a.status = :status ORDER BY a.rating DESC")
    List<App> findByRatingGreaterThanEqualAndStatus(@Param("minRating") Double minRating, @Param("status") Integer status, Pageable pageable);
}
