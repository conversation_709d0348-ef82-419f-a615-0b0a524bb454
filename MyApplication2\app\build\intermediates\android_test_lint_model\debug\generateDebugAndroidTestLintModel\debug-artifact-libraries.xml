<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.leanback:leanback:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\89d10e7acdc9e8617fe8b024a2336641\transformed\leanback-1.0.0\jars\classes.jar"
      resolved="androidx.leanback:leanback:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\89d10e7acdc9e8617fe8b024a2336641\transformed\leanback-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.11.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\59e3767b96f4ee90f4b57a0c56b92a96\transformed\glide-4.11.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.11.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\59e3767b96f4ee90f4b57a0c56b92a96\transformed\glide-4.11.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.6.2@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\ccfd53c838c812e96e4910096138f89c\transformed\fragment-1.6.2\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.6.2"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\ccfd53c838c812e96e4910096138f89c\transformed\fragment-1.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.6.2@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\374ceb5124eacb51f9cd2e0f0855c4f3\transformed\fragment-ktx-1.6.2\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.6.2"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\374ceb5124eacb51f9cd2e0f0855c4f3\transformed\fragment-ktx-1.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.7.0\85334205d65cca70ed0109c3acbd29e22a2d9cb1\lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"
      provided="true"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\e986c459db27042bd4bd99648aeb71d5\transformed\lifecycle-livedata-core-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\e986c459db27042bd4bd99648aeb71d5\transformed\lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\6d91530bd68c1a5268acfee80590353b\transformed\lifecycle-livedata-core-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\6d91530bd68c1a5268acfee80590353b\transformed\lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\22d0274585fcaae534e1fc3e6eb76e6e\transformed\vectordrawable-animated-1.0.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\22d0274585fcaae534e1fc3e6eb76e6e\transformed\vectordrawable-animated-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\ab3683f7a4d9a57eaa986edb4e5262fb\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\ab3683f7a4d9a57eaa986edb4e5262fb\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\e4d5bf82e4d5c2bf484b0d2d23ce3117\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\e4d5bf82e4d5c2bf484b0d2d23ce3117\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\8c1f2d282f20138de146e7f45bde41c9\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\8c1f2d282f20138de146e7f45bde41c9\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\0b77184f006223771d62c67bbd676e73\transformed\lifecycle-livedata-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.7.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\0b77184f006223771d62c67bbd676e73\transformed\lifecycle-livedata-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.7.2@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\22ec3492cd38aff0fdd3abd4ae94272b\transformed\activity-ktx-1.7.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.7.2"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\22ec3492cd38aff0fdd3abd4ae94272b\transformed\activity-ktx-1.7.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.7.2@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\9717948c970c19acce1692941c22c532\transformed\activity-1.7.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.7.2"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\9717948c970c19acce1692941c22c532\transformed\activity-1.7.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\09b2070107f58b91baaaa03824de0d80\transformed\lifecycle-viewmodel-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\09b2070107f58b91baaaa03824de0d80\transformed\lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\2cd992ff3743b623f80bb69d849f50ca\transformed\lifecycle-livedata-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-ktx:2.7.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\2cd992ff3743b623f80bb69d849f50ca\transformed\lifecycle-livedata-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\e2630f862baa837dd8db5f1a6cd2b6cf\transformed\lifecycle-viewmodel-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\e2630f862baa837dd8db5f1a6cd2b6cf\transformed\lifecycle-viewmodel-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\44b9bab188f5f5366f3115537e09a453\transformed\lifecycle-runtime-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\44b9bab188f5f5366f3115537e09a453\transformed\lifecycle-runtime-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.3.2@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\cffe277412d6e71dab1701f200c8c21e\transformed\recyclerview-1.3.2\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.3.2"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\cffe277412d6e71dab1701f200c8c21e\transformed\recyclerview-1.3.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\d1cb9debee0ec7aff0e9116da1dc9922\transformed\media-1.0.0\jars\classes.jar"
      resolved="androidx.media:media:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\d1cb9debee0ec7aff0e9116da1dc9922\transformed\media-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\2bd59f59ae2972a6ae2824d06af616c8\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\2bd59f59ae2972a6ae2824d06af616c8\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\cec8346c4e2ab21f142e352fe0bfc4ee\transformed\coordinatorlayout-1.0.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\cec8346c4e2ab21f142e352fe0bfc4ee\transformed\coordinatorlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\5c68e73db408f53b49ab0ec6aa072138\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\5c68e73db408f53b49ab0ec6aa072138\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\3b3a598714bb2410226fd2e2c54e7e86\transformed\slidingpanelayout-1.0.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\3b3a598714bb2410226fd2e2c54e7e86\transformed\slidingpanelayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\8b16bf72240f7d4350db4f5688c2644d\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\8b16bf72240f7d4350db4f5688c2644d\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\e8d642ec3f0deb889900fe6cc9fe5cef\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\e8d642ec3f0deb889900fe6cc9fe5cef\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\d3acc5e891e2e7a0ee325b25454f074d\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\d3acc5e891e2e7a0ee325b25454f074d\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\3f0c6eafd88bfcc275cea1cacdb0338e\transformed\vectordrawable-1.0.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\3f0c6eafd88bfcc275cea1cacdb0338e\transformed\vectordrawable-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.10.1@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\jars\classes.jar"
      resolved="androidx.core:core:1.10.1"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\9262bdae479e1b658eb70c7bbd10b157\transformed\lifecycle-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\9262bdae479e1b658eb70c7bbd10b157\transformed\lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\e71662f1e872c884e7f2d1e2bf0f11d8\transformed\lifecycle-viewmodel-savedstate-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\e71662f1e872c884e7f2d1e2bf0f11d8\transformed\lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.10.1@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\6758fc4f4664cc3e72a8df28e7d3f5da\transformed\core-ktx-1.10.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.10.1"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\6758fc4f4664cc3e72a8df28e7d3f5da\transformed\core-ktx-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.9.0\fc93484fc67ab52b1e0ccbdaa3922d8a6678e097\converter-gson-2.9.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.9.0"
      provided="true"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.9.0@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.9.0\d8fdfbd5da952141a665a403348b74538efc05ff\retrofit-2.9.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.9.0"
      provided="true"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.11.0@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.11.0\87fa769912b1f738f3c2dd87e3bca4d1d7f0e666\logging-interceptor-4.11.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.11.0"
      provided="true"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.11.0@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.11.0\436932d695b2c43f2c86b8111c596179cd133d56\okhttp-4.11.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.11.0"
      provided="true"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"
      provided="true"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"
      provided="true"/>
  <library
      name="com.squareup.okio:okio-jvm:3.2.0@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.2.0\332d1c5dc82b0241cb1d35bb0901d28470cc89ca\okio-jvm-3.2.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.2.0"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.20\73576ddf378c5b4f1f6b449fe6b119b8183fc078\kotlin-stdlib-jdk8-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20"
      provided="true"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"
      provided="true"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\b9b3bed66d3a00e5a31ccb8c12557bab\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\b9b3bed66d3a00e5a31ccb8c12557bab\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\220f8c0f15f6aa3f199c619409bad374\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\220f8c0f15f6aa3f199c619409bad374\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.3.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\03f0f7d3357ba78868c8f6b45210dab1\transformed\annotation-experimental-1.3.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.3.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\03f0f7d3357ba78868c8f6b45210dab1\transformed\annotation-experimental-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.11.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\e9cf136272e11e6df2f8a99b3e3d46e1\transformed\gifdecoder-4.11.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.11.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\e9cf136272e11e6df2f8a99b3e3d46e1\transformed\gifdecoder-4.11.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.exifinterface:exifinterface:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\2820c43de1ddfedc63a9c2819f02c6e3\transformed\exifinterface-1.0.0\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\2820c43de1ddfedc63a9c2819f02c6e3\transformed\exifinterface-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\f527d98c4d43bc99c34c0352c3afa590\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\f527d98c4d43bc99c34c0352c3afa590\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\a0f862da65c20cd37353b7a213469561\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\a0f862da65c20cd37353b7a213469561\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\d37b93b53f0b4b52458233873d9d92c6\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\d37b93b53f0b4b52458233873d9d92c6\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\f995e7e8cc3070312bef138ddf684057\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\f995e7e8cc3070312bef138ddf684057\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"
      provided="true"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"
      provided="true"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\2973b6edbd6255a26873f431c1cacfe3\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\2973b6edbd6255a26873f431c1cacfe3\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\0fceed22a269c56fb956e6a7c7b2505d\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\0fceed22a269c56fb956e6a7c7b2505d\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="D:\Android\GradleRepository\caches\8.12\transforms\1aea720ee1e923fbe1226a3f16df1f37\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      provided="true"
      folder="D:\Android\GradleRepository\caches\8.12\transforms\1aea720ee1e923fbe1226a3f16df1f37\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.6.0@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.6.0\a7257339a052df0f91433cf9651231bbb802b502\annotation-jvm-1.6.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.6.0"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.20\3aa51faf20aae8b31e1a4bc54f8370673d7b7df4\kotlin-stdlib-jdk7-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"
      provided="true"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"
      provided="true"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"
      provided="true"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.11.0@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.11.0\ed93d2e20549ad85f692d964788ec77520d78a8b\disklrucache-4.11.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.11.0"
      provided="true"/>
  <library
      name="com.github.bumptech.glide:annotations:4.11.0@jar"
      jars="D:\Android\GradleRepository\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.11.0\c57bae5a18147f8ae22f4da49baac875c6b6f84f\annotations-4.11.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.11.0"
      provided="true"/>
</libraries>
