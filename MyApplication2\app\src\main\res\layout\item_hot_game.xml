<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/card_background"
    android:focusable="true"
    android:clickable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- 游戏图标 -->
        <ImageView
            android:id="@+id/iv_game_icon"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_games"
            android:layout_marginBottom="8dp" />

        <!-- 游戏名称 -->
        <TextView
            android:id="@+id/tv_game_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="阴阳师"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="4dp" />

        <!-- 评分 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_rating"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="★★★★★"
                android:textColor="@color/rating_color"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
