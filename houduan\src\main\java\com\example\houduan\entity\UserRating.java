package com.example.houduan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户评分实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "user_rating")
public class UserRating {

    @Id
    @Column(length = 32)
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 电影ID
     */
    private String movieId;

    /**
     * 评分(0.0-10.0)
     */
    private BigDecimal rating;

    /**
     * 评论内容
     */
    private String comment;

    /**
     * 评分时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
