#Mon Jul 07 19:39:22 CST 2025
path.4=1/classes.dex
path.3=15/classes.dex
path.2=11/classes.dex
path.1=0/classes.dex
renamed.8=classes9.dex
path.8=9/classes.dex
path.7=7/classes.dex
path.6=5/classes.dex
path.5=3/classes.dex
path.0=classes.dex
base.4=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
base.3=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\15\\classes.dex
base.2=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\11\\classes.dex
base.1=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.0=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
renamed.3=classes4.dex
renamed.2=classes3.dex
renamed.1=classes2.dex
renamed.0=classes.dex
renamed.7=classes8.dex
base.8=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\9\\classes.dex
renamed.6=classes7.dex
base.7=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\7\\classes.dex
renamed.5=classes6.dex
base.6=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\5\\classes.dex
renamed.4=classes5.dex
base.5=E\:\\aikaifa\\MovieTV\\MyApplication2\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\3\\classes.dex
