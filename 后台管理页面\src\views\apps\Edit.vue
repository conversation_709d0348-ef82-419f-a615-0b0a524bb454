<template>
  <div class="app-container">
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="left"
      label-width="100px"
      style="width: 600px; margin-left:50px;"
    >
      <el-form-item label="应用名称" prop="name">
        <el-input v-model="temp.name" placeholder="请输入应用名称" />
      </el-form-item>
      <el-form-item label="应用图标" prop="icon">
        <el-input v-model="temp.icon" placeholder="请输入图标URL" />
        <div style="margin-top: 10px;">
          <el-image
            v-if="temp.icon"
            style="width: 100px; height: 100px"
            :src="temp.icon"
            fit="cover"
          />
        </div>
      </el-form-item>
      <el-form-item label="分类" prop="category">
        <el-select v-model="temp.category" placeholder="请选择分类">
          <el-option
            v-for="item in categoryOptions"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="版本" prop="version">
        <el-input v-model="temp.version" placeholder="请输入版本号" />
      </el-form-item>
      <el-form-item label="大小(MB)" prop="size">
        <el-input-number
          v-model="temp.size"
          :min="0"
          :max="10000"
          placeholder="请输入应用大小"
        />
      </el-form-item>
      <el-form-item label="评分" prop="rating">
        <el-rate
          v-model="temp.rating"
          :max="5"
          show-score
          text-color="#ff9900"
        />
      </el-form-item>
      <el-form-item label="下载地址" prop="downloadUrl">
        <el-input v-model="temp.downloadUrl" placeholder="请输入下载地址" />
      </el-form-item>
      <el-form-item label="应用描述" prop="description">
        <el-input
          v-model="temp.description"
          :autosize="{ minRows: 3, maxRows: 6 }"
          type="textarea"
          placeholder="请输入应用描述"
        />
      </el-form-item>
      <el-form-item label="是否推荐">
        <el-switch
          v-model="temp.isRecommended"
          active-color="#13ce66"
          inactive-color="#ff4949"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-radio-group v-model="temp.status">
          <el-radio value="ACTIVE">启用</el-radio>
          <el-radio value="INACTIVE">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div style="margin-left: 50px;">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="confirmData">确认</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()

const temp = reactive({
  id: undefined,
  name: '',
  icon: '',
  category: '',
  version: '',
  size: 0,
  rating: 0,
  downloadUrl: '',
  description: '',
  isRecommended: false,
  status: 'ACTIVE'
})

const categoryOptions = ref([
  { id: 1, name: '社交' },
  { id: 2, name: '娱乐' },
  { id: 3, name: '工具' },
  { id: 4, name: '游戏' }
])

const rules = {
  name: [{ required: true, message: '应用名称是必填项', trigger: 'blur' }],
  icon: [{ required: true, message: '应用图标是必填项', trigger: 'blur' }],
  category: [{ required: true, message: '分类是必填项', trigger: 'change' }],
  version: [{ required: true, message: '版本是必填项', trigger: 'blur' }],
  downloadUrl: [{ required: true, message: '下载地址是必填项', trigger: 'blur' }],
  description: [{ required: true, message: '应用描述是必填项', trigger: 'blur' }]
}

onMounted(() => {
  if (route.params && route.params.id) {
    temp.id = route.params.id
    // 模拟获取应用数据
    temp.name = '示例应用'
    temp.version = '1.0.0'
    temp.category = '工具'
  }
})
const confirmData = () => {
  if (temp.id !== undefined) {
    ElMessage.success('更新成功')
    router.push('/apps/list')
  } else {
    ElMessage.success('创建成功')
    router.push('/apps/list')
  }
}

const cancel = () => {
  router.push('/apps/list')
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
