{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-ca/values-ca.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,499,605,710,5274", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "196,298,397,494,600,705,831,5370"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,508,633,747,846,938,1067,1192,1302,1428,1593,1720,1843,1947,2046,2182,2277,2383,2495,2605,2700,2812,2932,3062,3188,3294,3407,3528,3654,3770,3891,3978,4061,4162,4298,4452", "endColumns": "106,100,97,96,124,113,98,91,128,124,109,125,164,126,122,103,98,135,94,105,111,109,94,111,119,129,125,105,112,120,125,115,120,86,82,100,135,153,90", "endOffsets": "207,308,406,503,628,742,841,933,1062,1187,1297,1423,1588,1715,1838,1942,2041,2177,2272,2378,2490,2600,2695,2807,2927,3057,3183,3289,3402,3523,3649,3765,3886,3973,4056,4157,4293,4447,4538"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,943,1044,1142,1239,1364,1478,1577,1669,1798,1923,2033,2159,2324,2451,2574,2678,2777,2913,3008,3114,3226,3336,3431,3543,3663,3793,3919,4025,4138,4259,4385,4501,4622,4709,4792,4893,5029,5183", "endColumns": "106,100,97,96,124,113,98,91,128,124,109,125,164,126,122,103,98,135,94,105,111,109,94,111,119,129,125,105,112,120,125,115,120,86,82,100,135,153,90", "endOffsets": "938,1039,1137,1234,1359,1473,1572,1664,1793,1918,2028,2154,2319,2446,2569,2673,2772,2908,3003,3109,3221,3331,3426,3538,3658,3788,3914,4020,4133,4254,4380,4496,4617,4704,4787,4888,5024,5178,5269"}}]}]}