package com.example.myapplicationtv.adapter

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.LayerDrawable
import androidx.core.content.ContextCompat
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.R
import com.example.myapplicationtv.model.AppCategory

/**
 * 应用分类适配器
 */
class AppCategoryAdapter(
    private val categories: List<AppCategory>,
    private val onItemClick: (AppCategory) -> Unit
) : RecyclerView.Adapter<AppCategoryAdapter.CategoryViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_app_category, parent, false)
        return CategoryViewHolder(view)
    }

    override fun onBindViewHolder(holder: CategoryViewHolder, position: Int) {
        val category = categories[position]
        holder.bind(category)
        
        holder.itemView.setOnClickListener {
            onItemClick(category)
        }

        // 添加焦点变化动画和背景更新
        holder.itemView.setOnFocusChangeListener { view, hasFocus ->
            val scaleX = if (hasFocus) 1.1f else 1.0f
            val scaleY = if (hasFocus) 1.1f else 1.0f

            view.animate()
                .scaleX(scaleX)
                .scaleY(scaleY)
                .setDuration(200)
                .start()

            // 更新背景以显示焦点效果
            holder.updateBackground(category, hasFocus)
        }
    }

    override fun getItemCount(): Int = categories.size

    class CategoryViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivIcon: ImageView = itemView.findViewById(R.id.iv_category_icon)
        private val tvTitle: TextView = itemView.findViewById(R.id.tv_category_title)

        fun bind(category: AppCategory) {
            ivIcon.setImageResource(category.iconRes)
            tvTitle.text = category.title

            // 初始设置背景
            updateBackground(category, false)
        }

        fun updateBackground(category: AppCategory, hasFocus: Boolean) {
            try {
                val color = Color.parseColor(category.backgroundColor)

                if (hasFocus) {
                    // 焦点状态：白色边框 + 彩色背景
                    val layerDrawable = LayerDrawable(arrayOf(
                        // 外层白色边框
                        GradientDrawable().apply {
                            shape = GradientDrawable.RECTANGLE
                            cornerRadius = 48f
                            setColor(Color.WHITE)
                        },
                        // 内层彩色背景
                        GradientDrawable().apply {
                            shape = GradientDrawable.RECTANGLE
                            cornerRadius = 36f
                            setColor(color)
                        }
                    ))
                    layerDrawable.setLayerInset(1, 12, 12, 12, 12)
                    itemView.background = layerDrawable
                } else {
                    // 普通状态：只有彩色背景
                    val drawable = GradientDrawable()
                    drawable.shape = GradientDrawable.RECTANGLE
                    drawable.cornerRadius = 36f
                    drawable.setColor(color)
                    itemView.background = drawable
                }
            } catch (e: Exception) {
                // 如果颜色解析失败，使用默认背景
                itemView.background = ContextCompat.getDrawable(itemView.context, R.drawable.app_category_background)
            }
        }
    }
}
