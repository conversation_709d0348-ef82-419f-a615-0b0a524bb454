package com.example.myapplicationtv.adapter

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.R
import com.example.myapplicationtv.model.AppCategory

/**
 * 应用分类适配器
 */
class AppCategoryAdapter(
    private val categories: List<AppCategory>,
    private val onItemClick: (AppCategory) -> Unit
) : RecyclerView.Adapter<AppCategoryAdapter.CategoryViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_app_category, parent, false)
        return CategoryViewHolder(view)
    }

    override fun onBindViewHolder(holder: CategoryViewHolder, position: Int) {
        val category = categories[position]
        holder.bind(category)
        
        holder.itemView.setOnClickListener {
            onItemClick(category)
        }
    }

    override fun getItemCount(): Int = categories.size

    class CategoryViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivIcon: ImageView = itemView.findViewById(R.id.iv_category_icon)
        private val tvTitle: TextView = itemView.findViewById(R.id.tv_category_title)

        fun bind(category: AppCategory) {
            ivIcon.setImageResource(category.iconRes)
            tvTitle.text = category.title
            
            // 设置背景颜色
            try {
                val color = Color.parseColor(category.backgroundColor)
                val drawable = GradientDrawable()
                drawable.shape = GradientDrawable.RECTANGLE
                drawable.cornerRadius = 24f
                drawable.setColor(color)
                itemView.background = drawable
            } catch (e: Exception) {
                // 如果颜色解析失败，使用默认背景
            }
        }
    }
}
