<lint-module
    format="1"
    dir="E:\aikaifa\MovieTV\MyApplication2\app"
    name=":app"
    type="APP"
    maven="My Application TV:app:unspecified"
    agpVersion="8.10.1"
    buildFolder="build"
    bootClassPath="D:\Android\sdk\platforms\android-35\android.jar;D:\Android\sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
