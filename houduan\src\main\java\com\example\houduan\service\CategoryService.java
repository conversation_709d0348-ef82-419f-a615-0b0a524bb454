package com.example.houduan.service;

import com.example.houduan.entity.Category;
import com.example.houduan.mapper.CategoryMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分类服务类
 */
@Service
@RequiredArgsConstructor
public class CategoryService {

    private final CategoryMapper categoryMapper;

    /**
     * 根据类型获取分类列表
     */
    public List<Category> getCategoriesByType(String type) {
        return categoryMapper.findByTypeAndStatusOrderBySortOrder(type, 1);
    }

    /**
     * 获取所有启用的分类
     */
    public List<Category> getAllCategories() {
        return categoryMapper.findByStatusOrderByTypeAscSortOrderAsc(1);
    }
}
