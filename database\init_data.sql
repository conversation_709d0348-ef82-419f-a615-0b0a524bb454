-- TV端影视应用示例数据初始化脚本

USE movietv;

-- 清空现有数据
DELETE FROM user_favorite;
DELETE FROM user_history;
DELETE FROM user_rating;
DELETE FROM product;
DELETE FROM game;
DELETE FROM app;
DELETE FROM movie;
DELETE FROM category;

-- 插入分类数据
INSERT INTO category (id, name, type, icon, sort_order, status, create_time, update_time) VALUES
('CAT001', '动作片', 'MOVIE', 'action', 1, 1, NOW(), NOW()),
('CAT002', '科幻片', 'MOVIE', 'sci-fi', 2, 1, NOW(), NOW()),
('CAT003', '爱情片', 'MOVIE', 'romance', 3, 1, NOW(), NOW()),
('CAT004', '喜剧片', 'MOVIE', 'comedy', 4, 1, NOW(), NOW()),
('CAT005', '恐怖片', 'MOVIE', 'horror', 5, 1, NOW(), NOW()),
('CAT006', '工具应用', 'APP', 'tools', 6, 1, NOW(), NOW()),
('CAT007', '娱乐应用', 'APP', 'entertainment', 7, 1, NOW(), NOW()),
('CAT008', '教育应用', 'APP', 'education', 8, 1, NOW(), NOW()),
('CAT009', '动作游戏', 'GAME', 'action-game', 9, 1, NOW(), NOW()),
('CAT010', '益智游戏', 'GAME', 'puzzle', 10, 1, NOW(), NOW()),
('CAT011', '电子产品', 'PRODUCT', 'electronics', 11, 1, NOW(), NOW()),
('CAT012', '家居用品', 'PRODUCT', 'home', 12, 1, NOW(), NOW());

-- 插入电影数据
INSERT INTO movie (id, title, original_title, type, category, area, language, year, duration, director, actors, cover, poster, play_url, trailer_url, description, rating, rating_count, view_count, is_recommended, is_hot, is_new, status, create_time, update_time) VALUES
('MOV001', '复仇者联盟：终局之战', 'Avengers: Endgame', '电影', '动作片', '美国', '英语', 2019, 181, '安东尼·罗素,乔·罗素', '小罗伯特·唐尼,克里斯·埃文斯,马克·鲁法洛', 'https://example.com/covers/avengers-endgame.jpg', 'https://example.com/posters/avengers-endgame.jpg', 'https://example.com/play/avengers-endgame.mp4', 'https://example.com/trailers/avengers-endgame.mp4', '超级英雄们为了拯救宇宙，与灭霸展开最终决战。', 9.2, 15000, 2500000, 1, 1, 0, 1, NOW(), NOW()),
('MOV002', '星际穿越', 'Interstellar', '电影', '科幻片', '美国', '英语', 2014, 169, '克里斯托弗·诺兰', '马修·麦康纳,安妮·海瑟薇,杰西卡·查斯坦', 'https://example.com/covers/interstellar.jpg', 'https://example.com/posters/interstellar.jpg', 'https://example.com/play/interstellar.mp4', 'https://example.com/trailers/interstellar.mp4', '在地球面临毁灭的未来，一群探险家穿越虫洞寻找人类新家园。', 9.3, 12000, 1800000, 1, 1, 0, 1, NOW(), NOW()),
('MOV003', '泰坦尼克号', 'Titanic', '电影', '爱情片', '美国', '英语', 1997, 194, '詹姆斯·卡梅隆', '莱昂纳多·迪卡普里奥,凯特·温斯莱特', 'https://example.com/covers/titanic.jpg', 'https://example.com/posters/titanic.jpg', 'https://example.com/play/titanic.mp4', 'https://example.com/trailers/titanic.mp4', '一段跨越阶级的爱情故事在泰坦尼克号上演。', 9.1, 20000, 3200000, 1, 0, 0, 1, NOW(), NOW()),
('MOV004', '疯狂动物城', 'Zootopia', '电影', '喜剧片', '美国', '英语', 2016, 108, '拜伦·霍华德,里奇·摩尔', '金妮弗·古德温,杰森·贝特曼', 'https://example.com/covers/zootopia.jpg', 'https://example.com/posters/zootopia.jpg', 'https://example.com/play/zootopia.mp4', 'https://example.com/trailers/zootopia.mp4', '在动物乌托邦中，兔子警官朱迪与狐狸尼克合作破案。', 8.8, 8500, 1500000, 1, 0, 1, 1, NOW(), NOW()),
('MOV005', '寂静之地', 'A Quiet Place', '电影', '恐怖片', '美国', '英语', 2018, 90, '约翰·卡拉辛斯基', '艾米莉·布朗特,约翰·卡拉辛斯基', 'https://example.com/covers/quiet-place.jpg', 'https://example.com/posters/quiet-place.jpg', 'https://example.com/play/quiet-place.mp4', 'https://example.com/trailers/quiet-place.mp4', '在被怪物统治的世界里，一家人必须保持绝对安静才能生存。', 8.5, 6800, 980000, 0, 1, 1, 1, NOW(), NOW());

-- 插入应用数据
INSERT INTO application (id, name, icon, category, version, size, rating, rating_count, download_count, description, download_url, screenshots, is_recommended, status, create_time, update_time) VALUES
('APP001', 'Netflix', 'https://example.com/icons/netflix.png', '娱乐应用', '8.45.0', 52428800, 8.9, 2500000, 1000000000, '全球领先的流媒体娱乐服务，提供丰富的电影、电视剧和纪录片内容。', 'https://example.com/downloads/netflix.apk', '["https://example.com/screenshots/netflix1.jpg","https://example.com/screenshots/netflix2.jpg"]', 1, 1, NOW(), NOW()),
('APP002', 'YouTube', 'https://example.com/icons/youtube.png', '娱乐应用', '17.49.37', 134217728, 8.7, 5000000, 5000000000, '观看、分享和发现世界各地的视频内容。', 'https://example.com/downloads/youtube.apk', '["https://example.com/screenshots/youtube1.jpg","https://example.com/screenshots/youtube2.jpg"]', 1, 1, NOW(), NOW()),
('APP003', 'VLC Media Player', 'https://example.com/icons/vlc.png', '工具应用', '3.4.9', 41943040, 9.1, 800000, 500000000, '功能强大的多媒体播放器，支持几乎所有音视频格式。', 'https://example.com/downloads/vlc.apk', '["https://example.com/screenshots/vlc1.jpg","https://example.com/screenshots/vlc2.jpg"]', 1, 1, NOW(), NOW()),
('APP004', 'Khan Academy', 'https://example.com/icons/khan.png', '教育应用', '7.3.2', 67108864, 8.6, 150000, 10000000, '免费的世界级教育平台，提供各种学科的在线课程。', 'https://example.com/downloads/khan.apk', '["https://example.com/screenshots/khan1.jpg","https://example.com/screenshots/khan2.jpg"]', 0, 1, NOW(), NOW()),
('APP005', 'File Manager', 'https://example.com/icons/filemanager.png', '工具应用', '2.8.1', 16777216, 8.4, 300000, 50000000, '简洁高效的文件管理器，支持本地和云端文件管理。', 'https://example.com/downloads/filemanager.apk', '["https://example.com/screenshots/fm1.jpg","https://example.com/screenshots/fm2.jpg"]', 0, 1, NOW(), NOW());

-- 插入游戏数据
INSERT INTO game (id, name, cover, icon, category, version, size, rating, rating_count, play_count, description, play_url, screenshots, is_recommended, is_featured, status, create_time, update_time) VALUES
('GAM001', '愤怒的小鸟', 'https://example.com/covers/angrybirds.jpg', 'https://example.com/icons/angrybirds.png', '益智游戏', '8.0.3', 104857600, 8.8, 1200000, 800000000, '经典的物理益智游戏，用弹弓发射小鸟击败绿猪。', 'https://example.com/games/angrybirds/', '["https://example.com/screenshots/ab1.jpg","https://example.com/screenshots/ab2.jpg"]', 1, 1, 1, NOW(), NOW()),
('GAM002', '地铁跑酷', 'https://example.com/covers/subwaysurfers.jpg', 'https://example.com/icons/subwaysurfers.png', '动作游戏', '3.12.0', 125829120, 8.6, 800000, 2000000000, '无尽跑酷游戏，在地铁轨道上奔跑躲避追捕。', 'https://example.com/games/subwaysurfers/', '["https://example.com/screenshots/ss1.jpg","https://example.com/screenshots/ss2.jpg"]', 1, 1, 1, NOW(), NOW()),
('GAM003', '植物大战僵尸', 'https://example.com/covers/pvz.jpg', 'https://example.com/icons/pvz.png', '益智游戏', '2.9.07', 209715200, 9.0, 600000, 500000000, '经典塔防游戏，种植植物抵御僵尸入侵。', 'https://example.com/games/pvz/', '["https://example.com/screenshots/pvz1.jpg","https://example.com/screenshots/pvz2.jpg"]', 1, 0, 1, NOW(), NOW()),
('GAM004', '糖果传奇', 'https://example.com/covers/candycrush.jpg', 'https://example.com/icons/candycrush.png', '益智游戏', '1.220.2', 83886080, 8.3, 2000000, 1500000000, '三消益智游戏，消除糖果获得高分。', 'https://example.com/games/candycrush/', '["https://example.com/screenshots/cc1.jpg","https://example.com/screenshots/cc2.jpg"]', 0, 1, 1, NOW(), NOW()),
('GAM005', '王者荣耀', 'https://example.com/covers/honor.jpg', 'https://example.com/icons/honor.png', '动作游戏', '********', 1073741824, 8.9, 5000000, 10000000000, '5V5英雄竞技手游，体验团队作战的乐趣。', 'https://example.com/games/honor/', '["https://example.com/screenshots/honor1.jpg","https://example.com/screenshots/honor2.jpg"]', 1, 1, 1, NOW(), NOW());

-- 插入商品数据
INSERT INTO product (id, name, image, images, category, brand, price, original_price, stock, sales, rating, rating_count, description, specifications, is_recommended, is_hot, status, create_time, update_time) VALUES
('PRD001', '小米电视 65英寸 4K', 'https://example.com/products/xiaomi-tv-65.jpg', '["https://example.com/products/xiaomi-tv-65-1.jpg","https://example.com/products/xiaomi-tv-65-2.jpg"]', '电子产品', '小米', 2999.00, 3499.00, 50, 1200, 9.1, 800, '65英寸4K超高清智能电视，支持HDR10+，内置小爱同学。', '{"屏幕尺寸":"65英寸","分辨率":"3840x2160","操作系统":"MIUI TV","接口":"HDMI x3, USB x2"}', 1, 1, 1, NOW(), NOW()),
('PRD002', '华为路由器 AX3000', 'https://example.com/products/huawei-router.jpg', '["https://example.com/products/huawei-router-1.jpg","https://example.com/products/huawei-router-2.jpg"]', '电子产品', '华为', 299.00, 399.00, 100, 800, 8.8, 500, 'Wi-Fi 6路由器，双频并发3000Mbps，支持160MHz频宽。', '{"无线标准":"Wi-Fi 6","传输速率":"3000Mbps","天线":"4根外置天线","接口":"千兆网口 x4"}', 1, 0, 1, NOW(), NOW()),
('PRD003', '飞利浦空气净化器', 'https://example.com/products/philips-purifier.jpg', '["https://example.com/products/philips-purifier-1.jpg","https://example.com/products/philips-purifier-2.jpg"]', '家居用品', '飞利浦', 1299.00, 1599.00, 30, 300, 8.9, 200, '高效除甲醛空气净化器，CADR值400m³/h，适用面积28-48㎡。', '{"CADR值":"400m³/h","适用面积":"28-48㎡","滤网":"复合滤网","噪音":"<32dB"}', 0, 1, 1, NOW(), NOW()),
('PRD004', '戴森吸尘器 V15', 'https://example.com/products/dyson-v15.jpg', '["https://example.com/products/dyson-v15-1.jpg","https://example.com/products/dyson-v15-2.jpg"]', '家居用品', '戴森', 3990.00, 4490.00, 20, 150, 9.3, 100, '无线吸尘器，激光显尘技术，60分钟续航。', '{"类型":"无线吸尘器","续航":"60分钟","吸力":"230AW","重量":"3.05kg"}', 1, 1, 1, NOW(), NOW()),
('PRD005', 'AirPods Pro 2', 'https://example.com/products/airpods-pro2.jpg', '["https://example.com/products/airpods-pro2-1.jpg","https://example.com/products/airpods-pro2-2.jpg"]', '电子产品', '苹果', 1899.00, 1999.00, 80, 600, 9.0, 400, '主动降噪无线耳机，空间音频，最长30小时续航。', '{"类型":"入耳式","降噪":"主动降噪","续航":"30小时","防水":"IPX4"}', 1, 1, 1, NOW(), NOW());

-- 插入用户收藏数据（使用已存在的用户）
INSERT INTO user_favorite (id, user_id, movie_id, create_time) VALUES
('FAV001', '1751509704DD4NxmeTcviglBJWC1apTF', 'MOV001', NOW()),
('FAV002', '1751509704DD4NxmeTcviglBJWC1apTF', 'MOV002', NOW());

-- 插入用户历史记录
INSERT INTO user_history (id, user_id, movie_id, progress, duration, watch_time, update_time) VALUES
('HIS001', '1751509704DD4NxmeTcviglBJWC1apTF', 'MOV001', 7200, 10860, NOW(), NOW()),
('HIS002', '1751509704DD4NxmeTcviglBJWC1apTF', 'MOV003', 5400, 11640, NOW(), NOW()),
('HIS003', '1751509704DD4NxmeTcviglBJWC1apTF', 'MOV004', 6480, 6480, NOW(), NOW());

-- 插入用户评分数据
INSERT INTO user_rating (id, user_id, movie_id, rating, comment, create_time, update_time) VALUES
('RAT001', '1751509704DD4NxmeTcviglBJWC1apTF', 'MOV001', 9.5, '非常精彩的超级英雄电影，特效震撼！', NOW(), NOW()),
('RAT002', '1751509704DD4NxmeTcviglBJWC1apTF', 'MOV002', 9.8, '诺兰的科幻神作，深度和视觉效果都很棒。', NOW(), NOW());

COMMIT;
