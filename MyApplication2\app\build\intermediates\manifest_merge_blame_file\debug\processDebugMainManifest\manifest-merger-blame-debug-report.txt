1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplicationtv"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:5:5-67
11-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:5:22-64
12
13    <uses-feature
13-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:7:5-9:36
14        android:name="android.hardware.touchscreen"
14-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:8:9-52
15        android:required="false" />
15-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:9:9-33
16    <uses-feature
16-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:10:5-12:35
17        android:name="android.software.leanback"
17-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:11:9-49
18        android:required="true" />
18-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:12:9-32
19
20    <permission
20-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
21        android:name="com.example.myapplicationtv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.example.myapplicationtv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
25
26    <application
26-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:14:5-57:19
27        android:name="com.example.myapplicationtv.TVApplication"
27-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:21:9-38
28        android:allowBackup="true"
28-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:15:9-35
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
30        android:debuggable="true"
31        android:extractNativeLibs="true"
32        android:icon="@mipmap/ic_launcher"
32-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:16:9-43
33        android:label="@string/app_name"
33-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:17:9-41
34        android:supportsRtl="true"
34-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:18:9-35
35        android:theme="@style/Theme.MyApplicationTV"
35-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:19:9-53
36        android:usesCleartextTraffic="true" >
36-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:20:9-44
37        <activity
37-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:22:9-35:20
38            android:name="com.example.myapplicationtv.MainActivity"
38-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:23:13-41
39            android:banner="@drawable/app_icon_your_company"
39-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:24:13-61
40            android:exported="true"
40-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:25:13-36
41            android:icon="@drawable/app_icon_your_company"
41-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:26:13-59
42            android:label="@string/app_name"
42-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:27:13-45
43            android:logo="@drawable/app_icon_your_company"
43-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:28:13-59
44            android:screenOrientation="landscape" >
44-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:29:13-50
45            <intent-filter>
45-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:30:13-34:29
46                <action android:name="android.intent.action.MAIN" />
46-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:31:17-69
46-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:31:25-66
47
48                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
48-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:33:17-86
48-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:33:27-83
49            </intent-filter>
50        </activity>
51        <activity
51-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:36:9-38:40
52            android:name="com.example.myapplicationtv.DetailsActivity"
52-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:37:13-44
53            android:exported="false" />
53-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:38:13-37
54        <activity
54-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:39:9-41:40
55            android:name="com.example.myapplicationtv.PlaybackActivity"
55-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:40:13-45
56            android:exported="false" />
56-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:41:13-37
57        <activity
57-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:42:9-44:40
58            android:name="com.example.myapplicationtv.BrowseErrorActivity"
58-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:43:13-48
59            android:exported="false" />
59-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:44:13-37
60        <activity
60-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:45:9-47:40
61            android:name="com.example.myapplicationtv.SearchActivity"
61-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:46:13-43
62            android:exported="false" />
62-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:47:13-37
63        <activity
63-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:48:9-50:40
64            android:name="com.example.myapplicationtv.LoginActivity"
64-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:49:13-42
65            android:exported="false" />
65-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:50:13-37
66        <activity
66-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:51:9-53:40
67            android:name="com.example.myapplicationtv.UserCenterActivity"
67-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:52:13-47
68            android:exported="false" />
68-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:53:13-37
69        <activity
69-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:54:9-56:40
70            android:name="com.example.myapplicationtv.TestActivity"
70-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:55:13-41
71            android:exported="false" />
71-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:56:13-37
72
73        <provider
73-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
74            android:name="androidx.startup.InitializationProvider"
74-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:25:13-67
75            android:authorities="com.example.myapplicationtv.androidx-startup"
75-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:26:13-68
76            android:exported="false" >
76-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:27:13-37
77            <meta-data
77-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
79                android:value="androidx.startup" />
79-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
80        </provider>
81
82        <receiver
82-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
83            android:name="androidx.profileinstaller.ProfileInstallReceiver"
83-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
84            android:directBootAware="false"
84-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
85            android:enabled="true"
85-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
86            android:exported="true"
86-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
87            android:permission="android.permission.DUMP" >
87-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
89                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
89-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
92                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
92-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
95                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
95-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
95-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
98                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
98-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
98-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
99            </intent-filter>
100        </receiver>
101    </application>
102
103</manifest>
