1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplicationtv"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:5:5-67
11-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:5:22-64
12
13    <uses-feature
13-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:7:5-9:36
14        android:name="android.hardware.touchscreen"
14-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:8:9-52
15        android:required="false" />
15-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:9:9-33
16    <uses-feature
16-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:10:5-12:35
17        android:name="android.software.leanback"
17-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:11:9-49
18        android:required="true" />
18-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:12:9-32
19
20    <permission
20-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
21        android:name="com.example.myapplicationtv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.example.myapplicationtv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
25
26    <application
26-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:14:5-57:19
27        android:name="com.example.myapplicationtv.TVApplication"
27-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:21:9-38
28        android:allowBackup="true"
28-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:15:9-35
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
30        android:debuggable="true"
31        android:extractNativeLibs="true"
32        android:icon="@mipmap/ic_launcher"
32-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:16:9-43
33        android:label="@string/app_name"
33-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:17:9-41
34        android:supportsRtl="true"
34-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:18:9-35
35        android:testOnly="true"
36        android:theme="@style/Theme.MyApplicationTV"
36-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:19:9-53
37        android:usesCleartextTraffic="true" >
37-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:20:9-44
38        <activity
38-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:22:9-35:20
39            android:name="com.example.myapplicationtv.MainActivity"
39-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:23:13-41
40            android:banner="@drawable/app_icon_your_company"
40-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:24:13-61
41            android:exported="true"
41-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:25:13-36
42            android:icon="@drawable/app_icon_your_company"
42-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:26:13-59
43            android:label="@string/app_name"
43-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:27:13-45
44            android:logo="@drawable/app_icon_your_company"
44-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:28:13-59
45            android:screenOrientation="landscape" >
45-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:29:13-50
46            <intent-filter>
46-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:30:13-34:29
47                <action android:name="android.intent.action.MAIN" />
47-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:31:17-69
47-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:31:25-66
48
49                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
49-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:33:17-86
49-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:33:27-83
50            </intent-filter>
51        </activity>
52        <activity
52-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:36:9-38:40
53            android:name="com.example.myapplicationtv.DetailsActivity"
53-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:37:13-44
54            android:exported="false" />
54-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:38:13-37
55        <activity
55-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:39:9-41:40
56            android:name="com.example.myapplicationtv.PlaybackActivity"
56-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:40:13-45
57            android:exported="false" />
57-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:41:13-37
58        <activity
58-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:42:9-44:40
59            android:name="com.example.myapplicationtv.BrowseErrorActivity"
59-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:43:13-48
60            android:exported="false" />
60-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:44:13-37
61        <activity
61-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:45:9-47:40
62            android:name="com.example.myapplicationtv.SearchActivity"
62-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:46:13-43
63            android:exported="false" />
63-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:47:13-37
64        <activity
64-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:48:9-50:40
65            android:name="com.example.myapplicationtv.LoginActivity"
65-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:49:13-42
66            android:exported="false" />
66-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:50:13-37
67        <activity
67-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:51:9-53:40
68            android:name="com.example.myapplicationtv.UserCenterActivity"
68-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:52:13-47
69            android:exported="false" />
69-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:53:13-37
70        <activity
70-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:54:9-56:40
71            android:name="com.example.myapplicationtv.TestActivity"
71-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:55:13-41
72            android:exported="false" />
72-->E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:56:13-37
73
74        <provider
74-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
75            android:name="androidx.startup.InitializationProvider"
75-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:25:13-67
76            android:authorities="com.example.myapplicationtv.androidx-startup"
76-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:26:13-68
77            android:exported="false" >
77-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:27:13-37
78            <meta-data
78-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
79                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
79-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
80                android:value="androidx.startup" />
80-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
81        </provider>
82
83        <receiver
83-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
84            android:name="androidx.profileinstaller.ProfileInstallReceiver"
84-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
85            android:directBootAware="false"
85-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
86            android:enabled="true"
86-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
87            android:exported="true"
87-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
88            android:permission="android.permission.DUMP" >
88-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
90                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
90-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
90-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
91            </intent-filter>
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
93                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
93-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
93-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
94            </intent-filter>
95            <intent-filter>
95-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
96                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
96-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
96-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
97            </intent-filter>
98            <intent-filter>
98-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
99                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
99-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
99-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
100            </intent-filter>
101        </receiver>
102    </application>
103
104</manifest>
