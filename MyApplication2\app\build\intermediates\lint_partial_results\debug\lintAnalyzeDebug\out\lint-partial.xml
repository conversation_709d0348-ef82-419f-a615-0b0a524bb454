<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="partial_results">
    <map id="NotificationPermission">
        <location id="class"
            file="$GRADLE_USER_HOME/caches/8.12/transforms/59e3767b96f4ee90f4b57a0c56b92a96/transformed/glide-4.11.0/jars/classes.jar"/>
        <entry
            name="className"
            string="com/bumptech/glide/request/target/NotificationTarget"/>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.example.myapplicationtv.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <entry
            name="model"
            string="color[selected_background(U),background_gradient_end(U),background_gradient_start(U),default_background(U),fastlane_background(U),search_opaque(U)],drawable[app_icon_your_company(U),button_background(U),default_background(U),edit_text_background(U),movie(U),lb_ic_sad_cloud(R)],id[details_fragment(U),login_fragment(U),main_browse_fragment(U),search_fragment(U),user_center_fragment(U),username_edit_text(U),password_edit_text(U),login_button(U),register_button(U),progress_bar(U)],layout[activity_details(U),activity_login(U),activity_main(U),activity_search(U),activity_test(U),activity_user_center(U),fragment_login(U)],mipmap[ic_launcher(U)],string[app_name(U),browse_title(U),related_movies(U),grid_view(U),error_fragment(U),personal_settings(U),watch_trailer_1(U),watch_trailer_2(U),rent_1(U),rent_2(U),buy_1(U),buy_2(U),movie(U),error_fragment_message(U),dismiss_error(U)],style[Theme_MyApplicationTV(U),Theme_Leanback(R)];7^0,8^1^2,9^0,1a^3,1c^3^6^9^7,2d^2e;;;"/>
    </map>

</incidents>
