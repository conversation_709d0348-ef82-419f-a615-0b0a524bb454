{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-29:/values-nb/values-nb.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,497,605,711,5192", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "194,296,393,492,600,706,826,5288"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,506,624,736,833,928,1068,1207,1313,1435,1585,1705,1824,1933,2032,2157,2250,2352,2460,2560,2659,2775,2883,3019,3154,3258,3368,3485,3601,3711,3820,3907,3988,4089,4223,4377", "endColumns": "106,100,97,94,117,111,96,94,139,138,105,121,149,119,118,108,98,124,92,101,107,99,98,115,107,135,134,103,109,116,115,109,108,86,80,100,133,153,88", "endOffsets": "207,308,406,501,619,731,828,923,1063,1202,1308,1430,1580,1700,1819,1928,2027,2152,2245,2347,2455,2555,2654,2770,2878,3014,3149,3253,3363,3480,3596,3706,3815,3902,3983,4084,4218,4372,4461"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,938,1039,1137,1232,1350,1462,1559,1654,1794,1933,2039,2161,2311,2431,2550,2659,2758,2883,2976,3078,3186,3286,3385,3501,3609,3745,3880,3984,4094,4211,4327,4437,4546,4633,4714,4815,4949,5103", "endColumns": "106,100,97,94,117,111,96,94,139,138,105,121,149,119,118,108,98,124,92,101,107,99,98,115,107,135,134,103,109,116,115,109,108,86,80,100,133,153,88", "endOffsets": "933,1034,1132,1227,1345,1457,1554,1649,1789,1928,2034,2156,2306,2426,2545,2654,2753,2878,2971,3073,3181,3281,3380,3496,3604,3740,3875,3979,4089,4206,4322,4432,4541,4628,4709,4810,4944,5098,5187"}}]}]}