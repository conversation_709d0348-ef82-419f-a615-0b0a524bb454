# 🚀 TV端影视应用快速启动指南

## ✅ 当前状态

**好消息！** 后端服务已经成功启动并运行在 `http://localhost:8080/api`

## 📋 下一步操作

### 1. 创建数据库 (必需)

使用MySQL客户端或管理工具执行以下命令：

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS movietv CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE movietv;
```

然后执行数据库初始化脚本：
```bash
# 在MySQL中执行
source E:/aikaifa/MovieTV/database/create_database.sql
```

或者使用MySQL Workbench、phpMyAdmin等工具导入 `database/create_database.sql` 文件。

### 2. 重启后端服务

数据库创建完成后，重启Spring Boot应用：
- 在当前终端按 `Ctrl+C` 停止服务
- 重新运行：`./mvnw.cmd spring-boot:run`

### 3. 测试接口

数据库连接成功后，可以测试以下接口：

#### 健康检查（无需认证）
```
GET http://localhost:8080/api/test/health
```

#### 用户注册
```
POST http://localhost:8080/api/auth/register
Content-Type: application/json

{
    "username": "testuser",
    "password": "123456",
    "phone": "13800138000",
    "email": "<EMAIL>"
}
```

#### 用户登录
```
POST http://localhost:8080/api/auth/login
Content-Type: application/json

{
    "username": "testuser",
    "password": "123456"
}
```

#### 获取电影列表（需要JWT Token）
```
GET http://localhost:8080/api/movies/list?page=1&size=10
Authorization: Bearer <your-jwt-token>
```

## 🎯 项目架构概览

### 后端服务 (Spring Boot 3.5.3)
- **端口**: 8080
- **基础路径**: `/api`
- **认证方式**: JWT Token
- **数据库**: MySQL 8.0
- **ORM**: JPA/Hibernate

### 主要功能模块
1. **用户管理** - 注册、登录、认证
2. **影视内容** - 电影CRUD、分类管理
3. **播放功能** - 收藏、历史、评分
4. **系统管理** - 配置、日志、权限

### API接口分组
- `/api/auth/*` - 认证相关
- `/api/movies/*` - 电影相关
- `/api/categories/*` - 分类相关
- `/api/favorites/*` - 收藏相关
- `/api/history/*` - 历史相关
- `/api/ratings/*` - 评分相关

## 🔧 开发工具推荐

### API测试
- **Postman** - 推荐用于接口测试
- **Apifox** - 国产API工具
- **VS Code REST Client** - 轻量级选择

### 数据库管理
- **MySQL Workbench** - 官方工具
- **phpMyAdmin** - Web界面
- **Navicat** - 商业工具

## 📝 默认数据

数据库初始化后会包含：
- 默认管理员账号：`admin/admin123`
- 基础分类数据（电影、电视剧、综艺等）
- 系统配置数据

## 🚨 常见问题

### 1. 数据库连接失败
- 确认MySQL服务已启动
- 检查数据库名称、用户名、密码
- 确认数据库已创建

### 2. JWT认证失败
- 检查请求头是否包含 `Authorization: Bearer <token>`
- 确认token未过期
- 先调用登录接口获取有效token

### 3. 端口占用
- 检查8080端口是否被占用
- 可在 `application.properties` 中修改端口

## 🎉 成功标志

当看到以下日志时，表示服务启动成功：
```
Started HouduanApplication in X.XXX seconds (process running for X.XXX)
Tomcat started on port 8080 (http) with context path '/api'
```

## 📞 下一步开发

1. **完善前端界面** - Vue3管理后台
2. **Android TV应用** - 完善遥控器交互
3. **内容管理** - 批量导入影视资源
4. **性能优化** - 缓存、CDN等

---

**恭喜！** 您的TV端影视应用后端服务已经成功运行！🎊
