{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-ro/values-ro.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,511,639,753,852,945,1075,1201,1316,1450,1615,1744,1869,1979,2076,2208,2299,2404,2507,2611,2713,2835,2949,3081,3209,3325,3446,3563,3686,3798,3916,4003,4088,4195,4331,4489", "endColumns": "106,100,99,97,127,113,98,92,129,125,114,133,164,128,124,109,96,131,90,104,102,103,101,121,113,131,127,115,120,116,122,111,117,86,84,106,135,157,95", "endOffsets": "207,308,408,506,634,748,847,940,1070,1196,1311,1445,1610,1739,1864,1974,2071,2203,2294,2399,2502,2606,2708,2830,2944,3076,3204,3320,3441,3558,3681,3793,3911,3998,4083,4190,4326,4484,4580"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,939,1040,1140,1238,1366,1480,1579,1672,1802,1928,2043,2177,2342,2471,2596,2706,2803,2935,3026,3131,3234,3338,3440,3562,3676,3808,3936,4052,4173,4290,4413,4525,4643,4730,4815,4922,5058,5216", "endColumns": "106,100,99,97,127,113,98,92,129,125,114,133,164,128,124,109,96,131,90,104,102,103,101,121,113,131,127,115,120,116,122,111,117,86,84,106,135,157,95", "endOffsets": "934,1035,1135,1233,1361,1475,1574,1667,1797,1923,2038,2172,2337,2466,2591,2701,2798,2930,3021,3126,3229,3333,3435,3557,3671,3803,3931,4047,4168,4285,4408,4520,4638,4725,4810,4917,5053,5211,5307"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,504,606,715,5312", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "198,300,400,499,601,710,827,5408"}}]}]}