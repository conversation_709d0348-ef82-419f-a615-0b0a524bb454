package com.example.myapplicationtv

import android.os.Bundle
import com.example.myapplicationtv.base.BaseTVActivity

/**
 * 搜索Activity
 */
class SearchActivity : BaseTVActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_search)
        
        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .replace(R.id.search_fragment, SearchFragment())
                .commitNow()
        }
    }

    companion object {
        const val SEARCH_QUERY = "search_query"
    }
}
