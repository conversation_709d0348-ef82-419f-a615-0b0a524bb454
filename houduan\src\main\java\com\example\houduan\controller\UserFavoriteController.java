package com.example.houduan.controller;

import com.example.houduan.common.PageResult;
import com.example.houduan.common.Result;
import com.example.houduan.dto.MovieResponse;
import com.example.houduan.service.UserFavoriteService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

/**
 * 用户收藏控制器
 */
@RestController
@RequestMapping("/favorites")
@RequiredArgsConstructor
public class UserFavoriteController {

    private final UserFavoriteService userFavoriteService;

    /**
     * 添加收藏
     */
    @PostMapping("/{movieId}")
    public Result<String> addFavorite(@PathVariable String movieId, Authentication authentication) {
        try {
            String userId = getUserId(authentication);
            userFavoriteService.addFavorite(userId, movieId);
            return Result.success("收藏成功");
        } catch (Exception e) {
            return Result.error("收藏失败: " + e.getMessage());
        }
    }

    /**
     * 取消收藏
     */
    @DeleteMapping("/{movieId}")
    public Result<String> removeFavorite(@PathVariable String movieId, Authentication authentication) {
        try {
            String userId = getUserId(authentication);
            userFavoriteService.removeFavorite(userId, movieId);
            return Result.success("取消收藏成功");
        } catch (Exception e) {
            return Result.error("取消收藏失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否已收藏
     */
    @GetMapping("/check/{movieId}")
    public Result<Boolean> checkFavorite(@PathVariable String movieId, Authentication authentication) {
        try {
            String userId = getUserId(authentication);
            boolean isFavorited = userFavoriteService.isFavorited(userId, movieId);
            return Result.success(isFavorited);
        } catch (Exception e) {
            return Result.error("检查收藏状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户收藏列表
     */
    @GetMapping("/list")
    public Result<PageResult<MovieResponse>> getFavoriteList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
        try {
            String userId = getUserId(authentication);
            PageResult<MovieResponse> result = userFavoriteService.getUserFavorites(userId, page, size);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("获取收藏列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户收藏数量
     */
    @GetMapping("/count")
    public Result<Long> getFavoriteCount(Authentication authentication) {
        try {
            String userId = getUserId(authentication);
            long count = userFavoriteService.getUserFavoriteCount(userId);
            return Result.success(count);
        } catch (Exception e) {
            return Result.error("获取收藏数量失败: " + e.getMessage());
        }
    }

    /**
     * 从认证信息中获取用户ID
     */
    private String getUserId(Authentication authentication) {
        if (authentication == null || authentication.getPrincipal() == null) {
            throw new RuntimeException("用户未登录");
        }
        
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        return userDetails.getUsername(); // 这里需要根据实际情况调整获取用户ID的方式
    }
}
