<template>
  <div class="app-container">
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="left"
      label-width="100px"
      style="width: 600px; margin-left:50px;"
    >
      <el-form-item label="游戏名称" prop="name">
        <el-input v-model="temp.name" placeholder="请输入游戏名称" />
      </el-form-item>
      <el-form-item label="游戏封面" prop="cover">
        <el-input v-model="temp.cover" placeholder="请输入封面URL" />
        <div style="margin-top: 10px;">
          <el-image
            v-if="temp.cover"
            style="width: 150px; height: 100px"
            :src="temp.cover"
            fit="cover"
          />
        </div>
      </el-form-item>
      <el-form-item label="游戏图标" prop="icon">
        <el-input v-model="temp.icon" placeholder="请输入图标URL" />
        <div style="margin-top: 10px;">
          <el-image
            v-if="temp.icon"
            style="width: 100px; height: 100px"
            :src="temp.icon"
            fit="cover"
          />
        </div>
      </el-form-item>
      <el-form-item label="分类" prop="category">
        <el-select v-model="temp.category" placeholder="请选择分类">
          <el-option
            v-for="item in categoryOptions"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开发商" prop="developer">
        <el-input v-model="temp.developer" placeholder="请输入开发商" />
      </el-form-item>
      <el-form-item label="大小(MB)" prop="size">
        <el-input-number
          v-model="temp.size"
          :min="0"
          :max="50000"
          placeholder="请输入游戏大小"
        />
      </el-form-item>
      <el-form-item label="评分" prop="rating">
        <el-rate
          v-model="temp.rating"
          :max="5"
          show-score
          text-color="#ff9900"
        />
      </el-form-item>
      <el-form-item label="下载地址" prop="downloadUrl">
        <el-input v-model="temp.downloadUrl" placeholder="请输入下载地址" />
      </el-form-item>
      <el-form-item label="游戏描述" prop="description">
        <el-input
          v-model="temp.description"
          :autosize="{ minRows: 3, maxRows: 6 }"
          type="textarea"
          placeholder="请输入游戏描述"
        />
      </el-form-item>
      <el-form-item label="是否推荐">
        <el-switch
          v-model="temp.isRecommended"
          active-color="#13ce66"
          inactive-color="#ff4949"
        />
      </el-form-item>
      <el-form-item label="是否精选">
        <el-switch
          v-model="temp.isFeatured"
          active-color="#13ce66"
          inactive-color="#ff4949"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-radio-group v-model="temp.status">
          <el-radio value="ACTIVE">启用</el-radio>
          <el-radio value="INACTIVE">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div style="margin-left: 50px;">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="confirmData">确认</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getGame, createGame, updateGame } from '@/api/games'
import { getCategories } from '@/api/categories'

const route = useRoute()
const router = useRouter()

const temp = reactive({
  id: undefined,
  name: '',
  cover: '',
  icon: '',
  category: '',
  developer: '',
  size: 0,
  rating: 0,
  downloadUrl: '',
  description: '',
  isRecommended: false,
  isFeatured: false,
  status: 'ACTIVE'
})

const categoryOptions = ref([])

const rules = {
  name: [{ required: true, message: '游戏名称是必填项', trigger: 'blur' }],
  cover: [{ required: true, message: '游戏封面是必填项', trigger: 'blur' }],
  category: [{ required: true, message: '分类是必填项', trigger: 'change' }],
  developer: [{ required: true, message: '开发商是必填项', trigger: 'blur' }],
  downloadUrl: [{ required: true, message: '下载地址是必填项', trigger: 'blur' }],
  description: [{ required: true, message: '游戏描述是必填项', trigger: 'blur' }]
}

const getGameCategories = async () => {
  try {
    const response = await getCategories({ type: 'GAME' })
    categoryOptions.value = response.data || []
  } catch (error) {
    console.error('获取分类失败:', error)
    // 如果API失败，使用模拟数据
    categoryOptions.value = [
      { id: 1, name: '竞技' },
      { id: 2, name: '射击' },
      { id: 3, name: '角色扮演' },
      { id: 4, name: '策略' }
    ]
  }
}

const getGameData = async () => {
  try {
    const response = await getGame(temp.id)
    Object.assign(temp, response.data)
  } catch (error) {
    console.error('获取游戏数据失败:', error)
    ElMessage.error('获取游戏数据失败')
  }
}

onMounted(async () => {
  await getGameCategories()
  if (route.params && route.params.id) {
    temp.id = route.params.id
    await getGameData()
  }
})
const confirmData = async () => {
  try {
    if (temp.id !== undefined) {
      await updateGame(temp)
      ElMessage.success('更新成功')
    } else {
      await createGame(temp)
      ElMessage.success('创建成功')
    }
    router.push('/games/list')
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败，请重试')
  }
}

const cancel = () => {
  router.push('/games/list')
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
