package com.example.myapplicationtv.repository

import com.example.myapplicationtv.model.*
import com.example.myapplicationtv.network.ApiClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 内容数据仓库
 */
class ContentRepository {
    
    private val apiService = ApiClient.getApiService()
    
    // 电影相关方法
    suspend fun getMovies(
        page: Int = 1,
        size: Int = 20,
        keyword: String? = null,
        category: String? = null,
        sort: String = "latest"
    ): Result<PageResult<MovieResponse>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getMovies(page, size, keyword, category, sort)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取电影列表失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getMovieById(id: String): Result<MovieResponse> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getMovieById(id)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取电影详情失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getRecommendedMovies(limit: Int = 10): Result<List<MovieResponse>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getRecommendedMovies(limit)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取推荐电影失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getHotMovies(limit: Int = 10): Result<List<MovieResponse>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getHotMovies(limit)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取热门电影失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // 应用相关方法
    suspend fun getApps(
        page: Int = 1,
        size: Int = 20,
        keyword: String? = null,
        category: String? = null,
        sort: String = "latest"
    ): Result<PageResult<AppResponse>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getApps(page, size, keyword, category, sort)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取应用列表失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getRecommendedApps(limit: Int = 10): Result<List<AppResponse>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getRecommendedApps(limit)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取推荐应用失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // 游戏相关方法
    suspend fun getGames(
        page: Int = 1,
        size: Int = 20,
        keyword: String? = null,
        category: String? = null,
        sort: String = "latest"
    ): Result<PageResult<GameResponse>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getGames(page, size, keyword, category, sort)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取游戏列表失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getRecommendedGames(limit: Int = 10): Result<List<GameResponse>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getRecommendedGames(limit)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取推荐游戏失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getFeaturedGames(limit: Int = 10): Result<List<GameResponse>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getFeaturedGames(limit)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取精选游戏失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // 商品相关方法
    suspend fun getProducts(
        page: Int = 1,
        size: Int = 20,
        keyword: String? = null,
        category: String? = null,
        brand: String? = null,
        sort: String = "latest"
    ): Result<PageResult<ProductResponse>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getProducts(page, size, keyword, category, brand, sort)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取商品列表失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getRecommendedProducts(limit: Int = 10): Result<List<ProductResponse>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getRecommendedProducts(limit)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取推荐商品失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // 分类相关方法
    suspend fun getCategories(type: String? = null): Result<List<CategoryResponse>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getCategories(type)
            if (response.isSuccessful && response.body()?.success == true) {
                Result.success(response.body()!!.data!!)
            } else {
                Result.failure(Exception(response.body()?.message ?: "获取分类列表失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
