package com.example.myapplicationtv

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.cardview.widget.CardView
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.adapter.AppCategoryAdapter
import com.example.myapplicationtv.adapter.RecommendedAppAdapter
import com.example.myapplicationtv.model.AppCategory
import com.example.myapplicationtv.viewmodel.ApplicationViewModel

/**
 * 应用页面Fragment
 */
class ApplicationFragment : Fragment() {

    private lateinit var rvAppCategories: RecyclerView
    private lateinit var rvRecommendedApps: RecyclerView
    private lateinit var cvAppStoreBanner: CardView
    private lateinit var viewModel: ApplicationViewModel
    private lateinit var categoryAdapter: AppCategoryAdapter
    private lateinit var recommendedAppAdapter: RecommendedAppAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_application, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews(view)
        initViewModel()
        setupRecyclerViews()
        observeData()
    }

    private fun initViews(view: View) {
        rvAppCategories = view.findViewById(R.id.rv_app_categories)
        rvRecommendedApps = view.findViewById(R.id.rv_recommended_apps)
        cvAppStoreBanner = view.findViewById(R.id.cv_app_store_banner)
    }

    private fun initViewModel() {
        viewModel = ViewModelProvider(this)[ApplicationViewModel::class.java]
    }

    private fun setupRecyclerViews() {
        // 设置应用分类RecyclerView（水平滚动）
        categoryAdapter = AppCategoryAdapter { category ->
            Toast.makeText(context, "点击了分类: ${category.name}", Toast.LENGTH_SHORT).show()
        }
        rvAppCategories.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        rvAppCategories.adapter = categoryAdapter

        // 设置推荐应用RecyclerView（4列网格）
        recommendedAppAdapter = RecommendedAppAdapter { app ->
            Toast.makeText(context, "点击了应用: ${app.title}", Toast.LENGTH_SHORT).show()
        }
        rvRecommendedApps.layoutManager = GridLayoutManager(context, 4)
        rvRecommendedApps.adapter = recommendedAppAdapter

        // 设置应用商店横幅点击事件
        cvAppStoreBanner.setOnClickListener {
            Toast.makeText(context, "打开应用商店", Toast.LENGTH_SHORT).show()
        }
    }

    private fun observeData() {
        viewModel.appCategories.observe(viewLifecycleOwner) { categories ->
            categoryAdapter.updateCategories(categories)
        }

        viewModel.recommendedApps.observe(viewLifecycleOwner) { apps ->
            recommendedAppAdapter.updateApps(apps)
        }
    }
}
