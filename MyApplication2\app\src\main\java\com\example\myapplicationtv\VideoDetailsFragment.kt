package com.example.myapplicationtv

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.graphics.drawable.Drawable
import androidx.leanback.app.DetailsSupportFragment
import androidx.leanback.app.DetailsSupportFragmentBackgroundController
import androidx.leanback.widget.Action
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ClassPresenterSelector
import androidx.leanback.widget.DetailsOverviewRow
import androidx.leanback.widget.FullWidthDetailsOverviewRowPresenter
import androidx.leanback.widget.FullWidthDetailsOverviewSharedElementHelper
import androidx.leanback.widget.HeaderItem
import androidx.leanback.widget.ImageCardView
import androidx.leanback.widget.ListRow
import androidx.leanback.widget.ListRowPresenter
import androidx.leanback.widget.OnActionClickedListener
import androidx.leanback.widget.OnItemViewClickedListener
import androidx.leanback.widget.Presenter
import androidx.leanback.widget.Row
import androidx.leanback.widget.RowPresenter
import androidx.core.app.ActivityOptionsCompat
import androidx.core.content.ContextCompat
import android.util.Log
import android.widget.Toast

import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition

import java.util.Collections

/**
 * A wrapper fragment for leanback details screens.
 * It shows a detailed view of video and its metadata plus related videos.
 */
class VideoDetailsFragment : DetailsSupportFragment() {

    private var mSelectedMovie: Movie? = null
    private var mSelectedContentItem: ContentItem? = null

    private lateinit var mDetailsBackground: DetailsSupportFragmentBackgroundController
    private lateinit var mPresenterSelector: ClassPresenterSelector
    private lateinit var mAdapter: ArrayObjectAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        Log.d(TAG, "onCreate DetailsFragment")
        super.onCreate(savedInstanceState)

        mDetailsBackground = DetailsSupportFragmentBackgroundController(this)

        // 尝试获取ContentItem
        mSelectedContentItem = activity!!.intent.getSerializableExtra(DetailsActivity.CONTENT_ITEM) as? ContentItem

        // 如果没有ContentItem，尝试获取Movie（向后兼容）
        if (mSelectedContentItem == null) {
            mSelectedMovie = activity!!.intent.getSerializableExtra(DetailsActivity.MOVIE) as? Movie
        }

        if (mSelectedContentItem != null || mSelectedMovie != null) {
            mPresenterSelector = ClassPresenterSelector()
            mAdapter = ArrayObjectAdapter(mPresenterSelector)
            setupDetailsOverviewRow()
            setupDetailsOverviewRowPresenter()
            setupRelatedMovieListRow()
            adapter = mAdapter
            initializeBackground()
            onItemViewClickedListener = ItemViewClickedListener()
        } else {
            val intent = Intent(activity!!, MainActivity::class.java)
            startActivity(intent)
        }
    }

    private fun initializeBackground() {
        mDetailsBackground.enableParallax()
        val backgroundUrl = mSelectedContentItem?.backgroundImageUrl ?: mSelectedMovie?.backgroundImageUrl

        Glide.with(activity!!)
            .asBitmap()
            .centerCrop()
            .error(R.drawable.default_background)
            .load(backgroundUrl)
            .into<SimpleTarget<Bitmap>>(object : SimpleTarget<Bitmap>() {
                override fun onResourceReady(
                    bitmap: Bitmap,
                    transition: Transition<in Bitmap>?
                ) {
                    mDetailsBackground.coverBitmap = bitmap
                    mAdapter.notifyArrayItemRangeChanged(0, mAdapter.size())
                }
            })
    }

    private fun setupDetailsOverviewRow() {
        val item = mSelectedContentItem ?: mSelectedMovie
        Log.d(TAG, "doInBackground: " + item?.toString())

        val row = DetailsOverviewRow(item)
        row.imageDrawable = ContextCompat.getDrawable(activity!!, R.drawable.default_background)
        val width = convertDpToPixel(activity!!, DETAIL_THUMB_WIDTH)
        val height = convertDpToPixel(activity!!, DETAIL_THUMB_HEIGHT)

        val imageUrl = mSelectedContentItem?.cardImageUrl ?: mSelectedMovie?.cardImageUrl

        Glide.with(activity!!)
            .load(imageUrl)
            .centerCrop()
            .error(R.drawable.default_background)
            .into<SimpleTarget<Drawable>>(object : SimpleTarget<Drawable>(width, height) {
                override fun onResourceReady(
                    drawable: Drawable,
                    transition: Transition<in Drawable>?
                ) {
                    Log.d(TAG, "details overview card image url ready: " + drawable)
                    row.imageDrawable = drawable
                    mAdapter.notifyArrayItemRangeChanged(0, mAdapter.size())
                }
            })

        val actionAdapter = ArrayObjectAdapter()

        // 根据内容类型添加不同的操作按钮
        when (mSelectedContentItem?.type) {
            ContentType.MOVIE -> {
                actionAdapter.add(
                    Action(
                        ACTION_WATCH_TRAILER,
                        resources.getString(R.string.watch_trailer_1),
                        resources.getString(R.string.watch_trailer_2)
                    )
                )
                actionAdapter.add(
                    Action(
                        ACTION_RENT,
                        resources.getString(R.string.rent_1),
                        resources.getString(R.string.rent_2)
                    )
                )
                actionAdapter.add(
                    Action(
                        ACTION_BUY,
                        resources.getString(R.string.buy_1),
                        resources.getString(R.string.buy_2)
                    )
                )
            }
            ContentType.APP -> {
                actionAdapter.add(
                    Action(
                        ACTION_DOWNLOAD,
                        "下载",
                        "下载应用"
                    )
                )
                actionAdapter.add(
                    Action(
                        ACTION_FAVORITE,
                        "收藏",
                        "添加到收藏"
                    )
                )
            }
            ContentType.GAME -> {
                actionAdapter.add(
                    Action(
                        ACTION_PLAY,
                        "开始游戏",
                        "立即开始游戏"
                    )
                )
                actionAdapter.add(
                    Action(
                        ACTION_DOWNLOAD,
                        "下载",
                        "下载游戏"
                    )
                )
            }
            ContentType.PRODUCT -> {
                actionAdapter.add(
                    Action(
                        ACTION_BUY,
                        "购买",
                        "立即购买"
                    )
                )
                actionAdapter.add(
                    Action(
                        ACTION_FAVORITE,
                        "收藏",
                        "添加到收藏"
                    )
                )
            }
            else -> {
                // 默认操作（兼容原有Movie）
                actionAdapter.add(
                    Action(
                        ACTION_WATCH_TRAILER,
                        resources.getString(R.string.watch_trailer_1),
                        resources.getString(R.string.watch_trailer_2)
                    )
                )
                actionAdapter.add(
                    Action(
                        ACTION_RENT,
                        resources.getString(R.string.rent_1),
                        resources.getString(R.string.rent_2)
                    )
                )
                actionAdapter.add(
                    Action(
                        ACTION_BUY,
                        resources.getString(R.string.buy_1),
                        resources.getString(R.string.buy_2)
                    )
                )
            }
        }

        row.actionsAdapter = actionAdapter
        mAdapter.add(row)
    }

    private fun setupDetailsOverviewRowPresenter() {
        // Set detail background.
        val detailsPresenter = FullWidthDetailsOverviewRowPresenter(DetailsDescriptionPresenter())
        detailsPresenter.backgroundColor =
            ContextCompat.getColor(activity!!, R.color.selected_background)

        // Hook up transition element.
        val sharedElementHelper = FullWidthDetailsOverviewSharedElementHelper()
        sharedElementHelper.setSharedElementEnterTransition(
            activity, DetailsActivity.SHARED_ELEMENT_NAME
        )
        detailsPresenter.setListener(sharedElementHelper)
        detailsPresenter.isParticipatingEntranceTransition = true

        detailsPresenter.onActionClickedListener = OnActionClickedListener { action ->
            when (action.id) {
                ACTION_WATCH_TRAILER -> {
                    val intent = Intent(activity!!, PlaybackActivity::class.java)
                    intent.putExtra(DetailsActivity.MOVIE, mSelectedMovie)
                    startActivity(intent)
                }
                ACTION_PLAY -> {
                    Toast.makeText(activity!!, "启动游戏: ${mSelectedContentItem?.title}", Toast.LENGTH_SHORT).show()
                }
                ACTION_DOWNLOAD -> {
                    Toast.makeText(activity!!, "开始下载: ${mSelectedContentItem?.title}", Toast.LENGTH_SHORT).show()
                }
                ACTION_FAVORITE -> {
                    Toast.makeText(activity!!, "已添加到收藏: ${mSelectedContentItem?.title}", Toast.LENGTH_SHORT).show()
                }
                else -> {
                    Toast.makeText(activity!!, action.toString(), Toast.LENGTH_SHORT).show()
                }
            }
        }
        mPresenterSelector.addClassPresenter(DetailsOverviewRow::class.java, detailsPresenter)
    }

    private fun setupRelatedMovieListRow() {
        val subcategories = arrayOf(getString(R.string.related_movies))
        val list = MovieList.list

        Collections.shuffle(list)
        val listRowAdapter = ArrayObjectAdapter(CardPresenter())
        for (j in 0 until NUM_COLS) {
            listRowAdapter.add(list[j % 5])
        }

        val header = HeaderItem(0, subcategories[0])
        mAdapter.add(ListRow(header, listRowAdapter))
        mPresenterSelector.addClassPresenter(ListRow::class.java, ListRowPresenter())
    }

    private fun convertDpToPixel(context: Context, dp: Int): Int {
        val density = context.applicationContext.resources.displayMetrics.density
        return Math.round(dp.toFloat() * density)
    }

    private inner class ItemViewClickedListener : OnItemViewClickedListener {
        override fun onItemClicked(
            itemViewHolder: Presenter.ViewHolder?,
            item: Any?,
            rowViewHolder: RowPresenter.ViewHolder,
            row: Row
        ) {
            if (item is Movie) {
                Log.d(TAG, "Item: " + item.toString())
                val intent = Intent(activity!!, DetailsActivity::class.java)
                intent.putExtra(resources.getString(R.string.movie), mSelectedMovie)

                val bundle =
                    ActivityOptionsCompat.makeSceneTransitionAnimation(
                        activity!!,
                        (itemViewHolder?.view as ImageCardView).mainImageView,
                        DetailsActivity.SHARED_ELEMENT_NAME
                    )
                        .toBundle()
                startActivity(intent, bundle)
            }
        }
    }

    companion object {
        private val TAG = "VideoDetailsFragment"

        private val ACTION_WATCH_TRAILER = 1L
        private val ACTION_RENT = 2L
        private val ACTION_BUY = 3L
        private val ACTION_PLAY = 4L
        private val ACTION_DOWNLOAD = 5L
        private val ACTION_FAVORITE = 6L

        private val DETAIL_THUMB_WIDTH = 274
        private val DETAIL_THUMB_HEIGHT = 274

        private val NUM_COLS = 10
    }
}