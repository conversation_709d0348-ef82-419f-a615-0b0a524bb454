[versions]
agp = "8.10.1"
kotlin = "2.0.21"
coreKtx = "1.10.1"
leanback = "1.0.0"
glide = "4.11.0"

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-leanback = { group = "androidx.leanback", name = "leanback", version.ref = "leanback" }
glide = { group = "com.github.bumptech.glide", name = "glide", version.ref = "glide" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }

