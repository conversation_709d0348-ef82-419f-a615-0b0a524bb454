{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-sw/values-sw.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,499,606,713,5190", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "194,296,393,494,601,708,823,5286"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,504,643,757,856,945,1063,1181,1293,1414,1557,1675,1793,1899,1992,2124,2214,2315,2422,2523,2623,2740,2856,2982,3108,3221,3341,3462,3584,3696,3809,3896,3980,4082,4217,4370", "endColumns": "106,100,96,93,138,113,98,88,117,117,111,120,142,117,117,105,92,131,89,100,106,100,99,116,115,125,125,112,119,120,121,111,112,86,83,101,134,152,96", "endOffsets": "207,308,405,499,638,752,851,940,1058,1176,1288,1409,1552,1670,1788,1894,1987,2119,2209,2310,2417,2518,2618,2735,2851,2977,3103,3216,3336,3457,3579,3691,3804,3891,3975,4077,4212,4365,4462"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "828,935,1036,1133,1227,1366,1480,1579,1668,1786,1904,2016,2137,2280,2398,2516,2622,2715,2847,2937,3038,3145,3246,3346,3463,3579,3705,3831,3944,4064,4185,4307,4419,4532,4619,4703,4805,4940,5093", "endColumns": "106,100,96,93,138,113,98,88,117,117,111,120,142,117,117,105,92,131,89,100,106,100,99,116,115,125,125,112,119,120,121,111,112,86,83,101,134,152,96", "endOffsets": "930,1031,1128,1222,1361,1475,1574,1663,1781,1899,2011,2132,2275,2393,2511,2617,2710,2842,2932,3033,3140,3241,3341,3458,3574,3700,3826,3939,4059,4180,4302,4414,4527,4614,4698,4800,4935,5088,5185"}}]}]}