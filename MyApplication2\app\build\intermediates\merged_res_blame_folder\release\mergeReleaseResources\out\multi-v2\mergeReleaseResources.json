{"logs": [{"outputFile": "com.example.myapplicationtv.app-release-31:/values-hi_values-hi.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,411,512,625,731,5311", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "198,301,406,507,620,726,853,5407"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,509,631,743,843,937,1069,1201,1313,1441,1594,1738,1882,1989,2080,2211,2301,2406,2511,2616,2715,2840,2949,3073,3197,3299,3406,3536,3657,3783,3905,3992,4075,4171,4306,4460", "endColumns": "106,100,98,96,121,111,99,93,131,131,111,127,152,143,143,106,90,130,89,104,104,104,98,124,108,123,123,101,106,129,120,125,121,86,82,95,134,153,97", "endOffsets": "207,308,407,504,626,738,838,932,1064,1196,1308,1436,1589,1733,1877,1984,2075,2206,2296,2401,2506,2611,2710,2835,2944,3068,3192,3294,3401,3531,3652,3778,3900,3987,4070,4166,4301,4455,4553"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "858,965,1066,1165,1262,1384,1496,1596,1690,1822,1954,2066,2194,2347,2491,2635,2742,2833,2964,3054,3159,3264,3369,3468,3593,3702,3826,3950,4052,4159,4289,4410,4536,4658,4745,4828,4924,5059,5213", "endColumns": "106,100,98,96,121,111,99,93,131,131,111,127,152,143,143,106,90,130,89,104,104,104,98,124,108,123,123,101,106,129,120,125,121,86,82,95,134,153,97", "endOffsets": "960,1061,1160,1257,1379,1491,1591,1685,1817,1949,2061,2189,2342,2486,2630,2737,2828,2959,3049,3154,3259,3364,3463,3588,3697,3821,3945,4047,4154,4284,4405,4531,4653,4740,4823,4919,5054,5208,5306"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-cs_values-cs.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,406,505,610,717,5264", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "198,300,401,500,605,712,831,5360"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,509,639,753,850,940,1061,1181,1288,1411,1573,1697,1820,1923,2019,2149,2241,2344,2446,2559,2659,2776,2897,3019,3140,3248,3364,3488,3614,3738,3864,3951,4035,4139,4274,4440", "endColumns": "106,100,99,95,129,113,96,89,120,119,106,122,161,123,122,102,95,129,91,102,101,112,99,116,120,121,120,107,115,123,125,123,125,86,83,103,134,165,92", "endOffsets": "207,308,408,504,634,748,845,935,1056,1176,1283,1406,1568,1692,1815,1918,2014,2144,2236,2339,2441,2554,2654,2771,2892,3014,3135,3243,3359,3483,3609,3733,3859,3946,4030,4134,4269,4435,4528"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,943,1044,1144,1240,1370,1484,1581,1671,1792,1912,2019,2142,2304,2428,2551,2654,2750,2880,2972,3075,3177,3290,3390,3507,3628,3750,3871,3979,4095,4219,4345,4469,4595,4682,4766,4870,5005,5171", "endColumns": "106,100,99,95,129,113,96,89,120,119,106,122,161,123,122,102,95,129,91,102,101,112,99,116,120,121,120,107,115,123,125,123,125,86,83,103,134,165,92", "endOffsets": "938,1039,1139,1235,1365,1479,1576,1666,1787,1907,2014,2137,2299,2423,2546,2649,2745,2875,2967,3070,3172,3285,3385,3502,3623,3745,3866,3974,4090,4214,4340,4464,4590,4677,4761,4865,5000,5166,5259"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-b+sr+Latn_values-b+sr+Latn.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,402,506,610,715,5242", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "198,300,397,501,605,710,826,5338"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,504,629,744,841,935,1058,1178,1287,1412,1568,1693,1815,1918,2012,2136,2226,2327,2439,2542,2644,2763,2880,3004,3125,3234,3349,3470,3593,3709,3827,3914,4002,4119,4258,4424", "endColumns": "106,100,96,93,124,114,96,93,122,119,108,124,155,124,121,102,93,123,89,100,111,102,101,118,116,123,120,108,114,120,122,115,117,86,87,116,138,165,91", "endOffsets": "207,308,405,499,624,739,836,930,1053,1173,1282,1407,1563,1688,1810,1913,2007,2131,2221,2322,2434,2537,2639,2758,2875,2999,3120,3229,3344,3465,3588,3704,3822,3909,3997,4114,4253,4419,4511"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,938,1039,1136,1230,1355,1470,1567,1661,1784,1904,2013,2138,2294,2419,2541,2644,2738,2862,2952,3053,3165,3268,3370,3489,3606,3730,3851,3960,4075,4196,4319,4435,4553,4640,4728,4845,4984,5150", "endColumns": "106,100,96,93,124,114,96,93,122,119,108,124,155,124,121,102,93,123,89,100,111,102,101,118,116,123,120,108,114,120,122,115,117,86,87,116,138,165,91", "endOffsets": "933,1034,1131,1225,1350,1465,1562,1656,1779,1899,2008,2133,2289,2414,2536,2639,2733,2857,2947,3048,3160,3263,3365,3484,3601,3725,3846,3955,4070,4191,4314,4430,4548,4635,4723,4840,4979,5145,5237"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-en-rXC_values-en-rXC.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,301,506,707,908,1115,1320,9733", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "296,501,702,903,1110,1315,1527,9932"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,313,516,713,909,1130,1344,1541,1739,1972,2202,2409,2629,2856,3078,3297,3503,3694,3924,4113,4316,4521,4724,4917,5129,5337,5550,5761,5962,6170,6382,6599,6807,7018,7207,7393,7594,7846,8114", "endColumns": "207,202,196,195,220,213,196,197,232,229,206,219,226,221,218,205,190,229,188,202,204,202,192,211,207,212,210,200,207,211,216,207,210,188,185,200,251,267,191", "endOffsets": "308,511,708,904,1125,1339,1536,1734,1967,2197,2404,2624,2851,3073,3292,3498,3689,3919,4108,4311,4516,4719,4912,5124,5332,5545,5756,5957,6165,6377,6594,6802,7013,7202,7388,7589,7841,8109,8301"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1532,1740,1943,2140,2336,2557,2771,2968,3166,3399,3629,3836,4056,4283,4505,4724,4930,5121,5351,5540,5743,5948,6151,6344,6556,6764,6977,7188,7389,7597,7809,8026,8234,8445,8634,8820,9021,9273,9541", "endColumns": "207,202,196,195,220,213,196,197,232,229,206,219,226,221,218,205,190,229,188,202,204,202,192,211,207,212,210,200,207,211,216,207,210,188,185,200,251,267,191", "endOffsets": "1735,1938,2135,2331,2552,2766,2963,3161,3394,3624,3831,4051,4278,4500,4719,4925,5116,5346,5535,5738,5943,6146,6339,6551,6759,6972,7183,7384,7592,7804,8021,8229,8440,8629,8815,9016,9268,9536,9728"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-v21_values-v21.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,7,13,17,20,25,29,52,55,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,418,933,1169,1377,1801,2095,3791,3989,4180", "endLines": "2,6,12,16,19,24,28,51,54,58,59", "endColumns": "88,12,12,12,12,12,12,12,12,12,72", "endOffsets": "139,413,928,1164,1372,1796,2090,3786,3984,4175,4248"}, "to": {"startLines": "22,23,27,33,37,40,45,49,78,81,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1530,1619,1893,2408,2644,2852,3276,3570,5429,5627,5818", "endLines": "22,26,32,36,39,44,48,69,80,84,85", "endColumns": "88,12,12,12,12,12,12,12,12,12,72", "endOffsets": "1614,1888,2403,2639,2847,3271,3565,4900,5622,5813,5886"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,3,4,5,6,7,14,18,70,73", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,941,1235,4905,5077", "endLines": "2,3,4,5,6,7,14,18,72,77", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,1062,1358,5072,5424"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\d1cb9debee0ec7aff0e9116da1dc9922\\transformed\\media-1.0.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "8,11,15,19", "startColumns": "4,4,4,4", "startOffsets": "610,778,1067,1363", "endLines": "10,13,17,21", "endColumns": "12,12,12,12", "endOffsets": "773,936,1230,1525"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-te_values-te.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,415,512,633,744,842,938,1075,1212,1327,1458,1604,1729,1854,1961,2056,2191,2285,2396,2510,2624,2723,2839,2953,3075,3197,3308,3424,3543,3672,3788,3914,4001,4083,4201,4341,4512", "endColumns": "106,100,101,96,120,110,97,95,136,136,114,130,145,124,124,106,94,134,93,110,113,113,98,115,113,121,121,110,115,118,128,115,125,86,81,117,139,170,85", "endOffsets": "207,308,410,507,628,739,837,933,1070,1207,1322,1453,1599,1724,1849,1956,2051,2186,2280,2391,2505,2619,2718,2834,2948,3070,3192,3303,3419,3538,3667,3783,3909,3996,4078,4196,4336,4507,4593"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "855,962,1063,1165,1262,1383,1494,1592,1688,1825,1962,2077,2208,2354,2479,2604,2711,2806,2941,3035,3146,3260,3374,3473,3589,3703,3825,3947,4058,4174,4293,4422,4538,4664,4751,4833,4951,5091,5262", "endColumns": "106,100,101,96,120,110,97,95,136,136,114,130,145,124,124,106,94,134,93,110,113,113,98,115,113,121,121,110,115,118,128,115,125,86,81,117,139,170,85", "endOffsets": "957,1058,1160,1257,1378,1489,1587,1683,1820,1957,2072,2203,2349,2474,2599,2706,2801,2936,3030,3141,3255,3369,3468,3584,3698,3820,3942,4053,4169,4288,4417,4533,4659,4746,4828,4946,5086,5257,5343"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,315,417,518,624,731,5348", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "202,310,412,513,619,726,850,5444"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-v19_values-v19.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-v19\\values-v19.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "10", "endColumns": "12", "endOffsets": "659"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-kn_values-kn.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,512,636,750,847,942,1085,1224,1333,1458,1601,1737,1869,1980,2071,2207,2296,2409,2526,2637,2731,2842,2961,3089,3213,3320,3431,3546,3664,3776,3891,3978,4062,4162,4297,4454", "endColumns": "106,100,99,98,123,113,96,94,142,138,108,124,142,135,131,110,90,135,88,112,116,110,93,110,118,127,123,106,110,114,117,111,114,86,83,99,134,156,90", "endOffsets": "207,308,408,507,631,745,842,937,1080,1219,1328,1453,1596,1732,1864,1975,2066,2202,2291,2404,2521,2632,2726,2837,2956,3084,3208,3315,3426,3541,3659,3771,3886,3973,4057,4157,4292,4449,4540"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "850,957,1058,1158,1257,1381,1495,1592,1687,1830,1969,2078,2203,2346,2482,2614,2725,2816,2952,3041,3154,3271,3382,3476,3587,3706,3834,3958,4065,4176,4291,4409,4521,4636,4723,4807,4907,5042,5199", "endColumns": "106,100,99,98,123,113,96,94,142,138,108,124,142,135,131,110,90,135,88,112,116,110,93,110,118,127,123,106,110,114,117,111,114,86,83,99,134,156,90", "endOffsets": "952,1053,1153,1252,1376,1490,1587,1682,1825,1964,2073,2198,2341,2477,2609,2720,2811,2947,3036,3149,3266,3377,3471,3582,3701,3829,3953,4060,4171,4286,4404,4516,4631,4718,4802,4902,5037,5194,5285"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,407,513,614,722,5290", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "198,301,402,508,609,717,845,5386"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-tl_values-tl.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,405,502,609,717,5329", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "197,299,400,497,604,712,834,5425"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,508,646,763,862,956,1093,1228,1335,1463,1616,1748,1878,1995,2088,2221,2312,2415,2521,2626,2721,2838,2960,3081,3200,3310,3425,3552,3672,3795,3911,3998,4084,4193,4333,4496", "endColumns": "106,100,99,94,137,116,98,93,136,134,106,127,152,131,129,116,92,132,90,102,105,104,94,116,121,120,118,109,114,126,119,122,115,86,85,108,139,162,98", "endOffsets": "207,308,408,503,641,758,857,951,1088,1223,1330,1458,1611,1743,1873,1990,2083,2216,2307,2410,2516,2621,2716,2833,2955,3076,3195,3305,3420,3547,3667,3790,3906,3993,4079,4188,4328,4491,4590"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "839,946,1047,1147,1242,1380,1497,1596,1690,1827,1962,2069,2197,2350,2482,2612,2729,2822,2955,3046,3149,3255,3360,3455,3572,3694,3815,3934,4044,4159,4286,4406,4529,4645,4732,4818,4927,5067,5230", "endColumns": "106,100,99,94,137,116,98,93,136,134,106,127,152,131,129,116,92,132,90,102,105,104,94,116,121,120,118,109,114,126,119,122,115,86,85,108,139,162,98", "endOffsets": "941,1042,1142,1237,1375,1492,1591,1685,1822,1957,2064,2192,2345,2477,2607,2724,2817,2950,3041,3144,3250,3355,3450,3567,3689,3810,3929,4039,4154,4281,4401,4524,4640,4727,4813,4922,5062,5225,5324"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-en-rIN_values-en-rIN.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,505,624,736,832,928,1059,1188,1293,1414,1542,1663,1782,1887,1978,2106,2195,2296,2399,2500,2593,2703,2809,2920,3029,3128,3235,3345,3461,3567,3679,3766,3850,3950,4085,4236", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "207,308,406,500,619,731,827,923,1054,1183,1288,1409,1537,1658,1777,1882,1973,2101,2190,2291,2394,2495,2588,2698,2804,2915,3024,3123,3230,3340,3456,3562,3674,3761,3845,3945,4080,4231,4321"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "824,931,1032,1130,1224,1343,1455,1551,1647,1778,1907,2012,2133,2261,2382,2501,2606,2697,2825,2914,3015,3118,3219,3312,3422,3528,3639,3748,3847,3954,4064,4180,4286,4398,4485,4569,4669,4804,4955", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "926,1027,1125,1219,1338,1450,1546,1642,1773,1902,2007,2128,2256,2377,2496,2601,2692,2820,2909,3010,3113,3214,3307,3417,3523,3634,3743,3842,3949,4059,4175,4281,4393,4480,4564,4664,4799,4950,5040"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,501,605,708,5045", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "196,298,397,496,600,703,819,5141"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values_values.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\9717948c970c19acce1692941c22c532\\transformed\\activity-1.7.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "417,437", "startColumns": "4,4", "startOffsets": "27256,28313", "endColumns": "41,59", "endOffsets": "27293,28368"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\6c7c511ec8b68e45e391fb30b6d5f144\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "394,416", "startColumns": "4,4", "startOffsets": "26071,27189", "endColumns": "53,66", "endOffsets": "26120,27251"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\cffe277412d6e71dab1701f200c8c21e\\transformed\\recyclerview-1.3.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "4,80,81,82,83,84,85,395,1873", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "226,5214,5273,5321,5377,5452,5528,26125,106812", "endLines": "4,80,81,82,83,84,85,395,1893", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "277,5268,5316,5372,5447,5523,5595,26186,107647"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\59e3767b96f4ee90f4b57a0c56b92a96\\transformed\\glide-4.11.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "393", "startColumns": "4", "startOffsets": "26013", "endColumns": "57", "endOffsets": "26066"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\b9b3bed66d3a00e5a31ccb8c12557bab\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "438", "startColumns": "4", "startOffsets": "28373", "endColumns": "53", "endOffsets": "28422"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\fc95bcae0ba172be27074a8a115ec3a7\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "478", "startColumns": "4", "startOffsets": "31058", "endColumns": "82", "endOffsets": "31136"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\cec8346c4e2ab21f142e352fe0bfc4ee\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "2,1371,1390,1396", "startColumns": "4,4,4,4", "startOffsets": "105,87507,88156,88367", "endLines": "2,1373,1395,1479", "endColumns": "60,12,24,24", "endOffsets": "161,87647,88362,92878"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "3,5,6,9,10,66,67,73,74,75,76,77,78,79,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,347,348,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,414,415,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,477,483,484,485,486,487,488,489,538,541,542,547,550,555,822,823,1374,1380,1480,1513,1543,1576", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "166,282,354,560,625,4259,4328,4726,4796,4864,4936,5006,5067,5141,21696,21757,21818,21880,21944,22006,22067,22135,22235,22295,22361,22434,22503,22560,22612,23116,23188,23981,24046,24105,24164,24224,24284,24344,24404,24464,24524,24584,24644,24704,24764,24823,24883,24943,25003,25063,25123,25183,25243,25303,25363,25423,25482,25542,25602,25661,25720,25779,25838,25897,27119,27154,27363,27418,27481,27536,27594,27652,27713,27776,27833,27884,27934,27995,28052,28118,28152,28187,30988,31349,31416,31488,31557,31626,31700,31772,35393,35571,35688,35955,36248,36515,54648,54720,87652,87855,92883,94614,95614,96296", "endLines": "3,5,6,9,10,66,67,73,74,75,76,77,78,79,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,347,348,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,414,415,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,477,483,484,485,486,487,488,489,538,541,545,547,553,555,822,823,1379,1389,1512,1533,1575,1581", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "221,349,437,620,686,4323,4386,4791,4859,4931,5001,5062,5136,5209,21752,21813,21875,21939,22001,22062,22130,22230,22290,22356,22429,22498,22555,22607,22669,23183,23259,24041,24100,24159,24219,24279,24339,24399,24459,24519,24579,24639,24699,24759,24818,24878,24938,24998,25058,25118,25178,25238,25298,25358,25418,25477,25537,25597,25656,25715,25774,25833,25892,25951,27149,27184,27413,27476,27531,27589,27647,27708,27771,27828,27879,27929,27990,28047,28113,28147,28182,28217,31053,31411,31483,31552,31621,31695,31767,31855,35459,35683,35884,36060,36444,36639,54715,54782,87850,88151,94609,95290,96291,96458"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\9262bdae479e1b658eb70c7bbd10b157\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "436", "startColumns": "4", "startOffsets": "28270", "endColumns": "42", "endOffsets": "28308"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,968,973,978,983,988,993,997,1001,1003,1007,1011,1016,1021,1026,1031,1036,1041,1043,1047,1050,1053,1056,1061,1063,1144,1146,1148,1150,1199,1204,1206,1209,1219,1221,1230,1231,1235,1245,1247,1253,1260,1271,1278,1279,1291,1304,1311,1327,1335,1352,1360,1369,1380,1387,1400,1408,1419,1420,1431,1432,1434,1444,1453,1457,1458,1468,1469,1476,1488,1496,1507,1517,1525,1536,1546,1552,1561,1568,1581,1596,1598,1609,1613,1631,1635,1639,1647,1655,1663,1670,1676,1680,1685,1690,1699,1709,1711,1714,1717,1720,1731,1738,1745,1747,1754,1760,1761,1763,1770,1776,55,231,266,409,425,478,491,514,520,528,541,559,574,584,604,611", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,221,280,349,413,480,537,608,664,724,781,840,904,964,1029,1099,1162,1225,1292,1366,1419,1461,1525,1594,1674,1745,1807,1876,1943,1999,2073,2146,2220,2293,2365,2439,2523,2593,2665,2745,2818,2893,2948,3015,3070,3137,3205,3267,3334,3403,3461,3509,37711,37766,37822,37883,37936,37996,38047,38110,38175,38238,38300,38361,38418,38486,38550,38619,38680,38741,38799,38856,38917,38999,39074,39144,39206,39261,39320,39411,39497,39555,39621,39677,39759,39823,39885,39941,39994,40049,40102,40179,40245,40316,40374,40434,40492,40558,40624,40678,40737,40800,40857,40915,40971,41039,41105,41164,41218,41271,41347,41419,41488,41561,41630,41703,41784,41863,41941,42011,42096,42178,42250,42351,42420,42486,42558,42632,42709,42783,42859,42933,42998,43063,43138,43211,43276,43339,43404,43488,43546,43607,43675,43745,43804,43862,43930,44000,44068,44120,44184,44246,44307,44365,44424,44482,44550,44620,44678,44746,44818,44888,44963,45065,45176,45275,45376,45441,45506,45570,45658,45725,45790,45863,45932,45997,46110,46211,46276,46343,46412,46481,46551,46622,46690,46755,46851,46956,47049,47133,47229,47308,47374,47425,47484,47544,47602,47669,47734,47794,47854,47917,47980,48045,48117,48194,48253,48315,48383,48451,48508,48567,48632,48704,48777,48850,48916,48980,49046,49113,49180,49265,49334,49400,49468,49534,49601,49671,49745,49822,49894,49968,50048,50117,50183,50249,50319,50384,50453,50517,50582,50654,50712,50771,50836,50916,50996,51074,51147,51219,51286,51356,51438,51516,51591,51663,51737,51805,51871,51938,52002,52070,52130,52198,52250,52313,52370,52433,52489,52553,52614,52672,52741,52805,52863,52922,52979,53048,53109,53164,53229,53295,53359,53413,53471,53526,53583,53638,53688,53744,53807,53870,53920,53971,54037,54113,54178,54250,54323,54395,54468,54553,54625,54689,54754,54812,54865,54917,54970,55023,55082,55133,55180,55228,55278,55332,55384,55434,55481,55535,55583,55627,55682,55730,55799,55871,55934,56005,56072,56142,56212,56282,56356,56427,56486,56561,56637,56707,56777,56862,56934,57000,57060,57121,57187,57254,57323,57394,57463,57533,57598,57660,57723,57800,57874,57950,58025,58070,58117,58194,58265,58333,58397,58486,58568,58634,58700,58801,58900,58975,59067,59166,59257,59346,59421,59482,59580,59639,59710,59783,59854,59917,59997,60073,60154,60233,60302,60379,60459,60545,60621,60703,60760,60814,60884,60989,61110,61170,61387,61662,61976,62293,62610,62886,63137,63368,63438,63686,63928,64219,64532,64786,65034,65284,65541,65648,65946,66158,66358,66538,66850,66947,72977,73055,73134,73215,77178,77518,77620,77807,78709,78793,79441,79506,79717,80429,80553,80905,81361,82185,82598,82644,83433,84150,84508,85409,85824,86768,87311,87848,88610,88994,89944,90464,91214,91278,91961,92024,92149,92832,93313,93525,93569,94187,94237,94655,95372,95795,96554,97159,97677,98258,98885,99202,99690,100089,101010,101938,102017,102593,102830,103950,104139,104376,104820,105294,105754,106174,106491,106717,107106,107390,107981,108535,108587,108771,108979,109116,109852,110275,110691,110744,111095,111452,111494,111553,111910,112255,3566,15670,18203,26294,27279,30232,30845,32264,32501,32857,33476,34421,35268,35838,36884,37349", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,967,972,977,982,987,992,996,1000,1002,1006,1010,1015,1020,1025,1030,1035,1040,1042,1046,1049,1052,1055,1060,1062,1143,1145,1147,1149,1198,1203,1205,1208,1218,1220,1229,1230,1234,1244,1246,1252,1259,1270,1277,1278,1290,1303,1310,1326,1334,1351,1359,1368,1379,1386,1399,1407,1418,1419,1430,1431,1433,1443,1452,1456,1457,1467,1468,1475,1487,1495,1506,1516,1524,1535,1545,1551,1560,1567,1580,1595,1597,1608,1612,1630,1634,1638,1646,1654,1662,1669,1675,1679,1684,1689,1698,1708,1710,1713,1716,1719,1730,1737,1744,1746,1753,1759,1760,1762,1769,1775,1776,230,265,408,424,477,490,513,519,527,540,558,573,583,603,610,618", "endColumns": "54,60,58,68,63,66,56,70,55,59,56,58,63,59,64,69,62,62,66,73,52,41,63,68,79,70,61,68,66,55,73,72,73,72,71,73,83,69,71,79,72,74,54,66,54,66,67,61,66,68,57,47,56,54,55,60,52,59,50,62,64,62,61,60,56,67,63,68,60,60,57,56,60,81,74,69,61,54,58,90,85,57,65,55,81,63,61,55,52,54,52,76,65,70,57,59,57,65,65,53,58,62,56,57,55,67,65,58,53,52,75,71,68,72,68,72,80,78,77,69,84,81,71,100,68,65,71,73,76,73,75,73,64,64,74,72,64,62,64,83,57,60,67,69,58,57,67,69,67,51,63,61,60,57,58,57,67,69,57,67,71,69,74,101,110,98,100,64,64,63,87,66,64,72,68,64,112,100,64,66,68,68,69,70,67,64,95,104,92,83,95,78,65,50,58,59,57,66,64,59,59,62,62,64,71,76,58,61,67,67,56,58,64,71,72,72,65,63,65,66,66,84,68,65,67,65,66,69,73,76,71,73,79,68,65,65,69,64,68,63,64,71,57,58,64,79,79,77,72,71,66,69,81,77,74,71,73,67,65,66,63,67,59,67,51,62,56,62,55,63,60,57,68,63,57,58,56,68,60,54,64,65,63,53,57,54,56,54,49,55,62,62,49,50,65,75,64,71,72,71,72,84,71,63,64,57,52,51,52,52,58,50,46,47,49,53,51,49,46,53,47,43,54,47,68,71,62,70,66,69,69,69,73,70,58,74,75,69,69,84,71,65,59,60,65,66,68,70,68,69,64,61,62,76,73,75,74,44,46,76,70,67,63,88,81,65,65,100,98,74,91,98,90,88,74,60,97,58,70,72,70,62,79,75,80,78,68,76,79,85,75,81,56,53,69,104,120,59,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,64,12,12,12,12,12,12,12,45,12,12,12,12,12,12,12,12,12,12,12,12,12,63,12,62,12,12,12,12,43,12,49,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,41,12,12,12,68,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,216,275,344,408,475,532,603,659,719,776,835,899,959,1024,1094,1157,1220,1287,1361,1414,1456,1520,1589,1669,1740,1802,1871,1938,1994,2068,2141,2215,2288,2360,2434,2518,2588,2660,2740,2813,2888,2943,3010,3065,3132,3200,3262,3329,3398,3456,3504,3561,37761,37817,37878,37931,37991,38042,38105,38170,38233,38295,38356,38413,38481,38545,38614,38675,38736,38794,38851,38912,38994,39069,39139,39201,39256,39315,39406,39492,39550,39616,39672,39754,39818,39880,39936,39989,40044,40097,40174,40240,40311,40369,40429,40487,40553,40619,40673,40732,40795,40852,40910,40966,41034,41100,41159,41213,41266,41342,41414,41483,41556,41625,41698,41779,41858,41936,42006,42091,42173,42245,42346,42415,42481,42553,42627,42704,42778,42854,42928,42993,43058,43133,43206,43271,43334,43399,43483,43541,43602,43670,43740,43799,43857,43925,43995,44063,44115,44179,44241,44302,44360,44419,44477,44545,44615,44673,44741,44813,44883,44958,45060,45171,45270,45371,45436,45501,45565,45653,45720,45785,45858,45927,45992,46105,46206,46271,46338,46407,46476,46546,46617,46685,46750,46846,46951,47044,47128,47224,47303,47369,47420,47479,47539,47597,47664,47729,47789,47849,47912,47975,48040,48112,48189,48248,48310,48378,48446,48503,48562,48627,48699,48772,48845,48911,48975,49041,49108,49175,49260,49329,49395,49463,49529,49596,49666,49740,49817,49889,49963,50043,50112,50178,50244,50314,50379,50448,50512,50577,50649,50707,50766,50831,50911,50991,51069,51142,51214,51281,51351,51433,51511,51586,51658,51732,51800,51866,51933,51997,52065,52125,52193,52245,52308,52365,52428,52484,52548,52609,52667,52736,52800,52858,52917,52974,53043,53104,53159,53224,53290,53354,53408,53466,53521,53578,53633,53683,53739,53802,53865,53915,53966,54032,54108,54173,54245,54318,54390,54463,54548,54620,54684,54749,54807,54860,54912,54965,55018,55077,55128,55175,55223,55273,55327,55379,55429,55476,55530,55578,55622,55677,55725,55794,55866,55929,56000,56067,56137,56207,56277,56351,56422,56481,56556,56632,56702,56772,56857,56929,56995,57055,57116,57182,57249,57318,57389,57458,57528,57593,57655,57718,57795,57869,57945,58020,58065,58112,58189,58260,58328,58392,58481,58563,58629,58695,58796,58895,58970,59062,59161,59252,59341,59416,59477,59575,59634,59705,59778,59849,59912,59992,60068,60149,60228,60297,60374,60454,60540,60616,60698,60755,60809,60879,60984,61105,61165,61382,61657,61971,62288,62605,62881,63132,63363,63433,63681,63923,64214,64527,64781,65029,65279,65536,65643,65941,66153,66353,66533,66845,66942,72972,73050,73129,73210,77173,77513,77615,77802,78704,78788,79436,79501,79712,80424,80548,80900,81356,82180,82593,82639,83428,84145,84503,85404,85819,86763,87306,87843,88605,88989,89939,90459,91209,91273,91956,92019,92144,92827,93308,93520,93564,94182,94232,94650,95367,95790,96549,97154,97672,98253,98880,99197,99685,100084,101005,101933,102012,102588,102825,103945,104134,104371,104815,105289,105749,106169,106486,106712,107101,107385,107976,108530,108582,108766,108974,109111,109847,110270,110686,110739,111090,111447,111489,111548,111905,112250,112319,15665,18198,26289,27274,30227,30840,32259,32496,32852,33471,34416,35263,35833,36879,37344,37706"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,339,340,341,342,349,350,351,352,353,354,355,356,357,358,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,435,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,533,557,561,566,571,576,581,586,590,594,596,600,604,609,614,619,624,629,634,636,640,643,646,649,654,656,737,739,741,743,790,795,797,800,810,812,824,825,829,839,841,847,854,865,872,873,885,898,905,921,929,946,954,963,974,981,994,1002,1013,1014,1025,1026,1028,1038,1047,1051,1052,1062,1063,1070,1082,1090,1101,1111,1119,1130,1140,1146,1155,1162,1175,1190,1192,1203,1207,1225,1229,1233,1241,1249,1257,1264,1270,1274,1279,1284,1293,1303,1305,1308,1311,1314,1325,1332,1339,1341,1348,1354,1355,1357,1364,1370,1582,1702,1726,1857,1894,1942,1953,1973,1979,1987,2000,2018,2033,2043,2063,2069", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "798,853,914,973,1042,1106,1173,1230,1301,1357,1417,1474,1533,1597,1657,1722,1792,1855,1918,1985,2059,2112,2154,2218,2287,2367,2438,2500,2569,2636,2692,2766,2839,2913,2986,3058,3132,3216,3286,3358,3438,3511,3586,3641,3708,3763,3830,3898,3960,4027,4096,4154,4202,5600,5655,5711,5772,5825,5885,5936,5999,6064,6127,6189,6250,6307,6375,6439,6508,6569,6630,6688,6745,6806,6888,6963,7033,7095,7150,7209,7300,7386,7444,7510,7566,7648,7712,7774,7830,7883,7938,7991,8068,8134,8205,8263,8323,8381,8447,8513,8567,8626,8689,8746,8804,8860,8928,8994,9053,9107,9160,9236,9308,9377,9450,9519,9592,9673,9752,9830,9900,9985,10067,10139,10240,10309,10375,10447,10521,10598,10672,10748,10822,10887,10952,11027,11100,11165,11228,11293,11377,11435,11496,11564,11634,11693,11751,11819,11889,11957,12009,12073,12135,12196,12254,12313,12371,12439,12509,12567,12635,12707,12777,12852,12954,13065,13164,13265,13330,13395,13459,13547,13614,13679,13752,13821,13886,13999,14100,14165,14232,14301,14370,14440,14511,14579,14644,14740,14845,14938,15022,15118,15197,15263,15314,15373,15433,15491,15558,15623,15683,15743,15806,15869,15934,16006,16083,16142,16204,16272,16340,16397,16456,16521,16593,16666,16739,16805,16869,16935,17002,17069,17154,17223,17289,17357,17423,17490,17560,17634,17711,17783,17857,17937,18006,18072,18138,18208,18273,18342,18406,18471,18543,18601,18660,18725,18805,18885,18963,19036,19108,19175,19245,19327,19405,19480,19552,19626,19694,19760,19827,19891,19959,20019,20087,20139,20202,20259,20322,20378,20442,20503,20561,20630,20694,20752,20811,20868,20937,20998,21053,21118,21184,21248,21302,21360,21415,21472,21527,21577,21633,22674,22737,22787,22838,23264,23340,23405,23477,23550,23622,23695,23780,23852,23916,26191,26249,26302,26354,26407,26460,26519,26570,26617,26665,26715,26769,26821,26871,26918,26972,27020,27064,28222,28601,28670,28742,28805,28876,28943,29013,29083,29153,29227,29298,29357,29432,29508,29578,29648,29733,29805,29871,29931,29992,30058,30125,30194,30265,30334,30404,30469,30531,30594,30671,30745,30821,30896,30941,32085,32162,32233,32301,32365,32454,32536,32602,32668,32769,32868,32943,33035,33134,33225,33314,33389,33450,33548,33607,33678,33751,33822,33885,33965,34041,34122,34201,34270,34347,34427,34513,34589,34671,34728,34782,34852,34957,35118,36711,36881,37156,37470,37787,38104,38380,38631,38862,38932,39180,39422,39713,40026,40280,40528,40778,41035,41142,41440,41652,41852,42032,42344,42441,48421,48499,48578,48659,52312,52652,52754,52941,53843,53927,54787,54852,55063,55775,55899,56251,56707,57531,57944,57990,58779,59496,59854,60755,61170,62114,62657,63194,63956,64340,65290,65810,66560,66624,67307,67370,67495,68178,68659,68871,68915,69533,69583,70001,70718,71141,71900,72505,72860,73441,74068,74385,74873,75272,76193,77121,77200,77776,78013,79133,79322,79559,80003,80477,80937,81357,81674,81900,82289,82573,83164,83718,83770,83954,84162,84299,85035,85458,85874,85927,86278,86635,86677,86736,87093,87438,96463,100562,101325,106298,107652,109890,110323,110946,111158,111424,111921,112866,113435,113798,114782,114979", "endLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,339,340,341,342,349,350,351,352,353,354,355,356,357,358,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,435,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,533,560,565,570,575,580,585,589,593,595,599,603,608,613,618,623,628,633,635,639,642,645,648,653,655,736,738,740,742,789,794,796,799,809,811,820,824,828,838,840,846,853,864,871,872,884,897,904,920,928,945,953,962,973,980,993,1001,1012,1013,1024,1025,1027,1037,1046,1050,1051,1061,1062,1069,1081,1089,1100,1110,1118,1129,1139,1145,1154,1161,1174,1189,1191,1202,1206,1224,1228,1232,1240,1248,1256,1263,1269,1273,1278,1283,1292,1302,1304,1307,1310,1313,1324,1331,1338,1340,1347,1353,1354,1356,1363,1369,1370,1701,1725,1856,1872,1941,1952,1972,1978,1986,1999,2017,2032,2042,2062,2068,2076", "endColumns": "54,60,58,68,63,66,56,70,55,59,56,58,63,59,64,69,62,62,66,73,52,41,63,68,79,70,61,68,66,55,73,72,73,72,71,73,83,69,71,79,72,74,54,66,54,66,67,61,66,68,57,47,56,54,55,60,52,59,50,62,64,62,61,60,56,67,63,68,60,60,57,56,60,81,74,69,61,54,58,90,85,57,65,55,81,63,61,55,52,54,52,76,65,70,57,59,57,65,65,53,58,62,56,57,55,67,65,58,53,52,75,71,68,72,68,72,80,78,77,69,84,81,71,100,68,65,71,73,76,73,75,73,64,64,74,72,64,62,64,83,57,60,67,69,58,57,67,69,67,51,63,61,60,57,58,57,67,69,57,67,71,69,74,101,110,98,100,64,64,63,87,66,64,72,68,64,112,100,64,66,68,68,69,70,67,64,95,104,92,83,95,78,65,50,58,59,57,66,64,59,59,62,62,64,71,76,58,61,67,67,56,58,64,71,72,72,65,63,65,66,66,84,68,65,67,65,66,69,73,76,71,73,79,68,65,65,69,64,68,63,64,71,57,58,64,79,79,77,72,71,66,69,81,77,74,71,73,67,65,66,63,67,59,67,51,62,56,62,55,63,60,57,68,63,57,58,56,68,60,54,64,65,63,53,57,54,56,54,49,55,62,62,49,50,65,75,64,71,72,71,72,84,71,63,64,57,52,51,52,52,58,50,46,47,49,53,51,49,46,53,47,43,54,47,68,71,62,70,66,69,69,69,73,70,58,74,75,69,69,84,71,65,59,60,65,66,68,70,68,69,64,61,62,76,73,75,74,44,46,76,70,67,63,88,81,65,65,100,98,74,91,98,90,88,74,60,97,58,70,72,70,62,79,75,80,78,68,76,79,85,75,81,56,53,69,104,120,59,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,64,12,12,12,12,12,12,12,45,12,12,12,12,12,12,12,12,12,12,12,12,12,63,12,62,12,12,12,12,43,12,49,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,41,12,12,12,68,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "848,909,968,1037,1101,1168,1225,1296,1352,1412,1469,1528,1592,1652,1717,1787,1850,1913,1980,2054,2107,2149,2213,2282,2362,2433,2495,2564,2631,2687,2761,2834,2908,2981,3053,3127,3211,3281,3353,3433,3506,3581,3636,3703,3758,3825,3893,3955,4022,4091,4149,4197,4254,5650,5706,5767,5820,5880,5931,5994,6059,6122,6184,6245,6302,6370,6434,6503,6564,6625,6683,6740,6801,6883,6958,7028,7090,7145,7204,7295,7381,7439,7505,7561,7643,7707,7769,7825,7878,7933,7986,8063,8129,8200,8258,8318,8376,8442,8508,8562,8621,8684,8741,8799,8855,8923,8989,9048,9102,9155,9231,9303,9372,9445,9514,9587,9668,9747,9825,9895,9980,10062,10134,10235,10304,10370,10442,10516,10593,10667,10743,10817,10882,10947,11022,11095,11160,11223,11288,11372,11430,11491,11559,11629,11688,11746,11814,11884,11952,12004,12068,12130,12191,12249,12308,12366,12434,12504,12562,12630,12702,12772,12847,12949,13060,13159,13260,13325,13390,13454,13542,13609,13674,13747,13816,13881,13994,14095,14160,14227,14296,14365,14435,14506,14574,14639,14735,14840,14933,15017,15113,15192,15258,15309,15368,15428,15486,15553,15618,15678,15738,15801,15864,15929,16001,16078,16137,16199,16267,16335,16392,16451,16516,16588,16661,16734,16800,16864,16930,16997,17064,17149,17218,17284,17352,17418,17485,17555,17629,17706,17778,17852,17932,18001,18067,18133,18203,18268,18337,18401,18466,18538,18596,18655,18720,18800,18880,18958,19031,19103,19170,19240,19322,19400,19475,19547,19621,19689,19755,19822,19886,19954,20014,20082,20134,20197,20254,20317,20373,20437,20498,20556,20625,20689,20747,20806,20863,20932,20993,21048,21113,21179,21243,21297,21355,21410,21467,21522,21572,21628,21691,22732,22782,22833,22899,23335,23400,23472,23545,23617,23690,23775,23847,23911,23976,26244,26297,26349,26402,26455,26514,26565,26612,26660,26710,26764,26816,26866,26913,26967,27015,27059,27114,28265,28665,28737,28800,28871,28938,29008,29078,29148,29222,29293,29352,29427,29503,29573,29643,29728,29800,29866,29926,29987,30053,30120,30189,30260,30329,30399,30464,30526,30589,30666,30740,30816,30891,30936,30983,32157,32228,32296,32360,32449,32531,32597,32663,32764,32863,32938,33030,33129,33220,33309,33384,33445,33543,33602,33673,33746,33817,33880,33960,34036,34117,34196,34265,34342,34422,34508,34584,34666,34723,34777,34847,34952,35073,35173,36876,37151,37465,37782,38099,38375,38626,38857,38927,39175,39417,39708,40021,40275,40523,40773,41030,41137,41435,41647,41847,42027,42339,42436,48416,48494,48573,48654,52307,52647,52749,52936,53838,53922,54570,54847,55058,55770,55894,56246,56702,57526,57939,57985,58774,59491,59849,60750,61165,62109,62652,63189,63951,64335,65285,65805,66555,66619,67302,67365,67490,68173,68654,68866,68910,69528,69578,69996,70713,71136,71895,72500,72855,73436,74063,74380,74868,75267,76188,77116,77195,77771,78008,79128,79317,79554,79998,80472,80932,81352,81669,81895,82284,82568,83159,83713,83765,83949,84157,84294,85030,85453,85869,85922,86273,86630,86672,86731,87088,87433,87502,100557,101320,106293,106807,109885,110318,110941,111153,111419,111916,112861,113430,113793,114777,114974,115243"}}, {"source": "E:\\aikaifa\\MovieTV\\MyApplication2\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2,1,6,3,4,5", "startColumns": "4,4,4,4,4,4", "startOffsets": "76,16,290,134,188,236", "endColumns": "57,59,52,53,47,53", "endOffsets": "129,71,338,183,231,285"}, "to": {"startLines": "7,8,11,12,70,72", "startColumns": "4,4,4,4,4,4", "startOffsets": "442,500,691,744,4551,4672", "endColumns": "57,59,52,53,47,53", "endOffsets": "495,555,739,793,4594,4721"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\09b2070107f58b91baaaa03824de0d80\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "439", "startColumns": "4", "startOffsets": "28427", "endColumns": "49", "endOffsets": "28472"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\ccfd53c838c812e96e4910096138f89c\\transformed\\fragment-1.6.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "392,418,440,1534,1539", "startColumns": "4,4,4,4,4", "startOffsets": "25956,27298,28477,95295,95465", "endLines": "392,418,440,1538,1542", "endColumns": "56,64,63,24,24", "endOffsets": "26008,27358,28536,95460,95609"}}, {"source": "E:\\aikaifa\\MovieTV\\MyApplication2\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,2,11,12,17,5,16,4,13,6,3,9,10,7,8", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16,71,563,609,790,241,721,193,652,299,135,470,517,363,421", "endColumns": "54,63,45,42,49,57,68,47,39,63,57,46,45,57,48", "endOffsets": "66,130,604,647,835,294,785,236,687,358,188,512,558,416,465"}, "to": {"startLines": "479,480,481,482,490,491,492,493,532,534,535,536,537,539,540", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "31141,31196,31260,31306,31860,31910,31968,32037,35078,35178,35242,35300,35347,35464,35522", "endColumns": "54,63,45,42,49,57,68,47,39,63,57,46,45,57,48", "endOffsets": "31191,31255,31301,31344,31905,31963,32032,32080,35113,35237,35295,35342,35388,35517,35566"}}, {"source": "E:\\aikaifa\\MovieTV\\MyApplication2\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "17", "endColumns": "73", "endOffsets": "86"}, "to": {"startLines": "821", "startColumns": "4", "startOffsets": "54575", "endColumns": "72", "endOffsets": "54643"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\d1cb9debee0ec7aff0e9116da1dc9922\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "68,69,71,343,344,345,346,441,546,548,549,554,556", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4391,4480,4599,22904,22957,23010,23063,28541,35889,36065,36187,36449,36644", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "4475,4546,4667,22952,23005,23058,23111,28596,35950,36182,36243,36510,36706"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-bn_values-bn.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,408,511,612,714,5331", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "199,301,403,506,607,709,829,5427"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,509,631,741,840,937,1062,1186,1294,1430,1577,1705,1832,1941,2034,2160,2250,2364,2482,2594,2692,2814,2937,3048,3158,3271,3388,3524,3669,3803,3946,4033,4116,4220,4355,4506", "endColumns": "106,100,99,95,121,109,98,96,124,123,107,135,146,127,126,108,92,125,89,113,117,111,97,121,122,110,109,112,116,135,144,133,142,86,82,103,134,150,95", "endOffsets": "207,308,408,504,626,736,835,932,1057,1181,1289,1425,1572,1700,1827,1936,2029,2155,2245,2359,2477,2589,2687,2809,2932,3043,3153,3266,3383,3519,3664,3798,3941,4028,4111,4215,4350,4501,4597"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "834,941,1042,1142,1238,1360,1470,1569,1666,1791,1915,2023,2159,2306,2434,2561,2670,2763,2889,2979,3093,3211,3323,3421,3543,3666,3777,3887,4000,4117,4253,4398,4532,4675,4762,4845,4949,5084,5235", "endColumns": "106,100,99,95,121,109,98,96,124,123,107,135,146,127,126,108,92,125,89,113,117,111,97,121,122,110,109,112,116,135,144,133,142,86,82,103,134,150,95", "endOffsets": "936,1037,1137,1233,1355,1465,1564,1661,1786,1910,2018,2154,2301,2429,2556,2665,2758,2884,2974,3088,3206,3318,3416,3538,3661,3772,3882,3995,4112,4248,4393,4527,4670,4757,4840,4944,5079,5230,5326"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-sw_values-sw.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,499,606,713,5190", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "194,296,393,494,601,708,823,5286"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,504,643,757,856,945,1063,1181,1293,1414,1557,1675,1793,1899,1992,2124,2214,2315,2422,2523,2623,2740,2856,2982,3108,3221,3341,3462,3584,3696,3809,3896,3980,4082,4217,4370", "endColumns": "106,100,96,93,138,113,98,88,117,117,111,120,142,117,117,105,92,131,89,100,106,100,99,116,115,125,125,112,119,120,121,111,112,86,83,101,134,152,96", "endOffsets": "207,308,405,499,638,752,851,940,1058,1176,1288,1409,1552,1670,1788,1894,1987,2119,2209,2310,2417,2518,2618,2735,2851,2977,3103,3216,3336,3457,3579,3691,3804,3891,3975,4077,4212,4365,4462"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "828,935,1036,1133,1227,1366,1480,1579,1668,1786,1904,2016,2137,2280,2398,2516,2622,2715,2847,2937,3038,3145,3246,3346,3463,3579,3705,3831,3944,4064,4185,4307,4419,4532,4619,4703,4805,4940,5093", "endColumns": "106,100,96,93,138,113,98,88,117,117,111,120,142,117,117,105,92,131,89,100,106,100,99,116,115,125,125,112,119,120,121,111,112,86,83,101,134,152,96", "endOffsets": "930,1031,1128,1222,1361,1475,1574,1663,1781,1899,2011,2132,2275,2393,2511,2617,2710,2842,2932,3033,3140,3241,3341,3458,3574,3700,3826,3939,4059,4180,4302,4414,4527,4614,4698,4800,4935,5088,5185"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-bs_values-bs.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,504,626,741,838,930,1052,1172,1271,1386,1536,1661,1784,1888,1979,2108,2205,2306,2410,2513,2619,2731,2848,2975,3100,3207,3319,3434,3557,3670,3791,3878,3966,4085,4222,4392", "endColumns": "106,100,96,93,121,114,96,91,121,119,98,114,149,124,122,103,90,128,96,100,103,102,105,111,116,126,124,106,111,114,122,112,120,86,87,118,136,169,89", "endOffsets": "207,308,405,499,621,736,833,925,1047,1167,1266,1381,1531,1656,1779,1883,1974,2103,2200,2301,2405,2508,2614,2726,2843,2970,3095,3202,3314,3429,3552,3665,3786,3873,3961,4080,4217,4387,4477"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "830,937,1038,1135,1229,1351,1466,1563,1655,1777,1897,1996,2111,2261,2386,2509,2613,2704,2833,2930,3031,3135,3238,3344,3456,3573,3700,3825,3932,4044,4159,4282,4395,4516,4603,4691,4810,4947,5117", "endColumns": "106,100,96,93,121,114,96,91,121,119,98,114,149,124,122,103,90,128,96,100,103,102,105,111,116,126,124,106,111,114,122,112,120,86,87,118,136,169,89", "endOffsets": "932,1033,1130,1224,1346,1461,1558,1650,1772,1892,1991,2106,2256,2381,2504,2608,2699,2828,2925,3026,3130,3233,3339,3451,3568,3695,3820,3927,4039,4154,4277,4390,4511,4598,4686,4805,4942,5112,5202"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,403,507,611,713,5207", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "198,300,398,502,606,708,825,5303"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ka_values-ka.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,501,607,711,5254", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "196,298,397,496,602,706,824,5350"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,510,636,749,848,940,1074,1205,1311,1442,1594,1719,1841,1954,2045,2177,2268,2375,2485,2591,2692,2805,2928,3048,3165,3274,3384,3508,3630,3752,3872,3959,4042,4146,4283,4437", "endColumns": "106,100,99,96,125,112,98,91,133,130,105,130,151,124,121,112,90,131,90,106,109,105,100,112,122,119,116,108,109,123,121,121,119,86,82,103,136,153,92", "endOffsets": "207,308,408,505,631,744,843,935,1069,1200,1306,1437,1589,1714,1836,1949,2040,2172,2263,2370,2480,2586,2687,2800,2923,3043,3160,3269,3379,3503,3625,3747,3867,3954,4037,4141,4278,4432,4525"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "829,936,1037,1137,1234,1360,1473,1572,1664,1798,1929,2035,2166,2318,2443,2565,2678,2769,2901,2992,3099,3209,3315,3416,3529,3652,3772,3889,3998,4108,4232,4354,4476,4596,4683,4766,4870,5007,5161", "endColumns": "106,100,99,96,125,112,98,91,133,130,105,130,151,124,121,112,90,131,90,106,109,105,100,112,122,119,116,108,109,123,121,121,119,86,82,103,136,153,92", "endOffsets": "931,1032,1132,1229,1355,1468,1567,1659,1793,1924,2030,2161,2313,2438,2560,2673,2764,2896,2987,3094,3204,3310,3411,3524,3647,3767,3884,3993,4103,4227,4349,4471,4591,4678,4761,4865,5002,5156,5249"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-sr_values-sr.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,504,629,744,840,934,1057,1177,1286,1411,1567,1692,1814,1916,2010,2134,2224,2325,2436,2539,2641,2760,2877,3000,3120,3229,3344,3465,3588,3704,3822,3909,3997,4114,4253,4419", "endColumns": "106,100,96,93,124,114,95,93,122,119,108,124,155,124,121,101,93,123,89,100,110,102,101,118,116,122,119,108,114,120,122,115,117,86,87,116,138,165,90", "endOffsets": "207,308,405,499,624,739,835,929,1052,1172,1281,1406,1562,1687,1809,1911,2005,2129,2219,2320,2431,2534,2636,2755,2872,2995,3115,3224,3339,3460,3583,3699,3817,3904,3992,4109,4248,4414,4505"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,938,1039,1136,1230,1355,1470,1566,1660,1783,1903,2012,2137,2293,2418,2540,2642,2736,2860,2950,3051,3162,3265,3367,3486,3603,3726,3846,3955,4070,4191,4314,4430,4548,4635,4723,4840,4979,5145", "endColumns": "106,100,96,93,124,114,95,93,122,119,108,124,155,124,121,101,93,123,89,100,110,102,101,118,116,122,119,108,114,120,122,115,117,86,87,116,138,165,90", "endOffsets": "933,1034,1131,1225,1350,1465,1561,1655,1778,1898,2007,2132,2288,2413,2535,2637,2731,2855,2945,3046,3157,3260,3362,3481,3598,3721,3841,3950,4065,4186,4309,4425,4543,4630,4718,4835,4974,5140,5231"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,402,506,610,715,5236", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "198,300,397,501,605,710,826,5332"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ky_values-ky.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,503,635,747,846,937,1068,1199,1304,1425,1590,1713,1836,1944,2035,2164,2253,2359,2470,2577,2675,2790,2908,3024,3140,3255,3374,3494,3611,3725,3840,3927,4010,4114,4248,4403", "endColumns": "106,100,96,92,131,111,98,90,130,130,104,120,164,122,122,107,90,128,88,105,110,106,97,114,117,115,115,114,118,119,116,113,114,86,82,103,133,154,89", "endOffsets": "207,308,405,498,630,742,841,932,1063,1194,1299,1420,1585,1708,1831,1939,2030,2159,2248,2354,2465,2572,2670,2785,2903,3019,3135,3250,3369,3489,3606,3720,3835,3922,4005,4109,4243,4398,4488"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,943,1044,1141,1234,1366,1478,1577,1668,1799,1930,2035,2156,2321,2444,2567,2675,2766,2895,2984,3090,3201,3308,3406,3521,3639,3755,3871,3986,4105,4225,4342,4456,4571,4658,4741,4845,4979,5134", "endColumns": "106,100,96,92,131,111,98,90,130,130,104,120,164,122,122,107,90,128,88,105,110,106,97,114,117,115,115,114,118,119,116,113,114,86,82,103,133,154,89", "endOffsets": "938,1039,1136,1229,1361,1473,1572,1663,1794,1925,2030,2151,2316,2439,2562,2670,2761,2890,2979,3085,3196,3303,3401,3516,3634,3750,3866,3981,4100,4220,4337,4451,4566,4653,4736,4840,4974,5129,5219"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,307,410,517,621,725,5224", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "200,302,405,512,616,720,831,5320"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-da_values-da.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,504,622,734,831,931,1057,1182,1284,1402,1555,1676,1796,1905,2003,2133,2224,2326,2430,2530,2629,2745,2866,2975,3083,3189,3301,3416,3536,3648,3765,3852,3933,4033,4171,4328", "endColumns": "106,100,96,93,117,111,96,99,125,124,101,117,152,120,119,108,97,129,90,101,103,99,98,115,120,108,107,105,111,114,119,111,116,86,80,99,137,156,88", "endOffsets": "207,308,405,499,617,729,826,926,1052,1177,1279,1397,1550,1671,1791,1900,1998,2128,2219,2321,2425,2525,2624,2740,2861,2970,3078,3184,3296,3411,3531,3643,3760,3847,3928,4028,4166,4323,4412"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,939,1040,1137,1231,1349,1461,1558,1658,1784,1909,2011,2129,2282,2403,2523,2632,2730,2860,2951,3053,3157,3257,3356,3472,3593,3702,3810,3916,4028,4143,4263,4375,4492,4579,4660,4760,4898,5055", "endColumns": "106,100,96,93,117,111,96,99,125,124,101,117,152,120,119,108,97,129,90,101,103,99,98,115,120,108,107,105,111,114,119,111,116,86,80,99,137,156,88", "endOffsets": "934,1035,1132,1226,1344,1456,1553,1653,1779,1904,2006,2124,2277,2398,2518,2627,2725,2855,2946,3048,3152,3252,3351,3467,3588,3697,3805,3911,4023,4138,4258,4370,4487,4574,4655,4755,4893,5050,5139"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,400,498,605,714,5144", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "196,298,395,493,600,709,827,5240"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-en-rCA_values-en-rCA.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,505,624,736,832,928,1059,1188,1293,1414,1542,1663,1782,1887,1978,2106,2195,2296,2399,2500,2593,2703,2809,2920,3029,3128,3235,3345,3461,3567,3679,3766,3850,3950,4085,4236", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "207,308,406,500,619,731,827,923,1054,1183,1288,1409,1537,1658,1777,1882,1973,2101,2190,2291,2394,2495,2588,2698,2804,2915,3024,3123,3230,3340,3456,3562,3674,3761,3845,3945,4080,4231,4321"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "823,930,1031,1129,1223,1342,1454,1550,1646,1777,1906,2011,2132,2260,2381,2500,2605,2696,2824,2913,3014,3117,3218,3311,3421,3527,3638,3747,3846,3953,4063,4179,4285,4397,4484,4568,4668,4803,4954", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "925,1026,1124,1218,1337,1449,1545,1641,1772,1901,2006,2127,2255,2376,2495,2600,2691,2819,2908,3009,3112,3213,3306,3416,3522,3633,3742,3841,3948,4058,4174,4280,4392,4479,4563,4663,4798,4949,5039"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,501,605,707,5044", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "196,298,397,496,600,702,818,5140"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ca_values-ca.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,499,605,710,5274", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "196,298,397,494,600,705,831,5370"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,508,633,747,846,938,1067,1192,1302,1428,1593,1720,1843,1947,2046,2182,2277,2383,2495,2605,2700,2812,2932,3062,3188,3294,3407,3528,3654,3770,3891,3978,4061,4162,4298,4452", "endColumns": "106,100,97,96,124,113,98,91,128,124,109,125,164,126,122,103,98,135,94,105,111,109,94,111,119,129,125,105,112,120,125,115,120,86,82,100,135,153,90", "endOffsets": "207,308,406,503,628,742,841,933,1062,1187,1297,1423,1588,1715,1838,1942,2041,2177,2272,2378,2490,2600,2695,2807,2927,3057,3183,3289,3402,3523,3649,3765,3886,3973,4056,4157,4293,4447,4538"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,943,1044,1142,1239,1364,1478,1577,1669,1798,1923,2033,2159,2324,2451,2574,2678,2777,2913,3008,3114,3226,3336,3431,3543,3663,3793,3919,4025,4138,4259,4385,4501,4622,4709,4792,4893,5029,5183", "endColumns": "106,100,97,96,124,113,98,91,128,124,109,125,164,126,122,103,98,135,94,105,111,109,94,111,119,129,125,105,112,120,125,115,120,86,82,100,135,153,90", "endOffsets": "938,1039,1137,1234,1359,1473,1572,1664,1793,1918,2028,2154,2319,2446,2569,2673,2772,2908,3003,3109,3221,3331,3426,3538,3658,3788,3914,4020,4133,4254,4380,4496,4617,4704,4787,4888,5024,5178,5269"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-pt_values-pt.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,403,503,610,720,5201", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "197,299,398,498,605,715,835,5297"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,634,748,847,948,1081,1210,1310,1426,1592,1717,1838,1941,2033,2165,2260,2363,2466,2572,2669,2783,2911,3032,3149,3252,3360,3471,3587,3692,3802,3889,3976,4080,4218,4373", "endColumns": "106,100,98,95,125,113,98,100,132,128,99,115,165,124,120,102,91,131,94,102,102,105,96,113,127,120,116,102,107,110,115,104,109,86,86,103,137,154,92", "endOffsets": "207,308,407,503,629,743,842,943,1076,1205,1305,1421,1587,1712,1833,1936,2028,2160,2255,2358,2461,2567,2664,2778,2906,3027,3144,3247,3355,3466,3582,3687,3797,3884,3971,4075,4213,4368,4461"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,947,1048,1147,1243,1369,1483,1582,1683,1816,1945,2045,2161,2327,2452,2573,2676,2768,2900,2995,3098,3201,3307,3404,3518,3646,3767,3884,3987,4095,4206,4322,4427,4537,4624,4711,4815,4953,5108", "endColumns": "106,100,98,95,125,113,98,100,132,128,99,115,165,124,120,102,91,131,94,102,102,105,96,113,127,120,116,102,107,110,115,104,109,86,86,103,137,154,92", "endOffsets": "942,1043,1142,1238,1364,1478,1577,1678,1811,1940,2040,2156,2322,2447,2568,2671,2763,2895,2990,3093,3196,3302,3399,3513,3641,3762,3879,3982,4090,4201,4317,4422,4532,4619,4706,4810,4948,5103,5196"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-in_values-in.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,507,627,737,839,929,1065,1197,1294,1407,1554,1682,1806,1915,2005,2133,2223,2326,2431,2530,2623,2733,2844,2956,3064,3174,3288,3409,3532,3645,3760,3847,3933,4042,4179,4339", "endColumns": "106,100,98,94,119,109,101,89,135,131,96,112,146,127,123,108,89,127,89,102,104,98,92,109,110,111,107,109,113,120,122,112,114,86,85,108,136,159,96", "endOffsets": "207,308,407,502,622,732,834,924,1060,1192,1289,1402,1549,1677,1801,1910,2000,2128,2218,2321,2426,2525,2618,2728,2839,2951,3059,3169,3283,3404,3527,3640,3755,3842,3928,4037,4174,4334,4431"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "835,942,1043,1142,1237,1357,1467,1569,1659,1795,1927,2024,2137,2284,2412,2536,2645,2735,2863,2953,3056,3161,3260,3353,3463,3574,3686,3794,3904,4018,4139,4262,4375,4490,4577,4663,4772,4909,5069", "endColumns": "106,100,98,94,119,109,101,89,135,131,96,112,146,127,123,108,89,127,89,102,104,98,92,109,110,111,107,109,113,120,122,112,114,86,85,108,136,159,96", "endOffsets": "937,1038,1137,1232,1352,1462,1564,1654,1790,1922,2019,2132,2279,2407,2531,2640,2730,2858,2948,3051,3156,3255,3348,3458,3569,3681,3789,3899,4013,4134,4257,4370,4485,4572,4658,4767,4904,5064,5161"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,399,496,602,720,5166", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "195,297,394,491,597,715,830,5262"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-it_values-it.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,503,628,744,842,933,1060,1183,1289,1412,1563,1686,1805,1910,2010,2146,2240,2343,2453,2554,2650,2764,2885,3014,3139,3258,3381,3496,3617,3726,3841,3928,4011,4113,4250,4406", "endColumns": "106,100,97,91,124,115,97,90,126,122,105,122,150,122,118,104,99,135,93,102,109,100,95,113,120,128,124,118,122,114,120,108,114,86,82,101,136,155,93", "endOffsets": "207,308,406,498,623,739,837,928,1055,1178,1284,1407,1558,1681,1800,1905,2005,2141,2235,2338,2448,2549,2645,2759,2880,3009,3134,3253,3376,3491,3612,3721,3836,3923,4006,4108,4245,4401,4495"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "852,959,1060,1158,1250,1375,1491,1589,1680,1807,1930,2036,2159,2310,2433,2552,2657,2757,2893,2987,3090,3200,3301,3397,3511,3632,3761,3886,4005,4128,4243,4364,4473,4588,4675,4758,4860,4997,5153", "endColumns": "106,100,97,91,124,115,97,90,126,122,105,122,150,122,118,104,99,135,93,102,109,100,95,113,120,128,124,118,122,114,120,108,114,86,82,101,136,155,93", "endOffsets": "954,1055,1153,1245,1370,1486,1584,1675,1802,1925,2031,2154,2305,2428,2547,2652,2752,2888,2982,3085,3195,3296,3392,3506,3627,3756,3881,4000,4123,4238,4359,4468,4583,4670,4753,4855,4992,5148,5242"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,404,506,615,722,5247", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "198,300,399,501,610,717,847,5343"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ja_values-ja.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,405,495,609,718,812,902,1026,1149,1245,1357,1487,1597,1706,1805,1895,2015,2102,2200,2298,2397,2488,2596,2702,2809,2915,3013,3115,3215,3320,3418,3521,3608,3688,3777,3909,4050", "endColumns": "106,100,91,89,113,108,93,89,123,122,95,111,129,109,108,98,89,119,86,97,97,98,90,107,105,106,105,97,101,99,104,97,102,86,79,88,131,140,80", "endOffsets": "207,308,400,490,604,713,807,897,1021,1144,1240,1352,1482,1592,1701,1800,1890,2010,2097,2195,2293,2392,2483,2591,2697,2804,2910,3008,3110,3210,3315,3413,3516,3603,3683,3772,3904,4045,4126"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "774,881,982,1074,1164,1278,1387,1481,1571,1695,1818,1914,2026,2156,2266,2375,2474,2564,2684,2771,2869,2967,3066,3157,3265,3371,3478,3584,3682,3784,3884,3989,4087,4190,4277,4357,4446,4578,4719", "endColumns": "106,100,91,89,113,108,93,89,123,122,95,111,129,109,108,98,89,119,86,97,97,98,90,107,105,106,105,97,101,99,104,97,102,86,79,88,131,140,80", "endOffsets": "876,977,1069,1159,1273,1382,1476,1566,1690,1813,1909,2021,2151,2261,2370,2469,2559,2679,2766,2864,2962,3061,3152,3260,3366,3473,3579,3677,3779,3879,3984,4082,4185,4272,4352,4441,4573,4714,4795"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,297,391,487,580,673,4800", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "192,292,386,482,575,668,769,4896"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-sk_values-sk.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,404,502,612,720,5292", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "196,298,399,497,607,715,837,5388"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,509,627,740,837,930,1057,1183,1292,1417,1578,1711,1843,1948,2044,2174,2266,2372,2474,2587,2687,2804,2924,3046,3167,3281,3407,3517,3635,3741,3855,3942,4026,4142,4285,4460", "endColumns": "106,100,99,95,117,112,96,92,126,125,108,124,160,132,131,104,95,129,91,105,101,112,99,116,119,121,120,113,125,109,117,105,113,86,83,115,142,174,94", "endOffsets": "207,308,408,504,622,735,832,925,1052,1178,1287,1412,1573,1706,1838,1943,2039,2169,2261,2367,2469,2582,2682,2799,2919,3041,3162,3276,3402,3512,3630,3736,3850,3937,4021,4137,4280,4455,4550"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,949,1050,1150,1246,1364,1477,1574,1667,1794,1920,2029,2154,2315,2448,2580,2685,2781,2911,3003,3109,3211,3324,3424,3541,3661,3783,3904,4018,4144,4254,4372,4478,4592,4679,4763,4879,5022,5197", "endColumns": "106,100,99,95,117,112,96,92,126,125,108,124,160,132,131,104,95,129,91,105,101,112,99,116,119,121,120,113,125,109,117,105,113,86,83,115,142,174,94", "endOffsets": "944,1045,1145,1241,1359,1472,1569,1662,1789,1915,2024,2149,2310,2443,2575,2680,2776,2906,2998,3104,3206,3319,3419,3536,3656,3778,3899,4013,4139,4249,4367,4473,4587,4674,4758,4874,5017,5192,5287"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-es-rUS_values-es-rUS.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,509,636,751,852,945,1084,1220,1322,1440,1622,1747,1869,1974,2066,2204,2299,2402,2504,2606,2703,2817,2952,3082,3209,3314,3422,3544,3666,3783,3900,3987,4071,4173,4307,4460", "endColumns": "106,100,98,96,126,114,100,92,138,135,101,117,181,124,121,104,91,137,94,102,101,101,96,113,134,129,126,104,107,121,121,116,116,86,83,101,133,152,94", "endOffsets": "207,308,407,504,631,746,847,940,1079,1215,1317,1435,1617,1742,1864,1969,2061,2199,2294,2397,2499,2601,2698,2812,2947,3077,3204,3309,3417,3539,3661,3778,3895,3982,4066,4168,4302,4455,4550"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,944,1045,1144,1241,1368,1483,1584,1677,1816,1952,2054,2172,2354,2479,2601,2706,2798,2936,3031,3134,3236,3338,3435,3549,3684,3814,3941,4046,4154,4276,4398,4515,4632,4719,4803,4905,5039,5192", "endColumns": "106,100,98,96,126,114,100,92,138,135,101,117,181,124,121,104,91,137,94,102,101,101,96,113,134,129,126,104,107,121,121,116,116,86,83,101,133,152,94", "endOffsets": "939,1040,1139,1236,1363,1478,1579,1672,1811,1947,2049,2167,2349,2474,2596,2701,2793,2931,3026,3129,3231,3333,3430,3544,3679,3809,3936,4041,4149,4271,4393,4510,4627,4714,4798,4900,5034,5187,5282"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,406,504,611,717,5287", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "199,301,401,499,606,712,832,5383"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-lo_values-lo.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,502,627,733,830,924,1052,1180,1284,1404,1539,1654,1769,1877,1973,2093,2183,2288,2391,2498,2598,2715,2824,2937,3050,3152,3259,3366,3483,3590,3702,3789,3872,3972,4106,4257", "endColumns": "106,100,95,92,124,105,96,93,127,127,103,119,134,114,114,107,95,119,89,104,102,106,99,116,108,112,112,101,106,106,116,106,111,86,82,99,133,150,87", "endOffsets": "207,308,404,497,622,728,825,919,1047,1175,1279,1399,1534,1649,1764,1872,1968,2088,2178,2283,2386,2493,2593,2710,2819,2932,3045,3147,3254,3361,3478,3585,3697,3784,3867,3967,4101,4252,4340"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "811,918,1019,1115,1208,1333,1439,1536,1630,1758,1886,1990,2110,2245,2360,2475,2583,2679,2799,2889,2994,3097,3204,3304,3421,3530,3643,3756,3858,3965,4072,4189,4296,4408,4495,4578,4678,4812,4963", "endColumns": "106,100,95,92,124,105,96,93,127,127,103,119,134,114,114,107,95,119,89,104,102,106,99,116,108,112,112,101,106,106,116,106,111,86,82,99,133,150,87", "endOffsets": "913,1014,1110,1203,1328,1434,1531,1625,1753,1881,1985,2105,2240,2355,2470,2578,2674,2794,2884,2989,3092,3199,3299,3416,3525,3638,3751,3853,3960,4067,4184,4291,4403,4490,4573,4673,4807,4958,5046"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,304,403,501,602,700,5051", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "196,299,398,496,597,695,806,5147"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-zh-rHK_values-zh-rHK.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,296,390,484,577,670,4677", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "192,291,385,479,572,665,761,4773"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,405,495,608,709,803,892,1005,1117,1214,1325,1428,1536,1643,1740,1828,1936,2023,2122,2219,2318,2407,2513,2607,2709,2810,2907,3008,3105,3208,3302,3402,3489,3569,3660,3792,3935", "endColumns": "106,100,91,89,112,100,93,88,112,111,96,110,102,107,106,96,87,107,86,98,96,98,88,105,93,101,100,96,100,96,102,93,99,86,79,90,131,142,80", "endOffsets": "207,308,400,490,603,704,798,887,1000,1112,1209,1320,1423,1531,1638,1735,1823,1931,2018,2117,2214,2313,2402,2508,2602,2704,2805,2902,3003,3100,3203,3297,3397,3484,3564,3655,3787,3930,4011"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,873,974,1066,1156,1269,1370,1464,1553,1666,1778,1875,1986,2089,2197,2304,2401,2489,2597,2684,2783,2880,2979,3068,3174,3268,3370,3471,3568,3669,3766,3869,3963,4063,4150,4230,4321,4453,4596", "endColumns": "106,100,91,89,112,100,93,88,112,111,96,110,102,107,106,96,87,107,86,98,96,98,88,105,93,101,100,96,100,96,102,93,99,86,79,90,131,142,80", "endOffsets": "868,969,1061,1151,1264,1365,1459,1548,1661,1773,1870,1981,2084,2192,2299,2396,2484,2592,2679,2778,2875,2974,3063,3169,3263,3365,3466,3563,3664,3761,3864,3958,4058,4145,4225,4316,4448,4591,4672"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-sl_values-sl.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,423,519,645,760,858,950,1075,1197,1303,1443,1598,1724,1847,1950,2050,2178,2272,2373,2481,2582,2681,2809,2933,3060,3184,3297,3413,3528,3643,3756,3869,3956,4041,4154,4291,4460", "endColumns": "112,106,97,95,125,114,97,91,124,121,105,139,154,125,122,102,99,127,93,100,107,100,98,127,123,126,123,112,115,114,114,112,112,86,84,112,136,168,91", "endOffsets": "213,320,418,514,640,755,853,945,1070,1192,1298,1438,1593,1719,1842,1945,2045,2173,2267,2368,2476,2577,2676,2804,2928,3055,3179,3292,3408,3523,3638,3751,3864,3951,4036,4149,4286,4455,4547"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "828,941,1048,1146,1242,1368,1483,1581,1673,1798,1920,2026,2166,2321,2447,2570,2673,2773,2901,2995,3096,3204,3305,3404,3532,3656,3783,3907,4020,4136,4251,4366,4479,4592,4679,4764,4877,5014,5183", "endColumns": "112,106,97,95,125,114,97,91,124,121,105,139,154,125,122,102,99,127,93,100,107,100,98,127,123,126,123,112,115,114,114,112,112,86,84,112,136,168,91", "endOffsets": "936,1043,1141,1237,1363,1478,1576,1668,1793,1915,2021,2161,2316,2442,2565,2668,2768,2896,2990,3091,3199,3300,3399,3527,3651,3778,3902,4015,4131,4246,4361,4474,4587,4674,4759,4872,5009,5178,5270"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,402,506,609,711,5275", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "197,299,397,501,604,706,823,5371"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-zh-rCN_values-zh-rCN.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,298,392,486,579,673,4672", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "192,293,387,481,574,668,764,4768"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,317,409,499,612,713,807,896,1006,1115,1210,1322,1424,1532,1639,1736,1824,1930,2017,2114,2211,2308,2397,2504,2597,2699,2800,2895,2994,3092,3196,3292,3394,3481,3561,3654,3784,3927", "endColumns": "108,102,91,89,112,100,93,88,109,108,94,111,101,107,106,96,87,105,86,96,96,96,88,106,92,101,100,94,98,97,103,95,101,86,79,92,129,142,80", "endOffsets": "209,312,404,494,607,708,802,891,1001,1110,1205,1317,1419,1527,1634,1731,1819,1925,2012,2109,2206,2303,2392,2499,2592,2694,2795,2890,2989,3087,3191,3287,3389,3476,3556,3649,3779,3922,4003"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "769,878,981,1073,1163,1276,1377,1471,1560,1670,1779,1874,1986,2088,2196,2303,2400,2488,2594,2681,2778,2875,2972,3061,3168,3261,3363,3464,3559,3658,3756,3860,3956,4058,4145,4225,4318,4448,4591", "endColumns": "108,102,91,89,112,100,93,88,109,108,94,111,101,107,106,96,87,105,86,96,96,96,88,106,92,101,100,94,98,97,103,95,101,86,79,92,129,142,80", "endOffsets": "873,976,1068,1158,1271,1372,1466,1555,1665,1774,1869,1981,2083,2191,2298,2395,2483,2589,2676,2773,2870,2967,3056,3163,3256,3358,3459,3554,3653,3751,3855,3951,4053,4140,4220,4313,4443,4586,4667"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-uz_values-uz.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,506,632,748,849,942,1074,1199,1309,1444,1600,1730,1851,1958,2049,2184,2273,2384,2490,2601,2704,2833,2946,3058,3186,3294,3407,3532,3652,3775,3893,3980,4065,4173,4318,4485", "endColumns": "106,100,98,93,125,115,100,92,131,124,109,134,155,129,120,106,90,134,88,110,105,110,102,128,112,111,127,107,112,124,119,122,117,86,84,107,144,166,89", "endOffsets": "207,308,407,501,627,743,844,937,1069,1194,1304,1439,1595,1725,1846,1953,2044,2179,2268,2379,2485,2596,2699,2828,2941,3053,3181,3289,3402,3527,3647,3770,3888,3975,4060,4168,4313,4480,4570"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "841,948,1049,1148,1242,1368,1484,1585,1678,1810,1935,2045,2180,2336,2466,2587,2694,2785,2920,3009,3120,3226,3337,3440,3569,3682,3794,3922,4030,4143,4268,4388,4511,4629,4716,4801,4909,5054,5221", "endColumns": "106,100,98,93,125,115,100,92,131,124,109,134,155,129,120,106,90,134,88,110,105,110,102,128,112,111,127,107,112,124,119,122,117,86,84,107,144,166,89", "endOffsets": "943,1044,1143,1237,1363,1479,1580,1673,1805,1930,2040,2175,2331,2461,2582,2689,2780,2915,3004,3115,3221,3332,3435,3564,3677,3789,3917,4025,4138,4263,4383,4506,4624,4711,4796,4904,5049,5216,5306"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,309,410,510,618,722,5311", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "202,304,405,505,613,717,836,5407"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-es_values-es.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,509,636,751,852,944,1072,1197,1309,1437,1586,1711,1833,1938,2030,2158,2253,2356,2458,2560,2656,2769,2885,3018,3148,3254,3363,3480,3602,3714,3831,3918,4012,4114,4248,4401", "endColumns": "106,100,98,96,126,114,100,91,127,124,111,127,148,124,121,104,91,127,94,102,101,101,95,112,115,132,129,105,108,116,121,111,116,86,93,101,133,152,89", "endOffsets": "207,308,407,504,631,746,847,939,1067,1192,1304,1432,1581,1706,1828,1933,2025,2153,2248,2351,2453,2555,2651,2764,2880,3013,3143,3249,3358,3475,3597,3709,3826,3913,4007,4109,4243,4396,4486"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,944,1045,1144,1241,1368,1483,1584,1676,1804,1929,2041,2169,2318,2443,2565,2670,2762,2890,2985,3088,3190,3292,3388,3501,3617,3750,3880,3986,4095,4212,4334,4446,4563,4650,4744,4846,4980,5133", "endColumns": "106,100,98,96,126,114,100,91,127,124,111,127,148,124,121,104,91,127,94,102,101,101,95,112,115,132,129,105,108,116,121,111,116,86,93,101,133,152,89", "endOffsets": "939,1040,1139,1236,1363,1478,1579,1671,1799,1924,2036,2164,2313,2438,2560,2665,2757,2885,2980,3083,3185,3287,3383,3496,3612,3745,3875,3981,4090,4207,4329,4441,4558,4645,4739,4841,4975,5128,5218"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,406,504,611,717,5223", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "199,301,401,499,606,712,832,5319"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-fi_values-fi.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,401,506,611,723,5205", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "196,298,396,501,606,718,834,5301"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,502,630,744,844,935,1066,1193,1301,1425,1570,1699,1824,1933,2027,2153,2244,2358,2469,2581,2684,2804,2913,3040,3163,3270,3381,3497,3613,3724,3836,3923,4005,4103,4237,4382", "endColumns": "106,100,94,93,127,113,99,90,130,126,107,123,144,128,124,108,93,125,90,113,110,111,102,119,108,126,122,106,110,115,115,110,111,86,81,97,133,144,88", "endOffsets": "207,308,403,497,625,739,839,930,1061,1188,1296,1420,1565,1694,1819,1928,2022,2148,2239,2353,2464,2576,2679,2799,2908,3035,3158,3265,3376,3492,3608,3719,3831,3918,4000,4098,4232,4377,4466"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "839,946,1047,1142,1236,1364,1478,1578,1669,1800,1927,2035,2159,2304,2433,2558,2667,2761,2887,2978,3092,3203,3315,3418,3538,3647,3774,3897,4004,4115,4231,4347,4458,4570,4657,4739,4837,4971,5116", "endColumns": "106,100,94,93,127,113,99,90,130,126,107,123,144,128,124,108,93,125,90,113,110,111,102,119,108,126,122,106,110,115,115,110,111,86,81,97,133,144,88", "endOffsets": "941,1042,1137,1231,1359,1473,1573,1664,1795,1922,2030,2154,2299,2428,2553,2662,2756,2882,2973,3087,3198,3310,3413,3533,3642,3769,3892,3999,4110,4226,4342,4453,4565,4652,4734,4832,4966,5111,5200"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ko_values-ko.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,315,407,497,611,713,807,896,1010,1123,1221,1336,1457,1567,1676,1774,1864,1972,2059,2155,2252,2349,2439,2547,2650,2754,2857,2956,3059,3159,3264,3362,3465,3552,3632,3722,3853,3994", "endColumns": "107,101,91,89,113,101,93,88,113,112,97,114,120,109,108,97,89,107,86,95,96,96,89,107,102,103,102,98,102,99,104,97,102,86,79,89,130,140,81", "endOffsets": "208,310,402,492,606,708,802,891,1005,1118,1216,1331,1452,1562,1671,1769,1859,1967,2054,2150,2247,2344,2434,2542,2645,2749,2852,2951,3054,3154,3259,3357,3460,3547,3627,3717,3848,3989,4071"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "782,890,992,1084,1174,1288,1390,1484,1573,1687,1800,1898,2013,2134,2244,2353,2451,2541,2649,2736,2832,2929,3026,3116,3224,3327,3431,3534,3633,3736,3836,3941,4039,4142,4229,4309,4399,4530,4671", "endColumns": "107,101,91,89,113,101,93,88,113,112,97,114,120,109,108,97,89,107,86,95,96,96,89,107,102,103,102,98,102,99,104,97,102,86,79,89,130,140,81", "endOffsets": "885,987,1079,1169,1283,1385,1479,1568,1682,1795,1893,2008,2129,2239,2348,2446,2536,2644,2731,2827,2924,3021,3111,3219,3322,3426,3529,3628,3731,3831,3936,4034,4137,4224,4304,4394,4525,4666,4748"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,297,391,488,584,682,4753", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "192,292,386,483,579,677,777,4849"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-my_values-my.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,208,312,415,517,622,728,5358", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "203,307,410,512,617,723,842,5454"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,512,642,754,854,946,1070,1194,1308,1438,1586,1716,1843,1962,2056,2204,2297,2408,2526,2638,2741,2861,2977,3095,3211,3326,3443,3568,3700,3818,3943,4030,4117,4218,4360,4516", "endColumns": "106,100,99,98,129,111,99,91,123,123,113,129,147,129,126,118,93,147,92,110,117,111,102,119,115,117,115,114,116,124,131,117,124,86,86,100,141,155,99", "endOffsets": "207,308,408,507,637,749,849,941,1065,1189,1303,1433,1581,1711,1838,1957,2051,2199,2292,2403,2521,2633,2736,2856,2972,3090,3206,3321,3438,3563,3695,3813,3938,4025,4112,4213,4355,4511,4611"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "847,954,1055,1155,1254,1384,1496,1596,1688,1812,1936,2050,2180,2328,2458,2585,2704,2798,2946,3039,3150,3268,3380,3483,3603,3719,3837,3953,4068,4185,4310,4442,4560,4685,4772,4859,4960,5102,5258", "endColumns": "106,100,99,98,129,111,99,91,123,123,113,129,147,129,126,118,93,147,92,110,117,111,102,119,115,117,115,114,116,124,131,117,124,86,86,100,141,155,99", "endOffsets": "949,1050,1150,1249,1379,1491,1591,1683,1807,1931,2045,2175,2323,2453,2580,2699,2793,2941,3034,3145,3263,3375,3478,3598,3714,3832,3948,4063,4180,4305,4437,4555,4680,4767,4854,4955,5097,5253,5353"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-v18_values-v18.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-v18\\values-v18.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "10", "endColumns": "12", "endOffsets": "642"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-si_values-si.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,310,415,520,619,723,5234", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "202,305,410,515,614,718,832,5330"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,415,514,630,739,834,928,1064,1199,1310,1440,1569,1693,1816,1925,2022,2163,2253,2361,2475,2581,2680,2796,2903,3013,3122,3228,3340,3466,3598,3722,3852,3939,4022,4123,4258,4410", "endColumns": "106,100,101,98,115,108,94,93,135,134,110,129,128,123,122,108,96,140,89,107,113,105,98,115,106,109,108,105,111,125,131,123,129,86,82,100,134,151,91", "endOffsets": "207,308,410,509,625,734,829,923,1059,1194,1305,1435,1564,1688,1811,1920,2017,2158,2248,2356,2470,2576,2675,2791,2898,3008,3117,3223,3335,3461,3593,3717,3847,3934,4017,4118,4253,4405,4497"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,944,1045,1147,1246,1362,1471,1566,1660,1796,1931,2042,2172,2301,2425,2548,2657,2754,2895,2985,3093,3207,3313,3412,3528,3635,3745,3854,3960,4072,4198,4330,4454,4584,4671,4754,4855,4990,5142", "endColumns": "106,100,101,98,115,108,94,93,135,134,110,129,128,123,122,108,96,140,89,107,113,105,98,115,106,109,108,105,111,125,131,123,129,86,82,100,134,151,91", "endOffsets": "939,1040,1142,1241,1357,1466,1561,1655,1791,1926,2037,2167,2296,2420,2543,2652,2749,2890,2980,3088,3202,3308,3407,3523,3630,3740,3849,3955,4067,4193,4325,4449,4579,4666,4749,4850,4985,5137,5229"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-eu_values-eu.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,415,509,638,750,849,949,1076,1199,1301,1419,1607,1748,1885,1993,2086,2228,2326,2435,2540,2645,2740,2852,2985,3112,3235,3343,3454,3573,3698,3813,3934,4021,4105,4213,4348,4518", "endColumns": "106,100,101,93,128,111,98,99,126,122,101,117,187,140,136,107,92,141,97,108,104,104,94,111,132,126,122,107,110,118,124,114,120,86,83,107,134,169,84", "endOffsets": "207,308,410,504,633,745,844,944,1071,1194,1296,1414,1602,1743,1880,1988,2081,2223,2321,2430,2535,2640,2735,2847,2980,3107,3230,3338,3449,3568,3693,3808,3929,4016,4100,4208,4343,4513,4598"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "834,941,1042,1144,1238,1367,1479,1578,1678,1805,1928,2030,2148,2336,2477,2614,2722,2815,2957,3055,3164,3269,3374,3469,3581,3714,3841,3964,4072,4183,4302,4427,4542,4663,4750,4834,4942,5077,5247", "endColumns": "106,100,101,93,128,111,98,99,126,122,101,117,187,140,136,107,92,141,97,108,104,104,94,111,132,126,122,107,110,118,124,114,120,86,83,107,134,169,84", "endOffsets": "936,1037,1139,1233,1362,1474,1573,1673,1800,1923,2025,2143,2331,2472,2609,2717,2810,2952,3050,3159,3264,3369,3464,3576,3709,3836,3959,4067,4178,4297,4422,4537,4658,4745,4829,4937,5072,5242,5327"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,563,666,784", "endColumns": "97,102,99,102,103,102,117,100", "endOffsets": "148,251,351,454,558,661,779,880"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,406,509,613,716,5332", "endColumns": "97,102,99,102,103,102,117,100", "endOffsets": "198,301,401,504,608,711,829,5428"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-or_values-or.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,414,515,639,750,851,948,1090,1231,1339,1467,1608,1737,1865,1970,2067,2201,2293,2404,2519,2625,2720,2845,2956,3073,3189,3298,3414,3532,3652,3765,3880,3967,4058,4162,4298,4453", "endColumns": "106,100,100,100,123,110,100,96,141,140,107,127,140,128,127,104,96,133,91,110,114,105,94,124,110,116,115,108,115,117,119,112,114,86,90,103,135,154,86", "endOffsets": "207,308,409,510,634,745,846,943,1085,1226,1334,1462,1603,1732,1860,1965,2062,2196,2288,2399,2514,2620,2715,2840,2951,3068,3184,3293,3409,3527,3647,3760,3875,3962,4053,4157,4293,4448,4535"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,947,1048,1149,1250,1374,1485,1586,1683,1825,1966,2074,2202,2343,2472,2600,2705,2802,2936,3028,3139,3254,3360,3455,3580,3691,3808,3924,4033,4149,4267,4387,4500,4615,4702,4793,4897,5033,5188", "endColumns": "106,100,100,100,123,110,100,96,141,140,107,127,140,128,127,104,96,133,91,110,114,105,94,124,110,116,115,108,115,117,119,112,114,86,90,103,135,154,86", "endOffsets": "942,1043,1144,1245,1369,1480,1581,1678,1820,1961,2069,2197,2338,2467,2595,2700,2797,2931,3023,3134,3249,3355,3450,3575,3686,3803,3919,4028,4144,4262,4382,4495,4610,4697,4788,4892,5028,5183,5270"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,208,310,413,518,619,721,5275", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "203,305,408,513,614,716,835,5371"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-af_values-af.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,508,626,739,839,934,1063,1189,1296,1419,1558,1681,1801,1909,2005,2131,2221,2325,2429,2531,2629,2744,2855,2969,3080,3188,3298,3407,3521,3628,3740,3827,3909,4010,4147,4303", "endColumns": "106,100,99,94,117,112,99,94,128,125,106,122,138,122,119,107,95,125,89,103,103,101,97,114,110,113,110,107,109,108,113,106,111,86,81,100,136,155,89", "endOffsets": "207,308,408,503,621,734,834,929,1058,1184,1291,1414,1553,1676,1796,1904,2000,2126,2216,2320,2424,2526,2624,2739,2850,2964,3075,3183,3293,3402,3516,3623,3735,3822,3904,4005,4142,4298,4388"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,944,1045,1145,1240,1358,1471,1571,1666,1795,1921,2028,2151,2290,2413,2533,2641,2737,2863,2953,3057,3161,3263,3361,3476,3587,3701,3812,3920,4030,4139,4253,4360,4472,4559,4641,4742,4879,5035", "endColumns": "106,100,99,94,117,112,99,94,128,125,106,122,138,122,119,107,95,125,89,103,103,101,97,114,110,113,110,107,109,108,113,106,111,86,81,100,136,155,89", "endOffsets": "939,1040,1140,1235,1353,1466,1566,1661,1790,1916,2023,2146,2285,2408,2528,2636,2732,2858,2948,3052,3156,3258,3356,3471,3582,3696,3807,3915,4025,4134,4248,4355,4467,4554,4636,4737,4874,5030,5120"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,403,501,608,717,5125", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "198,300,398,496,603,712,832,5221"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-v16_values-v16.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ar_values-ar.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,502,631,740,838,928,1054,1179,1282,1413,1570,1691,1811,1921,2017,2153,2243,2344,2447,2553,2645,2762,2880,2998,3115,3216,3321,3446,3573,3693,3815,3902,3985,4088,4223,4380", "endColumns": "106,100,95,92,128,108,97,89,125,124,102,130,156,120,119,109,95,135,89,100,102,105,91,116,117,117,116,100,104,124,126,119,121,86,82,102,134,156,87", "endOffsets": "207,308,404,497,626,735,833,923,1049,1174,1277,1408,1565,1686,1806,1916,2012,2148,2238,2339,2442,2548,2640,2757,2875,2993,3110,3211,3316,3441,3568,3688,3810,3897,3980,4083,4218,4375,4463"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "817,924,1025,1121,1214,1343,1452,1550,1640,1766,1891,1994,2125,2282,2403,2523,2633,2729,2865,2955,3056,3159,3265,3357,3474,3592,3710,3827,3928,4033,4158,4285,4405,4527,4614,4697,4800,4935,5092", "endColumns": "106,100,95,92,128,108,97,89,125,124,102,130,156,120,119,109,95,135,89,100,102,105,91,116,117,117,116,100,104,124,126,119,121,86,82,102,134,156,87", "endOffsets": "919,1020,1116,1209,1338,1447,1545,1635,1761,1886,1989,2120,2277,2398,2518,2628,2724,2860,2950,3051,3154,3260,3352,3469,3587,3705,3822,3923,4028,4153,4280,4400,4522,4609,4692,4795,4930,5087,5175"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,198,300,395,498,601,703,5180", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "193,295,390,493,596,698,812,5276"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ru_values-ru.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,502,641,752,849,940,1064,1186,1296,1422,1567,1694,1819,1925,2016,2149,2247,2351,2455,2570,2673,2793,2907,3026,3143,3264,3390,3506,3634,3745,3868,3955,4038,4141,4283,4455", "endColumns": "106,100,94,93,138,110,96,90,123,121,109,125,144,126,124,105,90,132,97,103,103,114,102,119,113,118,116,120,125,115,127,110,122,86,82,102,141,171,81", "endOffsets": "207,308,403,497,636,747,844,935,1059,1181,1291,1417,1562,1689,1814,1920,2011,2144,2242,2346,2450,2565,2668,2788,2902,3021,3138,3259,3385,3501,3629,3740,3863,3950,4033,4136,4278,4450,4532"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,939,1040,1135,1229,1368,1479,1576,1667,1791,1913,2023,2149,2294,2421,2546,2652,2743,2876,2974,3078,3182,3297,3400,3520,3634,3753,3870,3991,4117,4233,4361,4472,4595,4682,4765,4868,5010,5182", "endColumns": "106,100,94,93,138,110,96,90,123,121,109,125,144,126,124,105,90,132,97,103,103,114,102,119,113,118,116,120,125,115,127,110,122,86,82,102,141,171,81", "endOffsets": "934,1035,1130,1224,1363,1474,1571,1662,1786,1908,2018,2144,2289,2416,2541,2647,2738,2871,2969,3073,3177,3292,3395,3515,3629,3748,3865,3986,4112,4228,4356,4467,4590,4677,4760,4863,5005,5177,5259"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,406,507,612,715,5264", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "198,300,401,502,607,710,827,5360"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-zu_values-zu.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,502,646,762,866,957,1099,1245,1362,1501,1651,1779,1911,2019,2121,2257,2347,2450,2558,2663,2764,2882,2997,3109,3225,3332,3447,3571,3695,3818,3931,4018,4101,4206,4342,4500", "endColumns": "106,100,96,91,143,115,103,90,141,145,116,138,149,127,131,107,101,135,89,102,107,104,100,117,114,111,115,106,114,123,123,122,112,86,82,104,135,157,92", "endOffsets": "207,308,405,497,641,757,861,952,1094,1240,1357,1496,1646,1774,1906,2014,2116,2252,2342,2445,2553,2658,2759,2877,2992,3104,3220,3327,3442,3566,3690,3813,3926,4013,4096,4201,4337,4495,4588"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "835,942,1043,1140,1232,1376,1492,1596,1687,1829,1975,2092,2231,2381,2509,2641,2749,2851,2987,3077,3180,3288,3393,3494,3612,3727,3839,3955,4062,4177,4301,4425,4548,4661,4748,4831,4936,5072,5230", "endColumns": "106,100,96,91,143,115,103,90,141,145,116,138,149,127,131,107,101,135,89,102,107,104,100,117,114,111,115,106,114,123,123,122,112,86,82,104,135,157,92", "endOffsets": "937,1038,1135,1227,1371,1487,1591,1682,1824,1970,2087,2226,2376,2504,2636,2744,2846,2982,3072,3175,3283,3388,3489,3607,3722,3834,3950,4057,4172,4296,4420,4543,4656,4743,4826,4931,5067,5225,5318"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,307,406,509,615,722,5323", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "198,302,401,504,610,717,830,5419"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-lt_values-lt.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,313,412,515,626,736,5282", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "198,308,407,510,621,731,851,5378"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,317,412,508,629,739,836,928,1052,1175,1280,1412,1569,1692,1814,1922,2019,2146,2237,2341,2448,2552,2650,2776,2887,2999,3110,3214,3327,3463,3593,3725,3851,3938,4023,4135,4273,4437", "endColumns": "108,102,94,95,120,109,96,91,123,122,104,131,156,122,121,107,96,126,90,103,106,103,97,125,110,111,110,103,112,135,129,131,125,86,84,111,137,163,93", "endOffsets": "209,312,407,503,624,734,831,923,1047,1170,1275,1407,1564,1687,1809,1917,2014,2141,2232,2336,2443,2547,2645,2771,2882,2994,3105,3209,3322,3458,3588,3720,3846,3933,4018,4130,4268,4432,4526"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "856,965,1068,1163,1259,1380,1490,1587,1679,1803,1926,2031,2163,2320,2443,2565,2673,2770,2897,2988,3092,3199,3303,3401,3527,3638,3750,3861,3965,4078,4214,4344,4476,4602,4689,4774,4886,5024,5188", "endColumns": "108,102,94,95,120,109,96,91,123,122,104,131,156,122,121,107,96,126,90,103,106,103,97,125,110,111,110,103,112,135,129,131,125,86,84,111,137,163,93", "endOffsets": "960,1063,1158,1254,1375,1485,1582,1674,1798,1921,2026,2158,2315,2438,2560,2668,2765,2892,2983,3087,3194,3298,3396,3522,3633,3745,3856,3960,4073,4209,4339,4471,4597,4684,4769,4881,5019,5183,5277"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-pl_values-pl.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,402,501,615,720,5194", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "197,299,397,496,610,715,837,5290"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,503,628,741,838,933,1052,1169,1279,1405,1576,1697,1816,1923,2018,2144,2236,2343,2448,2552,2654,2773,2905,3026,3145,3249,3358,3466,3582,3684,3794,3881,3965,4070,4206,4363", "endColumns": "106,100,94,94,124,112,96,94,118,116,109,125,170,120,118,106,94,125,91,106,104,103,101,118,131,120,118,103,108,107,115,101,109,86,83,104,135,156,93", "endOffsets": "207,308,403,498,623,736,833,928,1047,1164,1274,1400,1571,1692,1811,1918,2013,2139,2231,2338,2443,2547,2649,2768,2900,3021,3140,3244,3353,3461,3577,3679,3789,3876,3960,4065,4201,4358,4452"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,949,1050,1145,1240,1365,1478,1575,1670,1789,1906,2016,2142,2313,2434,2553,2660,2755,2881,2973,3080,3185,3289,3391,3510,3642,3763,3882,3986,4095,4203,4319,4421,4531,4618,4702,4807,4943,5100", "endColumns": "106,100,94,94,124,112,96,94,118,116,109,125,170,120,118,106,94,125,91,106,104,103,101,118,131,120,118,103,108,107,115,101,109,86,83,104,135,156,93", "endOffsets": "944,1045,1140,1235,1360,1473,1570,1665,1784,1901,2011,2137,2308,2429,2548,2655,2750,2876,2968,3075,3180,3284,3386,3505,3637,3758,3877,3981,4090,4198,4314,4416,4526,4613,4697,4802,4938,5095,5189"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-am_values-am.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,198,298,395,494,590,692,4890", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "193,293,390,489,585,687,787,4986"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,406,497,613,718,813,901,1027,1150,1253,1372,1497,1613,1726,1830,1924,2043,2132,2231,2332,2431,2523,2633,2745,2852,2956,3057,3160,3261,3368,3467,3572,3659,3741,3836,3970,4117", "endColumns": "106,100,92,90,115,104,94,87,125,122,102,118,124,115,112,103,93,118,88,98,100,98,91,109,111,106,103,100,102,100,106,98,104,86,81,94,133,146,85", "endOffsets": "207,308,401,492,608,713,808,896,1022,1145,1248,1367,1492,1608,1721,1825,1919,2038,2127,2226,2327,2426,2518,2628,2740,2847,2951,3052,3155,3256,3363,3462,3567,3654,3736,3831,3965,4112,4198"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,899,1000,1093,1184,1300,1405,1500,1588,1714,1837,1940,2059,2184,2300,2413,2517,2611,2730,2819,2918,3019,3118,3210,3320,3432,3539,3643,3744,3847,3948,4055,4154,4259,4346,4428,4523,4657,4804", "endColumns": "106,100,92,90,115,104,94,87,125,122,102,118,124,115,112,103,93,118,88,98,100,98,91,109,111,106,103,100,102,100,106,98,104,86,81,94,133,146,85", "endOffsets": "894,995,1088,1179,1295,1400,1495,1583,1709,1832,1935,2054,2179,2295,2408,2512,2606,2725,2814,2913,3014,3113,3205,3315,3427,3534,3638,3739,3842,3943,4050,4149,4254,4341,4423,4518,4652,4799,4885"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-vi_values-vi.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,507,624,736,832,924,1040,1155,1254,1369,1507,1633,1758,1862,1956,2081,2170,2275,2380,2486,2580,2691,2813,2928,3042,3156,3269,3379,3494,3596,3703,3790,3876,3976,4113,4264", "endColumns": "106,100,97,95,116,111,95,91,115,114,98,114,137,125,124,103,93,124,88,104,104,105,93,110,121,114,113,113,112,109,114,101,106,86,85,99,136,150,91", "endOffsets": "207,308,406,502,619,731,827,919,1035,1150,1249,1364,1502,1628,1753,1857,1951,2076,2165,2270,2375,2481,2575,2686,2808,2923,3037,3151,3264,3374,3489,3591,3698,3785,3871,3971,4108,4259,4351"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "835,942,1043,1141,1237,1354,1466,1562,1654,1770,1885,1984,2099,2237,2363,2488,2592,2686,2811,2900,3005,3110,3216,3310,3421,3543,3658,3772,3886,3999,4109,4224,4326,4433,4520,4606,4706,4843,4994", "endColumns": "106,100,97,95,116,111,95,91,115,114,98,114,137,125,124,103,93,124,88,104,104,105,93,110,121,114,113,113,112,109,114,101,106,86,85,99,136,150,91", "endOffsets": "937,1038,1136,1232,1349,1461,1557,1649,1765,1880,1979,2094,2232,2358,2483,2587,2681,2806,2895,3000,3105,3211,3305,3416,3538,3653,3767,3881,3994,4104,4219,4321,4428,4515,4601,4701,4838,4989,5081"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,403,503,606,719,5086", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "197,299,398,498,601,714,830,5182"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-en-rGB_values-en-rGB.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,501,605,708,5045", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "196,298,397,496,600,703,819,5141"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,505,624,736,832,928,1059,1188,1293,1414,1542,1663,1782,1887,1978,2106,2195,2296,2399,2500,2593,2703,2809,2920,3029,3128,3235,3345,3461,3567,3679,3766,3850,3950,4085,4236", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "207,308,406,500,619,731,827,923,1054,1183,1288,1409,1537,1658,1777,1882,1973,2101,2190,2291,2394,2495,2588,2698,2804,2915,3024,3123,3230,3340,3456,3562,3674,3761,3845,3945,4080,4231,4321"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "824,931,1032,1130,1224,1343,1455,1551,1647,1778,1907,2012,2133,2261,2382,2501,2606,2697,2825,2914,3015,3118,3219,3312,3422,3528,3639,3748,3847,3954,4064,4180,4286,4398,4485,4569,4669,4804,4955", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "926,1027,1125,1219,1338,1450,1546,1642,1773,1902,2007,2128,2256,2377,2496,2601,2692,2820,2909,3010,3113,3214,3307,3417,3523,3634,3743,3842,3949,4059,4175,4281,4393,4480,4564,4664,4799,4950,5040"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-v22_values-v22.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-v22\\values-v22.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,107", "endColumns": "51,53", "endOffsets": "102,156"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-km_values-km.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,407,501,623,731,830,924,1043,1161,1264,1383,1529,1646,1762,1873,1964,2088,2177,2287,2396,2502,2600,2715,2836,2946,3056,3160,3267,3387,3512,3627,3745,3832,3917,4023,4159,4315", "endColumns": "106,100,93,93,121,107,98,93,118,117,102,118,145,116,115,110,90,123,88,109,108,105,97,114,120,109,109,103,106,119,124,114,117,86,84,105,135,155,93", "endOffsets": "207,308,402,496,618,726,825,919,1038,1156,1259,1378,1524,1641,1757,1868,1959,2083,2172,2282,2391,2497,2595,2710,2831,2941,3051,3155,3262,3382,3507,3622,3740,3827,3912,4018,4154,4310,4404"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "826,933,1034,1128,1222,1344,1452,1551,1645,1764,1882,1985,2104,2250,2367,2483,2594,2685,2809,2898,3008,3117,3223,3321,3436,3557,3667,3777,3881,3988,4108,4233,4348,4466,4553,4638,4744,4880,5036", "endColumns": "106,100,93,93,121,107,98,93,118,117,102,118,145,116,115,110,90,123,88,109,108,105,97,114,120,109,109,103,106,119,124,114,117,86,84,105,135,155,93", "endOffsets": "928,1029,1123,1217,1339,1447,1546,1640,1759,1877,1980,2099,2245,2362,2478,2589,2680,2804,2893,3003,3112,3218,3316,3431,3552,3662,3772,3876,3983,4103,4228,4343,4461,4548,4633,4739,4875,5031,5125"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,303,401,501,602,714,5130", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "195,298,396,496,597,709,821,5226"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-gl_values-gl.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,509,637,752,852,944,1071,1194,1300,1422,1598,1723,1844,1949,2041,2172,2267,2370,2473,2582,2678,2792,2922,3050,3174,3282,3394,3504,3627,3730,3846,3933,4016,4127,4261,4413", "endColumns": "106,100,98,96,127,114,99,91,126,122,105,121,175,124,120,104,91,130,94,102,102,108,95,113,129,127,123,107,111,109,122,102,115,86,82,110,133,151,91", "endOffsets": "207,308,407,504,632,747,847,939,1066,1189,1295,1417,1593,1718,1839,1944,2036,2167,2262,2365,2468,2577,2673,2787,2917,3045,3169,3277,3389,3499,3622,3725,3841,3928,4011,4122,4256,4408,4500"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "833,940,1041,1140,1237,1365,1480,1580,1672,1799,1922,2028,2150,2326,2451,2572,2677,2769,2900,2995,3098,3201,3310,3406,3520,3650,3778,3902,4010,4122,4232,4355,4458,4574,4661,4744,4855,4989,5141", "endColumns": "106,100,98,96,127,114,99,91,126,122,105,121,175,124,120,104,91,130,94,102,102,108,95,113,129,127,123,107,111,109,122,102,115,86,82,110,133,151,91", "endOffsets": "935,1036,1135,1232,1360,1475,1575,1667,1794,1917,2023,2145,2321,2446,2567,2672,2764,2895,2990,3093,3196,3305,3401,3515,3645,3773,3897,4005,4117,4227,4350,4453,4569,4656,4739,4850,4984,5136,5228"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,406,504,611,717,5233", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "199,301,401,499,606,712,828,5329"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-gu_values-gu.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,508,629,739,834,930,1062,1194,1304,1433,1576,1698,1820,1924,2016,2146,2236,2342,2453,2563,2662,2781,2894,3007,3120,3222,3329,3444,3565,3676,3793,3880,3962,4062,4195,4348", "endColumns": "106,100,96,97,120,109,94,95,131,131,109,128,142,121,121,103,91,129,89,105,110,109,98,118,112,112,112,101,106,114,120,110,116,86,81,99,132,152,90", "endOffsets": "207,308,405,503,624,734,829,925,1057,1189,1299,1428,1571,1693,1815,1919,2011,2141,2231,2337,2448,2558,2657,2776,2889,3002,3115,3217,3324,3439,3560,3671,3788,3875,3957,4057,4190,4343,4434"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "823,930,1031,1128,1226,1347,1457,1552,1648,1780,1912,2022,2151,2294,2416,2538,2642,2734,2864,2954,3060,3171,3281,3380,3499,3612,3725,3838,3940,4047,4162,4283,4394,4511,4598,4680,4780,4913,5066", "endColumns": "106,100,96,97,120,109,94,95,131,131,109,128,142,121,121,103,91,129,89,105,110,109,98,118,112,112,112,101,106,114,120,110,116,86,81,99,132,152,90", "endOffsets": "925,1026,1123,1221,1342,1452,1547,1643,1775,1907,2017,2146,2289,2411,2533,2637,2729,2859,2949,3055,3166,3276,3375,3494,3607,3720,3833,3935,4042,4157,4278,4389,4506,4593,4675,4775,4908,5061,5152"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,302,399,501,603,701,5157", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "194,297,394,496,598,696,818,5253"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-kk_values-kk.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,404,507,611,708,5209", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "195,297,399,502,606,703,814,5305"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,507,630,743,841,932,1056,1178,1285,1408,1582,1702,1820,1930,2023,2160,2251,2359,2472,2579,2681,2800,2931,3047,3161,3263,3370,3486,3610,3722,3842,3929,4012,4116,4250,4405", "endColumns": "106,100,99,93,122,112,97,90,123,121,106,122,173,119,117,109,92,136,90,107,112,106,101,118,130,115,113,101,106,115,123,111,119,86,82,103,133,154,89", "endOffsets": "207,308,408,502,625,738,836,927,1051,1173,1280,1403,1577,1697,1815,1925,2018,2155,2246,2354,2467,2574,2676,2795,2926,3042,3156,3258,3365,3481,3605,3717,3837,3924,4007,4111,4245,4400,4490"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "819,926,1027,1127,1221,1344,1457,1555,1646,1770,1892,1999,2122,2296,2416,2534,2644,2737,2874,2965,3073,3186,3293,3395,3514,3645,3761,3875,3977,4084,4200,4324,4436,4556,4643,4726,4830,4964,5119", "endColumns": "106,100,99,93,122,112,97,90,123,121,106,122,173,119,117,109,92,136,90,107,112,106,101,118,130,115,113,101,106,115,123,111,119,86,82,103,133,154,89", "endOffsets": "921,1022,1122,1216,1339,1452,1550,1641,1765,1887,1994,2117,2291,2411,2529,2639,2732,2869,2960,3068,3181,3288,3390,3509,3640,3756,3870,3972,4079,4195,4319,4431,4551,4638,4721,4825,4959,5114,5204"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-pt-rPT_values-pt-rPT.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,403,503,610,716,5204", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "197,299,398,498,605,711,832,5300"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,634,748,848,951,1075,1195,1295,1411,1572,1697,1818,1921,2018,2147,2242,2345,2448,2549,2642,2752,2874,3000,3122,3235,3352,3466,3585,3693,3806,3893,3980,4085,4223,4379", "endColumns": "106,100,98,95,125,113,99,102,123,119,99,115,160,124,120,102,96,128,94,102,102,100,92,109,121,125,121,112,116,113,118,107,112,86,86,104,137,155,92", "endOffsets": "207,308,407,503,629,743,843,946,1070,1190,1290,1406,1567,1692,1813,1916,2013,2142,2237,2340,2443,2544,2637,2747,2869,2995,3117,3230,3347,3461,3580,3688,3801,3888,3975,4080,4218,4374,4467"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,944,1045,1144,1240,1366,1480,1580,1683,1807,1927,2027,2143,2304,2429,2550,2653,2750,2879,2974,3077,3180,3281,3374,3484,3606,3732,3854,3967,4084,4198,4317,4425,4538,4625,4712,4817,4955,5111", "endColumns": "106,100,98,95,125,113,99,102,123,119,99,115,160,124,120,102,96,128,94,102,102,100,92,109,121,125,121,112,116,113,118,107,112,86,86,104,137,155,92", "endOffsets": "939,1040,1139,1235,1361,1475,1575,1678,1802,1922,2022,2138,2299,2424,2545,2648,2745,2874,2969,3072,3175,3276,3369,3479,3601,3727,3849,3962,4079,4193,4312,4420,4533,4620,4707,4812,4950,5106,5199"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-lv_values-lv.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,506,627,743,841,936,1068,1199,1309,1435,1610,1758,1905,2012,2104,2235,2328,2432,2542,2647,2739,2856,2974,3104,3233,3337,3450,3564,3680,3790,3902,3989,4073,4176,4315,4472", "endColumns": "106,100,97,94,120,115,97,94,131,130,109,125,174,147,146,106,91,130,92,103,109,104,91,116,117,129,128,103,112,113,115,109,111,86,83,102,138,156,94", "endOffsets": "207,308,406,501,622,738,836,931,1063,1194,1304,1430,1605,1753,1900,2007,2099,2230,2323,2427,2537,2642,2734,2851,2969,3099,3228,3332,3445,3559,3675,3785,3897,3984,4068,4171,4310,4467,4562"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,943,1044,1142,1237,1358,1474,1572,1667,1799,1930,2040,2166,2341,2489,2636,2743,2835,2966,3059,3163,3273,3378,3470,3587,3705,3835,3964,4068,4181,4295,4411,4521,4633,4720,4804,4907,5046,5203", "endColumns": "106,100,97,94,120,115,97,94,131,130,109,125,174,147,146,106,91,130,92,103,109,104,91,116,117,129,128,103,112,113,115,109,111,86,83,102,138,156,94", "endOffsets": "938,1039,1137,1232,1353,1469,1567,1662,1794,1925,2035,2161,2336,2484,2631,2738,2830,2961,3054,3158,3268,3373,3465,3582,3700,3830,3959,4063,4176,4290,4406,4516,4628,4715,4799,4902,5041,5198,5293"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,506,613,721,5298", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "198,300,400,501,608,716,831,5394"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-uk_values-uk.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,307,408,509,614,719,5303", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "200,302,403,504,609,714,827,5399"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,507,628,739,835,926,1049,1172,1282,1410,1571,1694,1817,1923,2020,2158,2253,2357,2463,2576,2679,2801,2920,3038,3156,3274,3397,3525,3657,3780,3907,3994,4077,4189,4325,4485", "endColumns": "106,100,99,93,120,110,95,90,122,122,109,127,160,122,122,105,96,137,94,103,105,112,102,121,118,117,117,117,122,127,131,122,126,86,82,111,135,159,90", "endOffsets": "207,308,408,502,623,734,830,921,1044,1167,1277,1405,1566,1689,1812,1918,2015,2153,2248,2352,2458,2571,2674,2796,2915,3033,3151,3269,3392,3520,3652,3775,3902,3989,4072,4184,4320,4480,4571"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,939,1040,1140,1234,1355,1466,1562,1653,1776,1899,2009,2137,2298,2421,2544,2650,2747,2885,2980,3084,3190,3303,3406,3528,3647,3765,3883,4001,4124,4252,4384,4507,4634,4721,4804,4916,5052,5212", "endColumns": "106,100,99,93,120,110,95,90,122,122,109,127,160,122,122,105,96,137,94,103,105,112,102,121,118,117,117,117,122,127,131,122,126,86,82,111,135,159,90", "endOffsets": "934,1035,1135,1229,1350,1461,1557,1648,1771,1894,2004,2132,2293,2416,2539,2645,2742,2880,2975,3079,3185,3298,3401,3523,3642,3760,3878,3996,4119,4247,4379,4502,4629,4716,4799,4911,5047,5207,5298"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-mr_values-mr.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,506,626,738,835,928,1049,1169,1271,1392,1533,1655,1777,1881,1977,2107,2196,2301,2413,2516,2614,2725,2840,2951,3062,3162,3266,3379,3492,3601,3710,3797,3879,3980,4113,4265", "endColumns": "106,100,98,93,119,111,96,92,120,119,101,120,140,121,121,103,95,129,88,104,111,102,97,110,114,110,110,99,103,112,112,108,108,86,81,100,132,151,86", "endOffsets": "207,308,407,501,621,733,830,923,1044,1164,1266,1387,1528,1650,1772,1876,1972,2102,2191,2296,2408,2511,2609,2720,2835,2946,3057,3157,3261,3374,3487,3596,3705,3792,3874,3975,4108,4260,4347"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,944,1045,1144,1238,1358,1470,1567,1660,1781,1901,2003,2124,2265,2387,2509,2613,2709,2839,2928,3033,3145,3248,3346,3457,3572,3683,3794,3894,3998,4111,4224,4333,4442,4529,4611,4712,4845,4997", "endColumns": "106,100,98,93,119,111,96,92,120,119,101,120,140,121,121,103,95,129,88,104,111,102,97,110,114,110,110,99,103,112,112,108,108,86,81,100,132,151,86", "endOffsets": "939,1040,1139,1233,1353,1465,1562,1655,1776,1896,1998,2119,2260,2382,2504,2608,2704,2834,2923,3028,3140,3243,3341,3452,3567,3678,3789,3889,3993,4106,4219,4328,4437,4524,4606,4707,4840,4992,5079"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,309,410,513,615,720,5084", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "200,304,405,508,610,715,832,5180"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ne_values-ne.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,418,520,653,763,860,954,1108,1260,1377,1500,1662,1796,1928,2034,2132,2279,2378,2489,2607,2719,2824,2946,3066,3204,3340,3446,3558,3690,3823,3952,4082,4169,4257,4365,4504,4663", "endColumns": "106,100,104,101,132,109,96,93,153,151,116,122,161,133,131,105,97,146,98,110,117,111,104,121,119,137,135,105,111,131,132,128,129,86,87,107,138,158,94", "endOffsets": "207,308,413,515,648,758,855,949,1103,1255,1372,1495,1657,1791,1923,2029,2127,2274,2373,2484,2602,2714,2819,2941,3061,3199,3335,3441,3553,3685,3818,3947,4077,4164,4252,4360,4499,4658,4753"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "825,932,1033,1138,1240,1373,1483,1580,1674,1828,1980,2097,2220,2382,2516,2648,2754,2852,2999,3098,3209,3327,3439,3544,3666,3786,3924,4060,4166,4278,4410,4543,4672,4802,4889,4977,5085,5224,5383", "endColumns": "106,100,104,101,132,109,96,93,153,151,116,122,161,133,131,105,97,146,98,110,117,111,104,121,119,137,135,105,111,131,132,128,129,86,87,107,138,158,94", "endOffsets": "927,1028,1133,1235,1368,1478,1575,1669,1823,1975,2092,2215,2377,2511,2643,2749,2847,2994,3093,3204,3322,3434,3539,3661,3781,3919,4055,4161,4273,4405,4538,4667,4797,4884,4972,5080,5219,5378,5473"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,413,519,617,717,5478", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "203,306,408,514,612,712,820,5574"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-de_values-de.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,503,622,734,832,930,1059,1185,1287,1405,1591,1718,1842,1951,2046,2174,2270,2377,2493,2607,2706,2822,2941,3060,3176,3295,3420,3541,3668,3781,3900,3987,4070,4174,4312,4470", "endColumns": "106,100,95,93,118,111,97,97,128,125,101,117,185,126,123,108,94,127,95,106,115,113,98,115,118,118,115,118,124,120,126,112,118,86,82,103,137,157,87", "endOffsets": "207,308,404,498,617,729,827,925,1054,1180,1282,1400,1586,1713,1837,1946,2041,2169,2265,2372,2488,2602,2701,2817,2936,3055,3171,3290,3415,3536,3663,3776,3895,3982,4065,4169,4307,4465,4553"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,943,1044,1140,1234,1353,1465,1563,1661,1790,1916,2018,2136,2322,2449,2573,2682,2777,2905,3001,3108,3224,3338,3437,3553,3672,3791,3907,4026,4151,4272,4399,4512,4631,4718,4801,4905,5043,5201", "endColumns": "106,100,95,93,118,111,97,97,128,125,101,117,185,126,123,108,94,127,95,106,115,113,98,115,118,118,115,118,124,120,126,112,118,86,82,103,137,157,87", "endOffsets": "938,1039,1135,1229,1348,1460,1558,1656,1785,1911,2013,2131,2317,2444,2568,2677,2772,2900,2996,3103,3219,3333,3432,3548,3667,3786,3902,4021,4146,4267,4394,4507,4626,4713,4796,4900,5038,5196,5284"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,505,613,718,5289", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "198,300,400,500,608,713,831,5385"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-mk_values-mk.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,402,500,605,708,5309", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "198,300,397,495,600,703,819,5405"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,505,637,752,850,944,1078,1209,1317,1441,1632,1758,1881,1988,2082,2214,2304,2410,2522,2625,2728,2848,2985,3098,3208,3317,3433,3548,3669,3782,3901,3988,4076,4189,4330,4491", "endColumns": "106,100,97,93,131,114,97,93,133,130,107,123,190,125,122,106,93,131,89,105,111,102,102,119,136,112,109,108,115,114,120,112,118,86,87,112,140,160,98", "endOffsets": "207,308,406,500,632,747,845,939,1073,1204,1312,1436,1627,1753,1876,1983,2077,2209,2299,2405,2517,2620,2723,2843,2980,3093,3203,3312,3428,3543,3664,3777,3896,3983,4071,4184,4325,4486,4585"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "824,931,1032,1130,1224,1356,1471,1569,1663,1797,1928,2036,2160,2351,2477,2600,2707,2801,2933,3023,3129,3241,3344,3447,3567,3704,3817,3927,4036,4152,4267,4388,4501,4620,4707,4795,4908,5049,5210", "endColumns": "106,100,97,93,131,114,97,93,133,130,107,123,190,125,122,106,93,131,89,105,111,102,102,119,136,112,109,108,115,114,120,112,118,86,87,112,140,160,98", "endOffsets": "926,1027,1125,1219,1351,1466,1564,1658,1792,1923,2031,2155,2346,2472,2595,2702,2796,2928,3018,3124,3236,3339,3442,3562,3699,3812,3922,4031,4147,4262,4383,4496,4615,4702,4790,4903,5044,5205,5304"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-hu_values-hu.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,506,623,735,833,931,1056,1184,1295,1416,1574,1706,1841,1951,2043,2167,2261,2368,2474,2578,2678,2795,2907,3036,3168,3277,3387,3514,3649,3770,3899,3986,4071,4187,4328,4499", "endColumns": "106,100,95,96,116,111,97,97,124,127,110,120,157,131,134,109,91,123,93,106,105,103,99,116,111,128,131,108,109,126,134,120,128,86,84,115,140,170,92", "endOffsets": "207,308,404,501,618,730,828,926,1051,1179,1290,1411,1569,1701,1836,1946,2038,2162,2256,2363,2469,2573,2673,2790,2902,3031,3163,3272,3382,3509,3644,3765,3894,3981,4066,4182,4323,4494,4587"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "827,934,1035,1131,1228,1345,1457,1555,1653,1778,1906,2017,2138,2296,2428,2563,2673,2765,2889,2983,3090,3196,3300,3400,3517,3629,3758,3890,3999,4109,4236,4371,4492,4621,4708,4793,4909,5050,5221", "endColumns": "106,100,95,96,116,111,97,97,124,127,110,120,157,131,134,109,91,123,93,106,105,103,99,116,111,128,131,108,109,126,134,120,128,86,84,115,140,170,92", "endOffsets": "929,1030,1126,1223,1340,1452,1550,1648,1773,1901,2012,2133,2291,2423,2558,2668,2760,2884,2978,3085,3191,3295,3395,3512,3624,3753,3885,3994,4104,4231,4366,4487,4616,4703,4788,4904,5045,5216,5309"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,406,507,610,717,5314", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "197,299,401,502,605,712,822,5410"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-is_values-is.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,501,622,734,831,929,1055,1180,1284,1404,1551,1674,1796,1904,1993,2114,2204,2309,2417,2522,2623,2741,2851,2964,3076,3178,3284,3396,3511,3619,3730,3817,3900,4004,4140,4297", "endColumns": "106,100,94,92,120,111,96,97,125,124,103,119,146,122,121,107,88,120,89,104,107,104,100,117,109,112,111,101,105,111,114,107,110,86,82,103,135,156,88", "endOffsets": "207,308,403,496,617,729,826,924,1050,1175,1279,1399,1546,1669,1791,1899,1988,2109,2199,2304,2412,2517,2618,2736,2846,2959,3071,3173,3279,3391,3506,3614,3725,3812,3895,3999,4135,4292,4381"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "822,929,1030,1125,1218,1339,1451,1548,1646,1772,1897,2001,2121,2268,2391,2513,2621,2710,2831,2921,3026,3134,3239,3340,3458,3568,3681,3793,3895,4001,4113,4228,4336,4447,4534,4617,4721,4857,5014", "endColumns": "106,100,94,92,120,111,96,97,125,124,103,119,146,122,121,107,88,120,89,104,107,104,100,117,109,112,111,101,105,111,114,107,110,86,82,103,135,156,88", "endOffsets": "924,1025,1120,1213,1334,1446,1543,1641,1767,1892,1996,2116,2263,2386,2508,2616,2705,2826,2916,3021,3129,3234,3335,3453,3563,3676,3788,3890,3996,4108,4223,4331,4442,4529,4612,4716,4852,5009,5098"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,307,404,504,607,711,5103", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "195,302,399,499,602,706,817,5199"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-zh-rTW_values-zh-rTW.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,296,390,484,577,670,4695", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "192,291,385,479,572,665,761,4791"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,405,495,608,709,803,892,1007,1121,1216,1327,1435,1543,1650,1747,1835,1942,2029,2128,2225,2324,2413,2519,2613,2715,2816,2913,3014,3114,3220,3317,3420,3507,3587,3678,3810,3953", "endColumns": "106,100,91,89,112,100,93,88,114,113,94,110,107,107,106,96,87,106,86,98,96,98,88,105,93,101,100,96,100,99,105,96,102,86,79,90,131,142,80", "endOffsets": "207,308,400,490,603,704,798,887,1002,1116,1211,1322,1430,1538,1645,1742,1830,1937,2024,2123,2220,2319,2408,2514,2608,2710,2811,2908,3009,3109,3215,3312,3415,3502,3582,3673,3805,3948,4029"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,873,974,1066,1156,1269,1370,1464,1553,1668,1782,1877,1988,2096,2204,2311,2408,2496,2603,2690,2789,2886,2985,3074,3180,3274,3376,3477,3574,3675,3775,3881,3978,4081,4168,4248,4339,4471,4614", "endColumns": "106,100,91,89,112,100,93,88,114,113,94,110,107,107,106,96,87,106,86,98,96,98,88,105,93,101,100,96,100,99,105,96,102,86,79,90,131,142,80", "endOffsets": "868,969,1061,1151,1264,1365,1459,1548,1663,1777,1872,1983,2091,2199,2306,2403,2491,2598,2685,2784,2881,2980,3069,3175,3269,3371,3472,3569,3670,3770,3876,3973,4076,4163,4243,4334,4466,4609,4690"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-el_values-el.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,406,509,617,723,5373", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "198,301,401,504,612,718,835,5469"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,504,633,745,844,935,1065,1192,1301,1426,1574,1706,1835,1950,2041,2189,2285,2390,2497,2612,2708,2821,2939,3070,3198,3306,3422,3551,3685,3812,3944,4031,4118,4234,4372,4542", "endColumns": "106,100,97,92,128,111,98,90,129,126,108,124,147,131,128,114,90,147,95,104,106,114,95,112,117,130,127,107,115,128,133,126,131,86,86,115,137,169,95", "endOffsets": "207,308,406,499,628,740,839,930,1060,1187,1296,1421,1569,1701,1830,1945,2036,2184,2280,2385,2492,2607,2703,2816,2934,3065,3193,3301,3417,3546,3680,3807,3939,4026,4113,4229,4367,4537,4633"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,947,1048,1146,1239,1368,1480,1579,1670,1800,1927,2036,2161,2309,2441,2570,2685,2776,2924,3020,3125,3232,3347,3443,3556,3674,3805,3933,4041,4157,4286,4420,4547,4679,4766,4853,4969,5107,5277", "endColumns": "106,100,97,92,128,111,98,90,129,126,108,124,147,131,128,114,90,147,95,104,106,114,95,112,117,130,127,107,115,128,133,126,131,86,86,115,137,169,95", "endOffsets": "942,1043,1141,1234,1363,1475,1574,1665,1795,1922,2031,2156,2304,2436,2565,2680,2771,2919,3015,3120,3227,3342,3438,3551,3669,3800,3928,4036,4152,4281,4415,4542,4674,4761,4848,4964,5102,5272,5368"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-az_values-az.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,511,630,745,844,937,1071,1202,1314,1442,1587,1718,1846,1957,2053,2190,2287,2398,2504,2612,2709,2823,2934,3055,3173,3278,3386,3502,3611,3723,3827,3914,3999,4104,4241,4398", "endColumns": "106,100,99,97,118,114,98,92,133,130,111,127,144,130,127,110,95,136,96,110,105,107,96,113,110,120,117,104,107,115,108,111,103,86,84,104,136,156,94", "endOffsets": "207,308,408,506,625,740,839,932,1066,1197,1309,1437,1582,1713,1841,1952,2048,2185,2282,2393,2499,2607,2704,2818,2929,3050,3168,3273,3381,3497,3606,3718,3822,3909,3994,4099,4236,4393,4488"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,939,1040,1140,1238,1357,1472,1571,1664,1798,1929,2041,2169,2314,2445,2573,2684,2780,2917,3014,3125,3231,3339,3436,3550,3661,3782,3900,4005,4113,4229,4338,4450,4554,4641,4726,4831,4968,5125", "endColumns": "106,100,99,97,118,114,98,92,133,130,111,127,144,130,127,110,95,136,96,110,105,107,96,113,110,120,117,104,107,115,108,111,103,86,84,104,136,156,94", "endOffsets": "934,1035,1135,1233,1352,1467,1566,1659,1793,1924,2036,2164,2309,2440,2568,2679,2775,2912,3009,3120,3226,3334,3431,3545,3656,3777,3895,4000,4108,4224,4333,4445,4549,4636,4721,4826,4963,5120,5215"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,206,308,411,515,616,721,5220", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "201,303,406,510,611,716,827,5316"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-pt-rBR_values-pt-rBR.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,403,503,610,720,5201", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "197,299,398,498,605,715,835,5297"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,634,748,847,948,1081,1210,1310,1426,1592,1717,1838,1941,2033,2165,2260,2363,2466,2572,2669,2783,2911,3032,3149,3252,3360,3471,3587,3692,3802,3889,3976,4080,4218,4373", "endColumns": "106,100,98,95,125,113,98,100,132,128,99,115,165,124,120,102,91,131,94,102,102,105,96,113,127,120,116,102,107,110,115,104,109,86,86,103,137,154,92", "endOffsets": "207,308,407,503,629,743,842,943,1076,1205,1305,1421,1587,1712,1833,1936,2028,2160,2255,2358,2461,2567,2664,2778,2906,3027,3144,3247,3355,3466,3582,3687,3797,3884,3971,4075,4213,4368,4461"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,947,1048,1147,1243,1369,1483,1582,1683,1816,1945,2045,2161,2327,2452,2573,2676,2768,2900,2995,3098,3201,3307,3404,3518,3646,3767,3884,3987,4095,4206,4322,4427,4537,4624,4711,4815,4953,5108", "endColumns": "106,100,98,95,125,113,98,100,132,128,99,115,165,124,120,102,91,131,94,102,102,105,96,113,127,120,116,102,107,110,115,104,109,86,86,103,137,154,92", "endOffsets": "942,1043,1142,1238,1364,1478,1577,1678,1811,1940,2040,2156,2322,2447,2568,2671,2763,2895,2990,3093,3196,3302,3399,3513,3641,3762,3879,3982,4090,4201,4317,4422,4532,4619,4706,4810,4948,5103,5196"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-pa_values-pa.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,624,735,831,927,1052,1177,1288,1415,1548,1676,1804,1909,1999,2133,2222,2322,2432,2537,2633,2746,2853,2968,3083,3186,3294,3414,3534,3645,3756,3843,3925,4022,4155,4303", "endColumns": "106,100,98,95,115,110,95,95,124,124,110,126,132,127,127,104,89,133,88,99,109,104,95,112,106,114,114,102,107,119,119,110,110,86,81,96,132,147,86", "endOffsets": "207,308,407,503,619,730,826,922,1047,1172,1283,1410,1543,1671,1799,1904,1994,2128,2217,2317,2427,2532,2628,2741,2848,2963,3078,3181,3289,3409,3529,3640,3751,3838,3920,4017,4150,4298,4385"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "838,945,1046,1145,1241,1357,1468,1564,1660,1785,1910,2021,2148,2281,2409,2537,2642,2732,2866,2955,3055,3165,3270,3366,3479,3586,3701,3816,3919,4027,4147,4267,4378,4489,4576,4658,4755,4888,5036", "endColumns": "106,100,98,95,115,110,95,95,124,124,110,126,132,127,127,104,89,133,88,99,109,104,95,112,106,114,114,102,107,119,119,110,110,86,81,96,132,147,86", "endOffsets": "940,1041,1140,1236,1352,1463,1559,1655,1780,1905,2016,2143,2276,2404,2532,2637,2727,2861,2950,3050,3160,3265,3361,3474,3581,3696,3811,3914,4022,4142,4262,4373,4484,4571,4653,4750,4883,5031,5118"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,408,509,611,709,5123", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "198,300,403,504,606,704,833,5219"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-sq_values-sq.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,404,501,609,720,5295", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "199,301,399,496,604,715,837,5391"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,505,631,746,842,932,1073,1193,1306,1435,1596,1727,1856,1965,2056,2200,2289,2398,2508,2611,2704,2822,2939,3055,3169,3280,3395,3523,3650,3774,3898,3985,4068,4171,4309,4463", "endColumns": "106,100,95,95,125,114,95,89,140,119,112,128,160,130,128,108,90,143,88,108,109,102,92,117,116,115,113,110,114,127,126,123,123,86,82,102,137,153,94", "endOffsets": "207,308,404,500,626,741,837,927,1068,1188,1301,1430,1591,1722,1851,1960,2051,2195,2284,2393,2503,2606,2699,2817,2934,3050,3164,3275,3390,3518,3645,3769,3893,3980,4063,4166,4304,4458,4553"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,949,1050,1146,1242,1368,1483,1579,1669,1810,1930,2043,2172,2333,2464,2593,2702,2793,2937,3026,3135,3245,3348,3441,3559,3676,3792,3906,4017,4132,4260,4387,4511,4635,4722,4805,4908,5046,5200", "endColumns": "106,100,95,95,125,114,95,89,140,119,112,128,160,130,128,108,90,143,88,108,109,102,92,117,116,115,113,110,114,127,126,123,123,86,82,102,137,153,94", "endOffsets": "944,1045,1141,1237,1363,1478,1574,1664,1805,1925,2038,2167,2328,2459,2588,2697,2788,2932,3021,3130,3240,3343,3436,3554,3671,3787,3901,4012,4127,4255,4382,4506,4630,4717,4800,4903,5041,5195,5290"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-v24_values-v24.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\d1cb9debee0ec7aff0e9116da1dc9922\\transformed\\media-1.0.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,121,182,248", "endColumns": "65,60,65,66", "endOffsets": "116,177,243,310"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ldrtl-v17_values-ldrtl-v17.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ldrtl-v17\\values-ldrtl-v17.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,178", "endColumns": "77,44,46", "endOffsets": "128,173,220"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-fr-rCA_values-fr-rCA.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,404,506,610,714,5409", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "198,300,399,501,605,709,823,5505"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,631,746,845,939,1071,1199,1305,1429,1607,1743,1875,1982,2073,2209,2298,2408,2517,2626,2727,2846,2972,3099,3222,3339,3465,3600,3741,3867,3999,4086,4174,4282,4426,4590", "endColumns": "106,100,98,95,122,114,98,93,131,127,105,123,177,135,131,106,90,135,88,109,108,108,100,118,125,126,122,116,125,134,140,125,131,86,87,107,143,163,95", "endOffsets": "207,308,407,503,626,741,840,934,1066,1194,1300,1424,1602,1738,1870,1977,2068,2204,2293,2403,2512,2621,2722,2841,2967,3094,3217,3334,3460,3595,3736,3862,3994,4081,4169,4277,4421,4585,4681"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "828,935,1036,1135,1231,1354,1469,1568,1662,1794,1922,2028,2152,2330,2466,2598,2705,2796,2932,3021,3131,3240,3349,3450,3569,3695,3822,3945,4062,4188,4323,4464,4590,4722,4809,4897,5005,5149,5313", "endColumns": "106,100,98,95,122,114,98,93,131,127,105,123,177,135,131,106,90,135,88,109,108,108,100,118,125,126,122,116,125,134,140,125,131,86,87,107,143,163,95", "endOffsets": "930,1031,1130,1226,1349,1464,1563,1657,1789,1917,2023,2147,2325,2461,2593,2700,2791,2927,3016,3126,3235,3344,3445,3564,3690,3817,3940,4057,4183,4318,4459,4585,4717,4804,4892,5000,5144,5308,5404"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-en-rAU_values-en-rAU.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,501,605,708,5045", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "196,298,397,496,600,703,819,5141"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,505,624,736,832,928,1059,1188,1293,1414,1542,1663,1782,1887,1978,2106,2195,2296,2399,2500,2593,2703,2809,2920,3029,3128,3235,3345,3461,3567,3679,3766,3850,3950,4085,4236", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "207,308,406,500,619,731,827,923,1054,1183,1288,1409,1537,1658,1777,1882,1973,2101,2190,2291,2394,2495,2588,2698,2804,2915,3024,3123,3230,3340,3456,3562,3674,3761,3845,3945,4080,4231,4321"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "824,931,1032,1130,1224,1343,1455,1551,1647,1778,1907,2012,2133,2261,2382,2501,2606,2697,2825,2914,3015,3118,3219,3312,3422,3528,3639,3748,3847,3954,4064,4180,4286,4398,4485,4569,4669,4804,4955", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "926,1027,1125,1219,1338,1450,1546,1642,1773,1902,2007,2128,2256,2377,2496,2601,2692,2820,2909,3010,3113,3214,3307,3417,3523,3634,3743,3842,3949,4059,4175,4281,4393,4480,4564,4664,4799,4950,5040"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-et_values-et.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,502,623,737,837,930,1052,1172,1275,1394,1550,1679,1806,1916,2007,2132,2222,2324,2432,2533,2631,2746,2859,2993,3125,3243,3364,3478,3596,3706,3820,3907,3991,4094,4241,4407", "endColumns": "106,100,94,93,120,113,99,92,121,119,102,118,155,128,126,109,90,124,89,101,107,100,97,114,112,133,131,117,120,113,117,109,113,86,83,102,146,165,90", "endOffsets": "207,308,403,497,618,732,832,925,1047,1167,1270,1389,1545,1674,1801,1911,2002,2127,2217,2319,2427,2528,2626,2741,2854,2988,3120,3238,3359,3473,3591,3701,3815,3902,3986,4089,4236,4402,4493"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "834,941,1042,1137,1231,1352,1466,1566,1659,1781,1901,2004,2123,2279,2408,2535,2645,2736,2861,2951,3053,3161,3262,3360,3475,3588,3722,3854,3972,4093,4207,4325,4435,4549,4636,4720,4823,4970,5136", "endColumns": "106,100,94,93,120,113,99,92,121,119,102,118,155,128,126,109,90,124,89,101,107,100,97,114,112,133,131,117,120,113,117,109,113,86,83,102,146,165,90", "endOffsets": "936,1037,1132,1226,1347,1461,1561,1654,1776,1896,1999,2118,2274,2403,2530,2640,2731,2856,2946,3048,3156,3257,3355,3470,3583,3717,3849,3967,4088,4202,4320,4430,4544,4631,4715,4818,4965,5131,5222"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,400,503,609,714,5227", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "195,297,395,498,604,709,829,5323"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-fr_values-fr.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,404,506,610,714,5351", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "198,300,399,501,605,709,827,5447"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,631,746,846,940,1072,1200,1306,1431,1608,1736,1860,1967,2058,2191,2283,2393,2507,2623,2724,2845,2971,3106,3237,3353,3475,3588,3714,3818,3935,4022,4110,4218,4362,4526", "endColumns": "106,100,98,95,122,114,99,93,131,127,105,124,176,127,123,106,90,132,91,109,113,115,100,120,125,134,130,115,121,112,125,103,116,86,87,107,143,163,97", "endOffsets": "207,308,407,503,626,741,841,935,1067,1195,1301,1426,1603,1731,1855,1962,2053,2186,2278,2388,2502,2618,2719,2840,2966,3101,3232,3348,3470,3583,3709,3813,3930,4017,4105,4213,4357,4521,4619"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,939,1040,1139,1235,1358,1473,1573,1667,1799,1927,2033,2158,2335,2463,2587,2694,2785,2918,3010,3120,3234,3350,3451,3572,3698,3833,3964,4080,4202,4315,4441,4545,4662,4749,4837,4945,5089,5253", "endColumns": "106,100,98,95,122,114,99,93,131,127,105,124,176,127,123,106,90,132,91,109,113,115,100,120,125,134,130,115,121,112,125,103,116,86,87,107,143,163,97", "endOffsets": "934,1035,1134,1230,1353,1468,1568,1662,1794,1922,2028,2153,2330,2458,2582,2689,2780,2913,3005,3115,3229,3345,3446,3567,3693,3828,3959,4075,4197,4310,4436,4540,4657,4744,4832,4940,5084,5248,5346"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-bg_values-bg.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,501,638,752,850,947,1079,1208,1318,1456,1620,1754,1885,1990,2094,2234,2334,2444,2549,2665,2768,2900,3022,3147,3269,3380,3495,3611,3737,3848,3969,4056,4141,4240,4381,4536", "endColumns": "106,100,95,91,136,113,97,96,131,128,109,137,163,133,130,104,103,139,99,109,104,115,102,131,121,124,121,110,114,115,125,110,120,86,84,98,140,154,95", "endOffsets": "207,308,404,496,633,747,845,942,1074,1203,1313,1451,1615,1749,1880,1985,2089,2229,2329,2439,2544,2660,2763,2895,3017,3142,3264,3375,3490,3606,3732,3843,3964,4051,4136,4235,4376,4531,4627"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "846,953,1054,1150,1242,1379,1493,1591,1688,1820,1949,2059,2197,2361,2495,2626,2731,2835,2975,3075,3185,3290,3406,3509,3641,3763,3888,4010,4121,4236,4352,4478,4589,4710,4797,4882,4981,5122,5277", "endColumns": "106,100,95,91,136,113,97,96,131,128,109,137,163,133,130,104,103,139,99,109,104,115,102,131,121,124,121,110,114,115,125,110,120,86,84,98,140,154,95", "endOffsets": "948,1049,1145,1237,1374,1488,1586,1683,1815,1944,2054,2192,2356,2490,2621,2726,2830,2970,3070,3180,3285,3401,3504,3636,3758,3883,4005,4116,4231,4347,4473,4584,4705,4792,4877,4976,5117,5272,5368"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,312,414,515,622,727,5373", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "197,307,409,510,617,722,841,5469"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-mn_values-mn.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,415,510,633,744,842,935,1072,1204,1315,1442,1592,1722,1847,1952,2049,2185,2279,2384,2496,2600,2693,2803,2919,3038,3152,3259,3369,3495,3618,3743,3864,3951,4033,4138,4271,4427", "endColumns": "106,100,101,94,122,110,97,92,136,131,110,126,149,129,124,104,96,135,93,104,111,103,92,109,115,118,113,106,109,125,122,124,120,86,81,104,132,155,90", "endOffsets": "207,308,410,505,628,739,837,930,1067,1199,1310,1437,1587,1717,1842,1947,2044,2180,2274,2379,2491,2595,2688,2798,2914,3033,3147,3254,3364,3490,3613,3738,3859,3946,4028,4133,4266,4422,4513"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,947,1048,1150,1245,1368,1479,1577,1670,1807,1939,2050,2177,2327,2457,2582,2687,2784,2920,3014,3119,3231,3335,3428,3538,3654,3773,3887,3994,4104,4230,4353,4478,4599,4686,4768,4873,5006,5162", "endColumns": "106,100,101,94,122,110,97,92,136,131,110,126,149,129,124,104,96,135,93,104,111,103,92,109,115,118,113,106,109,125,122,124,120,86,81,104,132,155,90", "endOffsets": "942,1043,1145,1240,1363,1474,1572,1665,1802,1934,2045,2172,2322,2452,2577,2682,2779,2915,3009,3114,3226,3330,3423,3533,3649,3768,3882,3989,4099,4225,4348,4473,4594,4681,4763,4868,5001,5157,5248"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,406,504,609,721,5253", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "198,300,401,499,604,716,835,5349"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-as_values-as.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,206,309,417,522,626,726,5251", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "201,304,412,517,621,721,850,5347"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,415,509,633,745,844,938,1064,1189,1295,1421,1572,1691,1818,1921,2013,2145,2238,2349,2462,2572,2671,2787,2909,3024,3138,3248,3358,3478,3597,3713,3828,3915,3999,4105,4244,4401", "endColumns": "106,100,101,93,123,111,98,93,125,124,105,125,150,118,126,102,91,131,92,110,112,109,98,115,121,114,113,109,109,119,118,115,114,86,83,105,138,156,99", "endOffsets": "207,308,410,504,628,740,839,933,1059,1184,1290,1416,1567,1686,1813,1916,2008,2140,2233,2344,2457,2567,2666,2782,2904,3019,3133,3243,3353,3473,3592,3708,3823,3910,3994,4100,4239,4396,4496"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "855,962,1063,1165,1259,1383,1495,1594,1688,1814,1939,2045,2171,2322,2441,2568,2671,2763,2895,2988,3099,3212,3322,3421,3537,3659,3774,3888,3998,4108,4228,4347,4463,4578,4665,4749,4855,4994,5151", "endColumns": "106,100,101,93,123,111,98,93,125,124,105,125,150,118,126,102,91,131,92,110,112,109,98,115,121,114,113,109,109,119,118,115,114,86,83,105,138,156,99", "endOffsets": "957,1058,1160,1254,1378,1490,1589,1683,1809,1934,2040,2166,2317,2436,2563,2666,2758,2890,2983,3094,3207,3317,3416,3532,3654,3769,3883,3993,4103,4223,4342,4458,4573,4660,4744,4850,4989,5146,5246"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-nl_values-nl.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,508,626,736,836,932,1064,1194,1301,1424,1585,1713,1839,1943,2037,2165,2258,2363,2468,2571,2670,2786,2917,3033,3147,3250,3355,3487,3610,3735,3851,3938,4022,4126,4261,4416", "endColumns": "106,100,97,96,117,109,99,95,131,129,106,122,160,127,125,103,93,127,92,104,104,102,98,115,130,115,113,102,104,131,122,124,115,86,83,103,134,154,88", "endOffsets": "207,308,406,503,621,731,831,927,1059,1189,1296,1419,1580,1708,1834,1938,2032,2160,2253,2358,2463,2566,2665,2781,2912,3028,3142,3245,3350,3482,3605,3730,3846,3933,4017,4121,4256,4411,4500"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "839,946,1047,1145,1242,1360,1470,1570,1666,1798,1928,2035,2158,2319,2447,2573,2677,2771,2899,2992,3097,3202,3305,3404,3520,3651,3767,3881,3984,4089,4221,4344,4469,4585,4672,4756,4860,4995,5150", "endColumns": "106,100,97,96,117,109,99,95,131,129,106,122,160,127,125,103,93,127,92,104,104,102,98,115,130,115,113,102,104,131,122,124,115,86,83,103,134,154,88", "endOffsets": "941,1042,1140,1237,1355,1465,1565,1661,1793,1923,2030,2153,2314,2442,2568,2672,2766,2894,2987,3092,3197,3300,3399,3515,3646,3762,3876,3979,4084,4216,4339,4464,4580,4667,4751,4855,4990,5145,5234"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,309,409,509,616,720,5239", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "202,304,404,504,611,715,834,5335"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-th_values-th.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,304,402,500,603,708,5070", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "196,299,397,495,598,703,815,5166"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,505,632,741,838,931,1054,1177,1283,1405,1531,1647,1763,1877,1975,2102,2191,2296,2398,2507,2601,2712,2819,2932,3045,3152,3266,3380,3493,3602,3710,3797,3880,3978,4112,4261", "endColumns": "106,100,94,96,126,108,96,92,122,122,105,121,125,115,115,113,97,126,88,104,101,108,93,110,106,112,112,106,113,113,112,108,107,86,82,97,133,148,93", "endOffsets": "207,308,403,500,627,736,833,926,1049,1172,1278,1400,1526,1642,1758,1872,1970,2097,2186,2291,2393,2502,2596,2707,2814,2927,3040,3147,3261,3375,3488,3597,3705,3792,3875,3973,4107,4256,4350"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "820,927,1028,1123,1220,1347,1456,1553,1646,1769,1892,1998,2120,2246,2362,2478,2592,2690,2817,2906,3011,3113,3222,3316,3427,3534,3647,3760,3867,3981,4095,4208,4317,4425,4512,4595,4693,4827,4976", "endColumns": "106,100,94,96,126,108,96,92,122,122,105,121,125,115,115,113,97,126,88,104,101,108,93,110,106,112,112,106,113,113,112,108,107,86,82,97,133,148,93", "endOffsets": "922,1023,1118,1215,1342,1451,1548,1641,1764,1887,1993,2115,2241,2357,2473,2587,2685,2812,2901,3006,3108,3217,3311,3422,3529,3642,3755,3862,3976,4090,4203,4312,4420,4507,4590,4688,4822,4971,5065"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-hy_values-hy.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,508,638,754,853,943,1066,1188,1290,1409,1567,1687,1806,1919,2013,2143,2237,2341,2440,2542,2637,2750,2876,2996,3115,3220,3329,3445,3565,3678,3795,3882,3967,4073,4208,4365", "endColumns": "106,100,99,94,129,115,98,89,122,121,101,118,157,119,118,112,93,129,93,103,98,101,94,112,125,119,118,104,108,115,119,112,116,86,84,105,134,156,83", "endOffsets": "207,308,408,503,633,749,848,938,1061,1183,1285,1404,1562,1682,1801,1914,2008,2138,2232,2336,2435,2537,2632,2745,2871,2991,3110,3215,3324,3440,3560,3673,3790,3877,3962,4068,4203,4360,4444"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "825,932,1033,1133,1228,1358,1474,1573,1663,1786,1908,2010,2129,2287,2407,2526,2639,2733,2863,2957,3061,3160,3262,3357,3470,3596,3716,3835,3940,4049,4165,4285,4398,4515,4602,4687,4793,4928,5085", "endColumns": "106,100,99,94,129,115,98,89,122,121,101,118,157,119,118,112,93,129,93,103,98,101,94,112,125,119,118,104,108,115,119,112,116,86,84,105,134,156,83", "endOffsets": "927,1028,1128,1223,1353,1469,1568,1658,1781,1903,2005,2124,2282,2402,2521,2634,2728,2858,2952,3056,3155,3257,3352,3465,3591,3711,3830,3935,4044,4160,4280,4393,4510,4597,4682,4788,4923,5080,5164"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,310,408,507,612,714,5169", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "200,305,403,502,607,709,820,5265"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-sv_values-sv.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,506,626,738,835,929,1054,1176,1286,1407,1557,1681,1802,1908,1999,2124,2218,2321,2425,2526,2627,2745,2854,2966,3072,3178,3293,3404,3521,3630,3745,3832,3913,4020,4154,4309", "endColumns": "106,100,97,94,119,111,96,93,124,121,109,120,149,123,120,105,90,124,93,102,103,100,100,117,108,111,105,105,114,110,116,108,114,86,80,106,133,154,85", "endOffsets": "207,308,406,501,621,733,830,924,1049,1171,1281,1402,1552,1676,1797,1903,1994,2119,2213,2316,2420,2521,2622,2740,2849,2961,3067,3173,3288,3399,3516,3625,3740,3827,3908,4015,4149,4304,4390"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "833,940,1041,1139,1234,1354,1466,1563,1657,1782,1904,2014,2135,2285,2409,2530,2636,2727,2852,2946,3049,3153,3254,3355,3473,3582,3694,3800,3906,4021,4132,4249,4358,4473,4560,4641,4748,4882,5037", "endColumns": "106,100,97,94,119,111,96,93,124,121,109,120,149,123,120,105,90,124,93,102,103,100,100,117,108,111,105,105,114,110,116,108,114,86,80,106,133,154,85", "endOffsets": "935,1036,1134,1229,1349,1461,1558,1652,1777,1899,2009,2130,2280,2404,2525,2631,2722,2847,2941,3044,3148,3249,3350,3468,3577,3689,3795,3901,4016,4127,4244,4353,4468,4555,4636,4743,4877,5032,5118"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,400,499,607,712,5123", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "195,297,395,494,602,707,828,5219"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-tr_values-tr.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,402,499,601,707,5202", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "197,299,397,494,596,702,813,5298"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,499,618,729,828,918,1052,1180,1282,1400,1552,1695,1832,1939,2033,2165,2255,2361,2473,2579,2674,2786,2902,3027,3146,3250,3357,3473,3587,3699,3809,3896,3985,4095,4237,4400", "endColumns": "106,100,94,90,118,110,98,89,133,127,101,117,151,142,136,106,93,131,89,105,111,105,94,111,115,124,118,103,106,115,113,111,109,86,88,109,141,162,88", "endOffsets": "207,308,403,494,613,724,823,913,1047,1175,1277,1395,1547,1690,1827,1934,2028,2160,2250,2356,2468,2574,2669,2781,2897,3022,3141,3245,3352,3468,3582,3694,3804,3891,3980,4090,4232,4395,4484"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "818,925,1026,1121,1212,1331,1442,1541,1631,1765,1893,1995,2113,2265,2408,2545,2652,2746,2878,2968,3074,3186,3292,3387,3499,3615,3740,3859,3963,4070,4186,4300,4412,4522,4609,4698,4808,4950,5113", "endColumns": "106,100,94,90,118,110,98,89,133,127,101,117,151,142,136,106,93,131,89,105,111,105,94,111,115,124,118,103,106,115,113,111,109,86,88,109,141,162,88", "endOffsets": "920,1021,1116,1207,1326,1437,1536,1626,1760,1888,1990,2108,2260,2403,2540,2647,2741,2873,2963,3069,3181,3287,3382,3494,3610,3735,3854,3958,4065,4181,4295,4407,4517,4604,4693,4803,4945,5108,5197"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ml_values-ml.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,310,412,516,619,720,5465", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "202,305,407,511,614,715,837,5561"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,511,639,749,849,944,1089,1233,1353,1489,1655,1791,1926,2039,2148,2298,2395,2505,2618,2727,2832,2951,3076,3209,3341,3451,3568,3691,3814,3937,4060,4147,4231,4340,4475,4635", "endColumns": "106,100,95,101,127,109,99,94,144,143,119,135,165,135,134,112,108,149,96,109,112,108,104,118,124,132,131,109,116,122,122,122,122,86,83,108,134,159,92", "endOffsets": "207,308,404,506,634,744,844,939,1084,1228,1348,1484,1650,1786,1921,2034,2143,2293,2390,2500,2613,2722,2827,2946,3071,3204,3336,3446,3563,3686,3809,3932,4055,4142,4226,4335,4470,4630,4723"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,949,1050,1146,1248,1376,1486,1586,1681,1826,1970,2090,2226,2392,2528,2663,2776,2885,3035,3132,3242,3355,3464,3569,3688,3813,3946,4078,4188,4305,4428,4551,4674,4797,4884,4968,5077,5212,5372", "endColumns": "106,100,95,101,127,109,99,94,144,143,119,135,165,135,134,112,108,149,96,109,112,108,104,118,124,132,131,109,116,122,122,122,122,86,83,108,134,159,92", "endOffsets": "944,1045,1141,1243,1371,1481,1581,1676,1821,1965,2085,2221,2387,2523,2658,2771,2880,3030,3127,3237,3350,3459,3564,3683,3808,3941,4073,4183,4300,4423,4546,4669,4792,4879,4963,5072,5207,5367,5460"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-nb_values-nb.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,497,605,711,5192", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "194,296,393,492,600,706,826,5288"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,506,624,736,833,928,1068,1207,1313,1435,1585,1705,1824,1933,2032,2157,2250,2352,2460,2560,2659,2775,2883,3019,3154,3258,3368,3485,3601,3711,3820,3907,3988,4089,4223,4377", "endColumns": "106,100,97,94,117,111,96,94,139,138,105,121,149,119,118,108,98,124,92,101,107,99,98,115,107,135,134,103,109,116,115,109,108,86,80,100,133,153,88", "endOffsets": "207,308,406,501,619,731,828,923,1063,1202,1308,1430,1580,1700,1819,1928,2027,2152,2245,2347,2455,2555,2654,2770,2878,3014,3149,3253,3363,3480,3596,3706,3815,3902,3983,4084,4218,4372,4461"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,938,1039,1137,1232,1350,1462,1559,1654,1794,1933,2039,2161,2311,2431,2550,2659,2758,2883,2976,3078,3186,3286,3385,3501,3609,3745,3880,3984,4094,4211,4327,4437,4546,4633,4714,4815,4949,5103", "endColumns": "106,100,97,94,117,111,96,94,139,138,105,121,149,119,118,108,98,124,92,101,107,99,98,115,107,135,134,103,109,116,115,109,108,86,80,100,133,153,88", "endOffsets": "933,1034,1132,1227,1345,1457,1554,1649,1789,1928,2034,2156,2306,2426,2545,2654,2753,2878,2971,3073,3181,3281,3380,3496,3604,3740,3875,3979,4089,4206,4322,4432,4541,4628,4709,4810,4944,5098,5187"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-fa_values-fa.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,501,619,727,823,919,1045,1167,1273,1397,1548,1672,1792,1900,1989,2119,2207,2307,2413,2516,2611,2725,2843,2962,3077,3181,3289,3400,3515,3624,3737,3824,3907,4012,4147,4296", "endColumns": "106,100,94,92,117,107,95,95,125,121,105,123,150,123,119,107,88,129,87,99,105,102,94,113,117,118,114,103,107,110,114,108,112,86,82,104,134,148,88", "endOffsets": "207,308,403,496,614,722,818,914,1040,1162,1268,1392,1543,1667,1787,1895,1984,2114,2202,2302,2408,2511,2606,2720,2838,2957,3072,3176,3284,3395,3510,3619,3732,3819,3902,4007,4142,4291,4380"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "829,936,1037,1132,1225,1343,1451,1547,1643,1769,1891,1997,2121,2272,2396,2516,2624,2713,2843,2931,3031,3137,3240,3335,3449,3567,3686,3801,3905,4013,4124,4239,4348,4461,4548,4631,4736,4871,5020", "endColumns": "106,100,94,92,117,107,95,95,125,121,105,123,150,123,119,107,88,129,87,99,105,102,94,113,117,118,114,103,107,110,114,108,112,86,82,104,134,148,88", "endOffsets": "931,1032,1127,1220,1338,1446,1542,1638,1764,1886,1992,2116,2267,2391,2511,2619,2708,2838,2926,3026,3132,3235,3330,3444,3562,3681,3796,3900,4008,4119,4234,4343,4456,4543,4626,4731,4866,5015,5104"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,405,505,606,712,5109", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "199,301,400,500,601,707,824,5205"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ur_values-ur.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,510,635,745,840,934,1063,1187,1299,1430,1566,1691,1811,1919,2015,2150,2241,2347,2456,2561,2661,2781,2889,3002,3110,3213,3321,3462,3607,3746,3889,3976,4063,4169,4307,4464", "endColumns": "106,100,99,96,124,109,94,93,128,123,111,130,135,124,119,107,95,134,90,105,108,104,99,119,107,112,107,102,107,140,144,138,142,86,86,105,137,156,92", "endOffsets": "207,308,408,505,630,740,835,929,1058,1182,1294,1425,1561,1686,1806,1914,2010,2145,2236,2342,2451,2556,2656,2776,2884,2997,3105,3208,3316,3457,3602,3741,3884,3971,4058,4164,4302,4459,4552"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "826,933,1034,1134,1231,1356,1466,1561,1655,1784,1908,2020,2151,2287,2412,2532,2640,2736,2871,2962,3068,3177,3282,3382,3502,3610,3723,3831,3934,4042,4183,4328,4467,4610,4697,4784,4890,5028,5185", "endColumns": "106,100,99,96,124,109,94,93,128,123,111,130,135,124,119,107,95,134,90,105,108,104,99,119,107,112,107,102,107,140,144,138,142,86,86,105,137,156,92", "endOffsets": "928,1029,1129,1226,1351,1461,1556,1650,1779,1903,2015,2146,2282,2407,2527,2635,2731,2866,2957,3063,3172,3277,3377,3497,3605,3718,3826,3929,4037,4178,4323,4462,4605,4692,4779,4885,5023,5180,5273"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,407,511,614,712,5278", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "198,300,402,506,609,707,821,5374"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ms_values-ms.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,506,627,737,839,931,1063,1192,1296,1416,1568,1693,1815,1921,2011,2140,2229,2331,2435,2536,2629,2739,2850,2962,3071,3179,3291,3406,3525,3632,3743,3830,3912,4023,4156,4311", "endColumns": "106,100,97,94,120,109,101,91,131,128,103,119,151,124,121,105,89,128,88,101,103,100,92,109,110,111,108,107,111,114,118,106,110,86,81,110,132,154,91", "endOffsets": "207,308,406,501,622,732,834,926,1058,1187,1291,1411,1563,1688,1810,1916,2006,2135,2224,2326,2430,2531,2624,2734,2845,2957,3066,3174,3286,3401,3520,3627,3738,3825,3907,4018,4151,4306,4398"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "848,955,1056,1154,1249,1370,1480,1582,1674,1806,1935,2039,2159,2311,2436,2558,2664,2754,2883,2972,3074,3178,3279,3372,3482,3593,3705,3814,3922,4034,4149,4268,4375,4486,4573,4655,4766,4899,5054", "endColumns": "106,100,97,94,120,109,101,91,131,128,103,119,151,124,121,105,89,128,88,101,103,100,92,109,110,111,108,107,111,114,118,106,110,86,81,110,132,154,91", "endOffsets": "950,1051,1149,1244,1365,1475,1577,1669,1801,1930,2034,2154,2306,2431,2553,2659,2749,2878,2967,3069,3173,3274,3367,3477,3588,3700,3809,3917,4029,4144,4263,4370,4481,4568,4650,4761,4894,5049,5141"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,399,509,615,733,5146", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "195,297,394,504,610,728,843,5242"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-hr_values-hr.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,504,630,748,845,937,1060,1180,1287,1410,1553,1680,1804,1908,1999,2129,2221,2322,2429,2532,2626,2737,2850,2978,3103,3213,3328,3445,3563,3677,3792,3879,3967,4084,4220,4388", "endColumns": "106,100,96,93,125,117,96,91,122,119,106,122,142,126,123,103,90,129,91,100,106,102,93,110,112,127,124,109,114,116,117,113,114,86,87,116,135,167,96", "endOffsets": "207,308,405,499,625,743,840,932,1055,1175,1282,1405,1548,1675,1799,1903,1994,2124,2216,2317,2424,2527,2621,2732,2845,2973,3098,3208,3323,3440,3558,3672,3787,3874,3962,4079,4215,4383,4480"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,938,1039,1136,1230,1356,1474,1571,1663,1786,1906,2013,2136,2279,2406,2530,2634,2725,2855,2947,3048,3155,3258,3352,3463,3576,3704,3829,3939,4054,4171,4289,4403,4518,4605,4693,4810,4946,5114", "endColumns": "106,100,96,93,125,117,96,91,122,119,106,122,142,126,123,103,90,129,91,100,106,102,93,110,112,127,124,109,114,116,117,113,114,86,87,116,135,167,96", "endOffsets": "933,1034,1131,1225,1351,1469,1566,1658,1781,1901,2008,2131,2274,2401,2525,2629,2720,2850,2942,3043,3150,3253,3347,3458,3571,3699,3824,3934,4049,4166,4284,4398,4513,4600,4688,4805,4941,5109,5206"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,310,407,506,610,714,5211", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "198,305,402,501,605,709,826,5307"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ta_values-ta.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,501,637,751,873,966,1096,1224,1332,1459,1628,1747,1864,1972,2069,2211,2302,2420,2537,2657,2752,2874,2998,3111,3220,3326,3437,3563,3695,3823,3957,4044,4127,4224,4361,4512", "endColumns": "106,100,95,91,135,113,121,92,129,127,107,126,168,118,116,107,96,141,90,117,116,119,94,121,123,112,108,105,110,125,131,127,133,86,82,96,136,150,87", "endOffsets": "207,308,404,496,632,746,868,961,1091,1219,1327,1454,1623,1742,1859,1967,2064,2206,2297,2415,2532,2652,2747,2869,2993,3106,3215,3321,3432,3558,3690,3818,3952,4039,4122,4219,4356,4507,4595"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "851,958,1059,1155,1247,1383,1497,1619,1712,1842,1970,2078,2205,2374,2493,2610,2718,2815,2957,3048,3166,3283,3403,3498,3620,3744,3857,3966,4072,4183,4309,4441,4569,4703,4790,4873,4970,5107,5258", "endColumns": "106,100,95,91,135,113,121,92,129,127,107,126,168,118,116,107,96,141,90,117,116,119,94,121,123,112,108,105,110,125,131,127,133,86,82,96,136,150,87", "endOffsets": "953,1054,1150,1242,1378,1492,1614,1707,1837,1965,2073,2200,2369,2488,2605,2713,2810,2952,3043,3161,3278,3398,3493,3615,3739,3852,3961,4067,4178,4304,4436,4564,4698,4785,4868,4965,5102,5253,5341"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,304,403,501,608,723,5346", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "196,299,398,496,603,718,846,5442"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-iw_values-iw.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,495,596,696,5085", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "194,296,393,490,591,691,797,5181"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,319,413,505,627,735,830,928,1047,1165,1268,1391,1532,1650,1767,1873,1964,2089,2179,2282,2390,2497,2594,2712,2815,2922,3028,3136,3250,3372,3496,3613,3732,3819,3902,4008,4145,4300", "endColumns": "109,103,93,91,121,107,94,97,118,117,102,122,140,117,116,105,90,124,89,102,107,106,96,117,102,106,105,107,113,121,123,116,118,86,82,105,136,154,87", "endOffsets": "210,314,408,500,622,730,825,923,1042,1160,1263,1386,1527,1645,1762,1868,1959,2084,2174,2277,2385,2492,2589,2707,2810,2917,3023,3131,3245,3367,3491,3608,3727,3814,3897,4003,4140,4295,4383"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "802,912,1016,1110,1202,1324,1432,1527,1625,1744,1862,1965,2088,2229,2347,2464,2570,2661,2786,2876,2979,3087,3194,3291,3409,3512,3619,3725,3833,3947,4069,4193,4310,4429,4516,4599,4705,4842,4997", "endColumns": "109,103,93,91,121,107,94,97,118,117,102,122,140,117,116,105,90,124,89,102,107,106,96,117,102,106,105,107,113,121,123,116,118,86,82,105,136,154,87", "endOffsets": "907,1011,1105,1197,1319,1427,1522,1620,1739,1857,1960,2083,2224,2342,2459,2565,2656,2781,2871,2974,3082,3189,3286,3404,3507,3614,3720,3828,3942,4064,4188,4305,4424,4511,4594,4700,4837,4992,5080"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-ro_values-ro.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,511,639,753,852,945,1075,1201,1316,1450,1615,1744,1869,1979,2076,2208,2299,2404,2507,2611,2713,2835,2949,3081,3209,3325,3446,3563,3686,3798,3916,4003,4088,4195,4331,4489", "endColumns": "106,100,99,97,127,113,98,92,129,125,114,133,164,128,124,109,96,131,90,104,102,103,101,121,113,131,127,115,120,116,122,111,117,86,84,106,135,157,95", "endOffsets": "207,308,408,506,634,748,847,940,1070,1196,1311,1445,1610,1739,1864,1974,2071,2203,2294,2399,2502,2606,2708,2830,2944,3076,3204,3320,3441,3558,3681,3793,3911,3998,4083,4190,4326,4484,4580"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,939,1040,1140,1238,1366,1480,1579,1672,1802,1928,2043,2177,2342,2471,2596,2706,2803,2935,3026,3131,3234,3338,3440,3562,3676,3808,3936,4052,4173,4290,4413,4525,4643,4730,4815,4922,5058,5216", "endColumns": "106,100,99,97,127,113,98,92,129,125,114,133,164,128,124,109,96,131,90,104,102,103,101,121,113,131,127,115,120,116,122,111,117,86,84,106,135,157,95", "endOffsets": "934,1035,1135,1233,1361,1475,1574,1667,1797,1923,2038,2172,2337,2466,2591,2701,2798,2930,3021,3126,3229,3333,3435,3557,3671,3803,3931,4047,4168,4285,4408,4520,4638,4725,4810,4917,5053,5211,5307"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,504,606,715,5312", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "198,300,400,499,601,710,827,5408"}}]}, {"outputFile": "com.example.myapplicationtv.app-release-31:/values-be_values-be.arsc.flat", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,506,612,715,5313", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "198,300,400,501,607,710,831,5409"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,510,638,749,846,937,1067,1195,1304,1429,1587,1712,1835,1941,2032,2169,2263,2367,2478,2592,2689,2808,2933,3051,3167,3288,3414,3536,3670,3787,3916,4003,4086,4194,4334,4500", "endColumns": "106,100,99,96,127,110,96,90,129,127,108,124,157,124,122,105,90,136,93,103,110,113,96,118,124,117,115,120,125,121,133,116,128,86,82,107,139,165,81", "endOffsets": "207,308,408,505,633,744,841,932,1062,1190,1299,1424,1582,1707,1830,1936,2027,2164,2258,2362,2473,2587,2684,2803,2928,3046,3162,3283,3409,3531,3665,3782,3911,3998,4081,4189,4329,4495,4577"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,943,1044,1144,1241,1369,1480,1577,1668,1798,1926,2035,2160,2318,2443,2566,2672,2763,2900,2994,3098,3209,3323,3420,3539,3664,3782,3898,4019,4145,4267,4401,4518,4647,4734,4817,4925,5065,5231", "endColumns": "106,100,99,96,127,110,96,90,129,127,108,124,157,124,122,105,90,136,93,103,110,113,96,118,124,117,115,120,125,121,133,116,128,86,82,107,139,165,81", "endOffsets": "938,1039,1139,1236,1364,1475,1572,1663,1793,1921,2030,2155,2313,2438,2561,2667,2758,2895,2989,3093,3204,3318,3415,3534,3659,3777,3893,4014,4140,4262,4396,4513,4642,4729,4812,4920,5060,5226,5308"}}]}]}