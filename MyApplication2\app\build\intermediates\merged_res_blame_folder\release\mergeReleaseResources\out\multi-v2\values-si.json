{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-si/values-si.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,310,415,520,619,723,5234", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "202,305,410,515,614,718,832,5330"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,415,514,630,739,834,928,1064,1199,1310,1440,1569,1693,1816,1925,2022,2163,2253,2361,2475,2581,2680,2796,2903,3013,3122,3228,3340,3466,3598,3722,3852,3939,4022,4123,4258,4410", "endColumns": "106,100,101,98,115,108,94,93,135,134,110,129,128,123,122,108,96,140,89,107,113,105,98,115,106,109,108,105,111,125,131,123,129,86,82,100,134,151,91", "endOffsets": "207,308,410,509,625,734,829,923,1059,1194,1305,1435,1564,1688,1811,1920,2017,2158,2248,2356,2470,2576,2675,2791,2898,3008,3117,3223,3335,3461,3593,3717,3847,3934,4017,4118,4253,4405,4497"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,944,1045,1147,1246,1362,1471,1566,1660,1796,1931,2042,2172,2301,2425,2548,2657,2754,2895,2985,3093,3207,3313,3412,3528,3635,3745,3854,3960,4072,4198,4330,4454,4584,4671,4754,4855,4990,5142", "endColumns": "106,100,101,98,115,108,94,93,135,134,110,129,128,123,122,108,96,140,89,107,113,105,98,115,106,109,108,105,111,125,131,123,129,86,82,100,134,151,91", "endOffsets": "939,1040,1142,1241,1357,1466,1561,1655,1791,1926,2037,2167,2296,2420,2543,2652,2749,2890,2980,3088,3202,3308,3407,3523,3630,3740,3849,3955,4067,4193,4325,4449,4579,4666,4749,4850,4985,5137,5229"}}]}]}