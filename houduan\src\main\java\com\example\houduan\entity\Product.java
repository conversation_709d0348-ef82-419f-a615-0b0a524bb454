package com.example.houduan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "product")
public class Product {

    @Id
    @Column(length = 32)
    private String id;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品主图URL
     */
    private String image;

    /**
     * 商品图片URLs(JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String images;

    /**
     * 商品分类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 库存数量
     */
    private Integer stock;

    /**
     * 销量
     */
    private Integer sales;

    /**
     * 评分(0.0-10.0)
     */
    private BigDecimal rating;

    /**
     * 评分人数
     */
    private Integer ratingCount;

    /**
     * 商品描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 商品规格(JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String specifications;

    /**
     * 是否推荐(0:否,1:是)
     */
    private Integer isRecommended;

    /**
     * 是否热销(0:否,1:是)
     */
    private Integer isHot;

    /**
     * 状态(0:下架,1:上架)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
