package com.example.houduan.dto;

import lombok.Data;

/**
 * 游戏查询请求DTO
 */
@Data
public class GameQueryRequest {

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页数量
     */
    private Integer size = 10;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 分类
     */
    private String category;

    /**
     * 排序方式(latest:最新, hot:热门, rating:评分, play:游玩次数)
     */
    private String sort = "latest";

    /**
     * 是否推荐
     */
    private Integer isRecommended;

    /**
     * 是否精选
     */
    private Integer isFeatured;

    /**
     * 状态
     */
    private Integer status = 1;
}
