package com.example.myapplicationtv.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.R
import com.example.myapplicationtv.model.ProductCategory

class ProductCategoryAdapter(
    private val onCategoryClick: (ProductCategory) -> Unit
) : RecyclerView.Adapter<ProductCategoryAdapter.CategoryViewHolder>() {

    private var categories = listOf<ProductCategory>()

    fun updateData(newCategories: List<ProductCategory>) {
        categories = newCategories
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_product_category, parent, false)
        return CategoryViewHolder(view)
    }

    override fun onBindViewHolder(holder: CategoryViewHolder, position: Int) {
        holder.bind(categories[position])
    }

    override fun getItemCount(): Int = categories.size

    inner class CategoryViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivCategoryIcon: ImageView = itemView.findViewById(R.id.iv_category_icon)
        private val tvCategoryName: TextView = itemView.findViewById(R.id.tv_category_name)

        fun bind(category: ProductCategory) {
            tvCategoryName.text = category.name
            
            // 根据分类设置图标
            val iconRes = when (category.name) {
                "服饰" -> R.drawable.ic_clothing
                "数码" -> R.drawable.ic_digital
                "家电" -> R.drawable.ic_appliance
                "食品" -> R.drawable.ic_food
                "图书" -> R.drawable.ic_book
                "运动" -> R.drawable.ic_sports
                else -> R.drawable.ic_shopping
            }
            ivCategoryIcon.setImageResource(iconRes)

            itemView.setOnClickListener {
                onCategoryClick(category)
            }
        }
    }
}
