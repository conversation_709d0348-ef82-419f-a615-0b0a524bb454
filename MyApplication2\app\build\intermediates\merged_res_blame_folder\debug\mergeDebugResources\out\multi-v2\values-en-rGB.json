{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,501,605,708,5045", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "196,298,397,496,600,703,819,5141"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,505,624,736,832,928,1059,1188,1293,1414,1542,1663,1782,1887,1978,2106,2195,2296,2399,2500,2593,2703,2809,2920,3029,3128,3235,3345,3461,3567,3679,3766,3850,3950,4085,4236", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "207,308,406,500,619,731,827,923,1054,1183,1288,1409,1537,1658,1777,1882,1973,2101,2190,2291,2394,2495,2588,2698,2804,2915,3024,3123,3230,3340,3456,3562,3674,3761,3845,3945,4080,4231,4321"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "824,931,1032,1130,1224,1343,1455,1551,1647,1778,1907,2012,2133,2261,2382,2501,2606,2697,2825,2914,3015,3118,3219,3312,3422,3528,3639,3748,3847,3954,4064,4180,4286,4398,4485,4569,4669,4804,4955", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "926,1027,1125,1219,1338,1450,1546,1642,1773,1902,2007,2128,2256,2377,2496,2601,2692,2820,2909,3010,3113,3214,3307,3417,3523,3634,3743,3842,3949,4059,4175,4281,4393,4480,4564,4664,4799,4950,5040"}}]}]}