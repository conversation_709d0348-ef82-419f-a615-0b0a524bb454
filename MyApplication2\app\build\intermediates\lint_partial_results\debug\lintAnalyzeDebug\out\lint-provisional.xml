<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="conditional_incidents">

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.ACCESS_NETWORK_STATE" message="Missing permissions required by ConnectivityManager.getActiveNetwork: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/TestHelper.kt"
            line="60"
            column="27"
            startOffset="1784"
            endLine="60"
            endColumn="60"
            endOffset="1817"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by ConnectivityManager.getActiveNetwork: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.ACCESS_NETWORK_STATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.ACCESS_NETWORK_STATE" message="Missing permissions required by ConnectivityManager.getNetworkCapabilities: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/TestHelper.kt"
            line="61"
            column="32"
            startOffset="1849"
            endLine="61"
            endColumn="83"
            endOffset="1900"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by ConnectivityManager.getNetworkCapabilities: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.ACCESS_NETWORK_STATE"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.ACCESS_NETWORK_STATE" message="Missing permissions required by ConnectivityManager.getActiveNetworkInfo: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/TestHelper.kt"
            line="71"
            column="31"
            startOffset="2374"
            endLine="71"
            endColumn="68"
            endOffset="2411"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by ConnectivityManager.getActiveNetworkInfo: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.ACCESS_NETWORK_STATE"/>
        </map>
    </incident>

    <incident
        id="GestureBackNavigation"
        severity="warning"
        message="If intercepting back events, this should be handled through the registration of callbacks on the window level; Please see https://developer.android.com/about/versions/13/features/predictive-back-gesture">
        <show-url
            description="Show https://developer.android.com/about/versions/13/features/predictive-back-gesture"
            url="https://developer.android.com/about/versions/13/features/predictive-back-gesture"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/base/BaseTVActivity.kt"
            line="25"
            column="13"
            startOffset="680"
            endLine="25"
            endColumn="34"
            endOffset="701"/>
    </incident>

    <incident
        id="GestureBackNavigation"
        severity="warning"
        message="If intercepting back events, this should be handled through the registration of callbacks on the window level; Please see https://developer.android.com/about/versions/13/features/predictive-back-gesture">
        <show-url
            description="Show https://developer.android.com/about/versions/13/features/predictive-back-gesture"
            url="https://developer.android.com/about/versions/13/features/predictive-back-gesture"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/myapplicationtv/utils/FocusHelper.kt"
            line="117"
            column="13"
            startOffset="3713"
            endLine="117"
            endColumn="34"
            endOffset="3734"/>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="21-∞" requiresApi="23-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="20"
            column="9"
            startOffset="689"
            endLine="20"
            endColumn="44"
            endOffset="724"/>
        <map>
            <entry
                name="message"
                string="Attribute `usesCleartextTraffic` is only used in API level 23 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <api-levels id="requiresApi"
                value="23-∞"/>
        </map>
    </incident>

</incidents>
