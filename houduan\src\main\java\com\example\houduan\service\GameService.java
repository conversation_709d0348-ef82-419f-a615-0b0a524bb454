package com.example.houduan.service;

import com.example.houduan.dto.GameQueryRequest;
import com.example.houduan.dto.GameResponse;
import com.example.houduan.common.PageResult;
import com.example.houduan.entity.Game;
import com.example.houduan.mapper.GameMapper;
import com.example.houduan.util.IdGenerator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 游戏服务类
 */
@Service
@RequiredArgsConstructor
public class GameService {

    private final GameMapper gameMapper;
    private final ObjectMapper objectMapper;

    /**
     * 分页查询游戏列表
     */
    public PageResult<GameResponse> getGameList(GameQueryRequest request) {
        // 构建排序
        Sort sort = buildSort(request.getSort());
        Pageable pageable = PageRequest.of(request.getPage() - 1, request.getSize(), sort);

        Page<Game> page;
        
        // 根据条件查询
        if (StringUtils.hasText(request.getKeyword())) {
            page = gameMapper.findByNameContainingAndStatus(request.getKeyword(), request.getStatus(), pageable);
        } else if (StringUtils.hasText(request.getCategory())) {
            page = gameMapper.findByCategoryAndStatus(request.getCategory(), request.getStatus(), pageable);
        } else {
            page = gameMapper.findByStatus(request.getStatus(), pageable);
        }

        List<GameResponse> list = page.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        return PageResult.of(list, page.getTotalElements(), request.getPage(), request.getSize());
    }

    /**
     * 根据ID获取游戏详情
     */
    public GameResponse getGameById(String id) {
        Game game = gameMapper.findById(id)
                .orElseThrow(() -> new RuntimeException("游戏不存在"));
        return convertToResponse(game);
    }

    /**
     * 获取推荐游戏
     */
    public List<GameResponse> getRecommendedGames(int limit) {
        List<Game> games = gameMapper.findByIsRecommendedAndStatusOrderByCreateTimeDesc(1, 1);
        return games.stream()
                .limit(limit)
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 获取精选游戏
     */
    public List<GameResponse> getFeaturedGames(int limit) {
        List<Game> games = gameMapper.findByIsFeaturedAndStatusOrderByCreateTimeDesc(1, 1);
        return games.stream()
                .limit(limit)
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 获取热门游戏
     */
    public List<GameResponse> getHotGames(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<Game> games = gameMapper.findHotGames(1, pageable);
        return games.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 创建游戏
     */
    public GameResponse createGame(Game game) {
        game.setId(IdGenerator.generateId());
        game.setCreateTime(LocalDateTime.now());
        game.setUpdateTime(LocalDateTime.now());
        
        if (game.getStatus() == null) {
            game.setStatus(1);
        }
        if (game.getIsRecommended() == null) {
            game.setIsRecommended(0);
        }
        if (game.getIsFeatured() == null) {
            game.setIsFeatured(0);
        }
        if (game.getPlayCount() == null) {
            game.setPlayCount(0L);
        }
        if (game.getRatingCount() == null) {
            game.setRatingCount(0);
        }

        Game savedGame = gameMapper.save(game);
        return convertToResponse(savedGame);
    }

    /**
     * 更新游戏
     */
    public GameResponse updateGame(String id, Game game) {
        Game existingGame = gameMapper.findById(id)
                .orElseThrow(() -> new RuntimeException("游戏不存在"));

        // 更新字段
        if (StringUtils.hasText(game.getName())) {
            existingGame.setName(game.getName());
        }
        if (StringUtils.hasText(game.getCover())) {
            existingGame.setCover(game.getCover());
        }
        if (StringUtils.hasText(game.getIcon())) {
            existingGame.setIcon(game.getIcon());
        }
        if (StringUtils.hasText(game.getCategory())) {
            existingGame.setCategory(game.getCategory());
        }
        if (StringUtils.hasText(game.getVersion())) {
            existingGame.setVersion(game.getVersion());
        }
        if (game.getSize() != null) {
            existingGame.setSize(game.getSize());
        }
        if (game.getRating() != null) {
            existingGame.setRating(game.getRating());
        }
        if (StringUtils.hasText(game.getDescription())) {
            existingGame.setDescription(game.getDescription());
        }
        if (StringUtils.hasText(game.getPlayUrl())) {
            existingGame.setPlayUrl(game.getPlayUrl());
        }
        if (StringUtils.hasText(game.getScreenshots())) {
            existingGame.setScreenshots(game.getScreenshots());
        }
        if (game.getIsRecommended() != null) {
            existingGame.setIsRecommended(game.getIsRecommended());
        }
        if (game.getIsFeatured() != null) {
            existingGame.setIsFeatured(game.getIsFeatured());
        }
        if (game.getStatus() != null) {
            existingGame.setStatus(game.getStatus());
        }

        existingGame.setUpdateTime(LocalDateTime.now());
        Game savedGame = gameMapper.save(existingGame);
        return convertToResponse(savedGame);
    }

    /**
     * 删除游戏
     */
    public void deleteGame(String id) {
        if (!gameMapper.existsById(id)) {
            throw new RuntimeException("游戏不存在");
        }
        gameMapper.deleteById(id);
    }

    /**
     * 构建排序
     */
    private Sort buildSort(String sortType) {
        return switch (sortType) {
            case "hot" -> Sort.by(Sort.Direction.DESC, "playCount");
            case "rating" -> Sort.by(Sort.Direction.DESC, "rating");
            case "play" -> Sort.by(Sort.Direction.DESC, "playCount");
            default -> Sort.by(Sort.Direction.DESC, "createTime");
        };
    }

    /**
     * 转换为响应DTO
     */
    private GameResponse convertToResponse(Game game) {
        GameResponse response = new GameResponse();
        response.setId(game.getId());
        response.setName(game.getName());
        response.setCover(game.getCover());
        response.setIcon(game.getIcon());
        response.setCategory(game.getCategory());
        response.setVersion(game.getVersion());
        response.setSize(game.getSize());
        response.setSizeFormatted(formatSize(game.getSize()));
        response.setRating(game.getRating());
        response.setRatingCount(game.getRatingCount());
        response.setPlayCount(game.getPlayCount());
        response.setPlayCountFormatted(formatNumber(game.getPlayCount()));
        response.setDescription(game.getDescription());
        response.setPlayUrl(game.getPlayUrl());
        response.setIsRecommended(game.getIsRecommended() == 1);
        response.setIsFeatured(game.getIsFeatured() == 1);
        response.setStatus(game.getStatus());
        response.setCreateTime(game.getCreateTime());
        response.setUpdateTime(game.getUpdateTime());

        // 解析截图JSON
        if (StringUtils.hasText(game.getScreenshots())) {
            try {
                List<String> screenshots = objectMapper.readValue(game.getScreenshots(), new TypeReference<List<String>>() {});
                response.setScreenshots(screenshots);
            } catch (Exception e) {
                // 忽略JSON解析错误
            }
        }

        return response;
    }

    /**
     * 格式化文件大小
     */
    private String formatSize(Long size) {
        if (size == null || size == 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        double fileSize = size.doubleValue();
        
        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", fileSize, units[unitIndex]);
    }

    /**
     * 格式化数字
     */
    private String formatNumber(Long number) {
        if (number == null || number == 0) {
            return "0";
        }
        
        if (number >= 10000) {
            return String.format("%.1f万", number / 10000.0);
        }
        
        return number.toString();
    }
}
