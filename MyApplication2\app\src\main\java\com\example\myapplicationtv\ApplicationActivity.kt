package com.example.myapplicationtv

import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import com.example.myapplicationtv.base.BaseTVActivity

/**
 * 应用页面Activity
 */
class ApplicationActivity : BaseTVActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_application)
        
        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .replace(R.id.application_fragment_container, ApplicationFragment())
                .commitNow()
        }
    }
}
