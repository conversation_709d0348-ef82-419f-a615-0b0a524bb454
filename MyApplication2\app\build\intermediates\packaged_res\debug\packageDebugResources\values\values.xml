<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_blue">#0066cc</color>
    <color name="accent_cyan">#06b6d4</color>
    <color name="accent_green">#10b981</color>
    <color name="accent_orange">#ff6b35</color>
    <color name="accent_purple">#8b5cf6</color>
    <color name="accent_yellow">#f59e0b</color>
    <color name="background_gradient_end">#DDDDDD</color>
    <color name="background_gradient_start">#000000</color>
    <color name="card_background">#0f3460</color>
    <color name="category_focused_background">#3a3a5e</color>
    <color name="category_normal_background">#2a2a3e</color>
    <color name="custom_tv_background">#1a1a2e</color>
    <color name="default_background">#3d3d3d</color>
    <color name="fastlane_background">#0096a6</color>
    <color name="focus_border_color">#0066cc</color>
    <color name="navigation_background">#16213e</color>
    <color name="rating_color">#ffd700</color>
    <color name="search_opaque">#ffaa3f</color>
    <color name="selected_background">#ffaa3f</color>
    <string name="app_name">My Application TV</string>
    <string name="browse_title">Videos by Your Company</string>
    <string name="buy_1">Buy and Own</string>
    <string name="buy_2">AT $9.99</string>
    <string name="dismiss_error">Dismiss</string>
    <string name="error_fragment">Error Fragment</string>
    <string name="error_fragment_message">An error occurred</string>
    <string name="grid_view">Grid View</string>
    <string name="movie">Movie</string>
    <string name="personal_settings">Personal Settings</string>
    <string name="related_movies">Related Videos</string>
    <string name="rent_1">Rent By Day</string>
    <string name="rent_2">From $1.99</string>
    <string name="watch_trailer_1">Watch trailer</string>
    <string name="watch_trailer_2">FREE</string>
    <style name="Theme.MyApplicationTV" parent="@style/Theme.Leanback"/>
</resources>