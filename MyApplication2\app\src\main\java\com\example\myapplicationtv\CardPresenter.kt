package com.example.myapplicationtv

import android.graphics.drawable.Drawable
import androidx.leanback.widget.ImageCardView
import androidx.leanback.widget.Presenter
import androidx.core.content.ContextCompat
import android.util.Log
import android.view.ViewGroup

import com.bumptech.glide.Glide
import com.example.myapplicationtv.widget.FocusableImageCardView
import com.example.myapplicationtv.model.ContentType
import kotlin.properties.Delegates

/**
 * A CardPresenter is used to generate Views and bind Objects to them on demand.
 * It contains an ImageCardView.
 */
class CardPresenter : Presenter() {
    private var mDefaultCardImage: Drawable? = null
    private var sSelectedBackgroundColor: Int by Delegates.notNull()
    private var sDefaultBackgroundColor: Int by Delegates.notNull()

    override fun onCreateViewHolder(parent: ViewGroup): Presenter.ViewHolder {
        Log.d(TAG, "onCreateViewHolder")

        sDefaultBackgroundColor = ContextCompat.getColor(parent.context, R.color.default_background)
        sSelectedBackgroundColor =
            ContextCompat.getColor(parent.context, R.color.selected_background)
        mDefaultCardImage = ContextCompat.getDrawable(parent.context, R.drawable.movie)

        val cardView = FocusableImageCardView(parent.context)

        // 设置选择状态变化监听器
        cardView.setOnSelectionChangedListener { selected ->
            updateCardBackgroundColor(cardView, selected)
        }

        cardView.isFocusable = true
        cardView.isFocusableInTouchMode = true
        updateCardBackgroundColor(cardView, false)
        return Presenter.ViewHolder(cardView)
    }

    override fun onBindViewHolder(viewHolder: Presenter.ViewHolder, item: Any) {
        val cardView = viewHolder.view as ImageCardView

        Log.d(TAG, "onBindViewHolder")

        when (item) {
            is Movie -> {
                // 兼容原有的Movie对象
                if (item.cardImageUrl != null) {
                    cardView.titleText = item.title
                    cardView.contentText = item.studio
                    cardView.setMainImageDimensions(CARD_WIDTH, CARD_HEIGHT)
                    Glide.with(viewHolder.view.context)
                        .load(item.cardImageUrl)
                        .centerCrop()
                        .error(mDefaultCardImage)
                        .into(cardView.mainImageView)
                }
            }
            is ContentItem -> {
                // 支持新的ContentItem对象
                cardView.titleText = item.title
                cardView.contentText = item.category ?: ""
                cardView.setMainImageDimensions(CARD_WIDTH, CARD_HEIGHT)
                Glide.with(viewHolder.view.context)
                    .load(item.cardImageUrl)
                    .centerCrop()
                    .error(mDefaultCardImage)
                    .into(cardView.mainImageView)
            }
            else -> {
                Log.w(TAG, "Unknown item type: ${item::class.java.simpleName}")
            }
        }
    }

    override fun onUnbindViewHolder(viewHolder: Presenter.ViewHolder) {
        Log.d(TAG, "onUnbindViewHolder")
        val cardView = viewHolder.view as ImageCardView
        // Remove references to images so that the garbage collector can free up memory
        cardView.badgeImage = null
        cardView.mainImage = null
    }

    private fun updateCardBackgroundColor(view: ImageCardView, selected: Boolean) {
        val color = if (selected) sSelectedBackgroundColor else sDefaultBackgroundColor
        // Both background colors should be set because the view"s background is temporarily visible
        // during animations.
        view.setBackgroundColor(color)
        view.setInfoAreaBackgroundColor(color)
    }

    companion object {
        private val TAG = "CardPresenter"

        private val CARD_WIDTH = 313
        private val CARD_HEIGHT = 176
    }
}