<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="48dp"
    android:background="@color/default_background">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="系统集成测试"
        android:textSize="32sp"
        android:textColor="@android:color/white"
        android:layout_marginBottom="32dp"
        android:textStyle="bold" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="正在运行系统测试..."
        android:textSize="18sp"
        android:textColor="@android:color/white"
        android:layout_marginBottom="24dp" />

    <ProgressBar
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="测试项目："
        android:textSize="16sp"
        android:textColor="@android:color/white"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="• 设备兼容性测试\n• API连接测试\n• 内存性能测试\n• 遥控器功能测试\n• UI响应时间测试"
        android:textSize="14sp"
        android:textColor="@android:color/white"
        android:lineSpacingExtra="4dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="请查看日志获取详细测试结果"
        android:textSize="12sp"
        android:textColor="#CCFFFFFF"
        android:layout_marginTop="32dp" />

</LinearLayout>
