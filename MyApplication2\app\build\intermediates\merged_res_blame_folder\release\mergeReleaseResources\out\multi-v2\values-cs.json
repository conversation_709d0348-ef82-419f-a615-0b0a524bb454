{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-cs/values-cs.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,406,505,610,717,5264", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "198,300,401,500,605,712,831,5360"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,509,639,753,850,940,1061,1181,1288,1411,1573,1697,1820,1923,2019,2149,2241,2344,2446,2559,2659,2776,2897,3019,3140,3248,3364,3488,3614,3738,3864,3951,4035,4139,4274,4440", "endColumns": "106,100,99,95,129,113,96,89,120,119,106,122,161,123,122,102,95,129,91,102,101,112,99,116,120,121,120,107,115,123,125,123,125,86,83,103,134,165,92", "endOffsets": "207,308,408,504,634,748,845,935,1056,1176,1283,1406,1568,1692,1815,1918,2014,2144,2236,2339,2441,2554,2654,2771,2892,3014,3135,3243,3359,3483,3609,3733,3859,3946,4030,4134,4269,4435,4528"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,943,1044,1144,1240,1370,1484,1581,1671,1792,1912,2019,2142,2304,2428,2551,2654,2750,2880,2972,3075,3177,3290,3390,3507,3628,3750,3871,3979,4095,4219,4345,4469,4595,4682,4766,4870,5005,5171", "endColumns": "106,100,99,95,129,113,96,89,120,119,106,122,161,123,122,102,95,129,91,102,101,112,99,116,120,121,120,107,115,123,125,123,125,86,83,103,134,165,92", "endOffsets": "938,1039,1139,1235,1365,1479,1576,1666,1787,1907,2014,2137,2299,2423,2546,2649,2745,2875,2967,3070,3172,3285,3385,3502,3623,3745,3866,3974,4090,4214,4340,4464,4590,4677,4761,4865,5000,5166,5259"}}]}]}