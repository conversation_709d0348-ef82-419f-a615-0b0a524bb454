<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <layer-list>
            <!-- 外层白色发光边框 -->
            <item>
                <shape android:shape="oval">
                    <solid android:color="@android:color/white" />
                </shape>
            </item>
            <!-- 内层按钮背景 -->
            <item android:left="3dp" android:top="3dp" android:right="3dp" android:bottom="3dp">
                <shape android:shape="oval">
                    <solid android:color="@color/accent_blue" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item android:state_focused="true">
        <shape android:shape="oval">
            <solid android:color="@color/accent_blue" />
        </shape>
    </item>
    <item>
        <shape android:shape="oval">
            <solid android:color="#33ffffff" />
        </shape>
    </item>
</selector>
