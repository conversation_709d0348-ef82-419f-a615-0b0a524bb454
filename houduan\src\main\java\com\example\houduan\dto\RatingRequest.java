package com.example.houduan.dto;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 评分请求DTO
 */
@Data
public class RatingRequest {

    @NotBlank(message = "电影ID不能为空")
    private String movieId;

    @NotNull(message = "评分不能为空")
    @DecimalMin(value = "0.0", message = "评分不能小于0.0")
    @DecimalMax(value = "10.0", message = "评分不能大于10.0")
    private BigDecimal rating;

    private String comment;
}
