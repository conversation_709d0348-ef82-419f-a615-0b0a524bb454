<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/card_background" />
            <corners android:radius="16dp" />
            <stroke android:width="3dp" android:color="@color/accent_blue" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/card_background" />
            <corners android:radius="16dp" />
        </shape>
    </item>
</selector>
