<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="搜索用户名"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.contentType"
        placeholder="内容类型"
        clearable
        style="width: 150px"
        class="filter-item"
      >
        <el-option label="电影" value="MOVIE" />
        <el-option label="应用" value="APP" />
        <el-option label="游戏" value="GAME" />
        <el-option label="商品" value="PRODUCT" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="{ row }">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户" width="120">
        <template #default="{ row }">
          <span>{{ row.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="内容类型" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getContentTypeColor(row.contentType)">
            {{ getContentTypeName(row.contentType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="内容ID" width="100" align="center">
        <template #default="{ row }">
          <span>{{ row.contentId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="内容标题" min-width="200">
        <template #default="{ row }">
          <span>{{ row.contentTitle || '未知内容' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="观看时长" width="120" align="center">
        <template #default="{ row }">
          <span>{{ formatWatchTime(row.watchTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="观看时间" width="180" align="center">
        <template #default="{ row }">
          <span>{{ formatDate(row.createdAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后更新" width="180" align="center">
        <template #default="{ row }">
          <span>{{ formatDate(row.updatedAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button size="small" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Pagination from '@/components/Pagination/index.vue'

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = reactive({
  page: 1,
  size: 20,
  keyword: undefined,
  contentType: undefined
})

onMounted(() => {
  getList()
})

const getList = async () => {
  try {
    listLoading.value = true
    // 模拟历史数据
    const mockData = {
      data: {
        list: [
          {
            id: 1,
            user: {
              id: 1,
              username: 'user001',
              avatar: '/images/avatars/user1.jpg'
            },
            content: {
              id: 1,
              title: '复仇者联盟4',
              type: 'MOVIE'
            },
            watchTime: '2小时30分钟',
            createdAt: '2025-01-01 20:30:00'
          },
          {
            id: 2,
            user: {
              id: 2,
              username: 'user002',
              avatar: '/images/avatars/user2.jpg'
            },
            content: {
              id: 2,
              title: '王者荣耀',
              type: 'GAME'
            },
            watchTime: '1小时15分钟',
            createdAt: '2025-01-02 18:45:00'
          }
        ],
        total: 2
      }
    }
    list.value = mockData.data.list
    total.value = mockData.data.total
  } catch (error) {
    console.error('获取历史列表失败:', error)
    ElMessage.error('获取历史列表失败')
  } finally {
    listLoading.value = false
  }
}

const handleFilter = () => {
  listQuery.page = 1
  getList()
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('此操作将删除该历史记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    ElMessage.success('删除成功!')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const getContentTypeName = (type) => {
  const typeMap = {
    'MOVIE': '电影',
    'APP': '应用',
    'GAME': '游戏',
    'PRODUCT': '商品'
  }
  return typeMap[type] || type
}

const getContentTypeColor = (type) => {
  const colorMap = {
    'MOVIE': 'primary',
    'APP': 'success',
    'GAME': 'warning',
    'PRODUCT': 'danger'
  }
  return colorMap[type] || 'info'
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString()
}

const formatWatchTime = (timeStr) => {
  if (!timeStr) return '0分钟'
  // 如果已经是格式化的字符串，直接返回
  if (typeof timeStr === 'string' && timeStr.includes('小时')) {
    return timeStr
  }
  // 如果是秒数，进行格式化
  if (typeof timeStr === 'number') {
    const hours = Math.floor(timeStr / 3600)
    const minutes = Math.floor((timeStr % 3600) / 60)
    const seconds = timeStr % 60

    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds}秒`
    } else {
      return `${seconds}秒`
    }
  }
  return timeStr || '0分钟'
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
</style>
