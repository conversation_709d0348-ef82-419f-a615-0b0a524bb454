#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TV端影视应用 API 测试脚本
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8080/api"
headers = {
    "Content-Type": "application/json"
}

def test_health_check():
    """测试健康检查接口"""
    print("🔍 测试健康检查接口...")
    try:
        response = requests.get(f"{BASE_URL}/test/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def test_public_endpoint():
    """测试公开接口"""
    print("\n🔍 测试公开接口...")
    try:
        response = requests.get(f"{BASE_URL}/test/public")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 公开接口测试失败: {e}")
        return False

def test_user_register():
    """测试用户注册"""
    print("\n🔍 测试用户注册接口...")
    
    # 生成唯一的测试用户
    timestamp = int(time.time())
    test_user = {
        "username": f"testuser_{timestamp}",
        "password": "123456",
        "confirmPassword": "123456",
        "phone": f"138{timestamp % 100000000:08d}",
        "email": f"test_{timestamp}@example.com"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/register",
            headers=headers,
            data=json.dumps(test_user)
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            print("✅ 用户注册成功")
            return test_user, True
        else:
            print("❌ 用户注册失败")
            return test_user, False
            
    except Exception as e:
        print(f"❌ 用户注册异常: {e}")
        return test_user, False

def test_user_login(user_data):
    """测试用户登录"""
    print("\n🔍 测试用户登录接口...")
    
    login_data = {
        "username": user_data["username"],
        "password": user_data["password"]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/login",
            headers=headers,
            data=json.dumps(login_data)
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success") and "data" in result:
                token = result["data"].get("token")
                if token:
                    print("✅ 用户登录成功")
                    return token, True
                    
        print("❌ 用户登录失败")
        return None, False
        
    except Exception as e:
        print(f"❌ 用户登录异常: {e}")
        return None, False

def test_authenticated_endpoint(token):
    """测试需要认证的接口"""
    print("\n🔍 测试认证接口...")
    
    auth_headers = headers.copy()
    auth_headers["Authorization"] = f"Bearer {token}"
    
    try:
        response = requests.get(f"{BASE_URL}/test/auth", headers=auth_headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 认证接口测试失败: {e}")
        return False

def test_categories():
    """测试分类接口"""
    print("\n🔍 测试分类接口...")
    try:
        response = requests.get(f"{BASE_URL}/categories/all")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 分类接口测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始API接口测试...")
    print("=" * 50)
    
    # 测试结果统计
    results = []
    
    # 1. 健康检查
    results.append(("健康检查", test_health_check()))
    
    # 2. 公开接口
    results.append(("公开接口", test_public_endpoint()))
    
    # 3. 用户注册
    user_data, register_success = test_user_register()
    results.append(("用户注册", register_success))
    
    # 4. 用户登录
    if register_success:
        token, login_success = test_user_login(user_data)
        results.append(("用户登录", login_success))
        
        # 5. 认证接口
        if login_success and token:
            results.append(("认证接口", test_authenticated_endpoint(token)))
    
    # 6. 分类接口
    results.append(("分类接口", test_categories()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if success:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(results)} 个测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查服务状态")

if __name__ == "__main__":
    main()
