<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="搜索应用名称"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.category"
        placeholder="选择分类"
        clearable
        style="width: 150px"
        class="filter-item"
      >
        <el-option
          v-for="item in categoryOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="Plus"
        @click="handleCreate"
      >
        添加应用
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="{ row }">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应用图标" width="100" align="center">
        <template #default="{ row }">
          <el-image
            style="width: 50px; height: 50px"
            :src="row.icon"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column label="应用名称" min-width="150">
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分类" width="100" align="center">
        <template #default="{ row }">
          <span>{{ row.category }}</span>
        </template>
      </el-table-column>
      <el-table-column label="版本" width="100" align="center">
        <template #default="{ row }">
          <span>{{ row.version }}</span>
        </template>
      </el-table-column>
      <el-table-column label="大小" width="100" align="center">
        <template #default="{ row }">
          <span>{{ row.sizeFormatted }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评分" width="100" align="center">
        <template #default="{ row }">
          <el-rate
            v-model="row.rating"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}"
          />
        </template>
      </el-table-column>
      <el-table-column label="下载量" width="120" align="center">
        <template #default="{ row }">
          <span>{{ row.downloadCountFormatted }}</span>
        </template>
      </el-table-column>
      <el-table-column label="推荐" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.isRecommended ? 'success' : 'info'">
            {{ row.isRecommended ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'danger'">
            {{ row.status === 'ACTIVE' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button
            v-if="row.status === 'ACTIVE'"
            size="small"
            type="warning"
            @click="handleModifyStatus(row, 'INACTIVE')"
          >
            禁用
          </el-button>
          <el-button
            v-else
            size="small"
            type="success"
            @click="handleModifyStatus(row, 'ACTIVE')"
          >
            启用
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getApps, deleteApp, updateAppStatus } from '@/api/apps'
import { getCategories } from '@/api/categories'
import Pagination from '@/components/Pagination/index.vue'

const router = useRouter()

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = reactive({
  page: 1,
  size: 20,
  keyword: undefined,
  category: undefined,
  sort: 'latest'
})
const categoryOptions = ref([])

const getList = async () => {
  try {
    listLoading.value = true
    // 模拟数据
    const mockData = {
      data: {
        list: [
          {
            id: 1,
            name: '微信',
            version: '8.0.32',
            size: '245MB',
            category: '社交',
            downloads: 1000000,
            status: 1,
            featured: true,
            createdAt: '2025-01-01'
          },
          {
            id: 2,
            name: '抖音',
            version: '24.5.0',
            size: '156MB',
            category: '娱乐',
            downloads: 800000,
            status: 1,
            featured: false,
            createdAt: '2025-01-02'
          }
        ],
        total: 2
      }
    }
    list.value = mockData.data.list
    total.value = mockData.data.total
  } catch (error) {
    console.error('获取应用列表失败:', error)
    ElMessage.error('获取应用列表失败')
  } finally {
    listLoading.value = false
  }
}

const getCategoryList = async () => {
  try {
    categoryOptions.value = [
      { id: 1, name: '社交' },
      { id: 2, name: '娱乐' },
      { id: 3, name: '工具' },
      { id: 4, name: '游戏' }
    ]
  } catch (error) {
    console.error('获取分类失败:', error)
  }
}

const handleFilter = () => {
  listQuery.page = 1
  getList()
}

const handleCreate = () => {
  router.push('/apps/add')
}

const handleUpdate = (row) => {
  router.push(`/apps/edit/${row.id}`)
}

const handleModifyStatus = async (row, status) => {
  try {
    ElMessage.success('操作成功')
    row.status = status
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('此操作将永久删除该应用, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    ElMessage.success('删除成功!')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

onMounted(() => {
  getList()
  getCategoryList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
</style>
