package com.example.houduan.controller;

import com.example.houduan.common.Result;
import com.example.houduan.dto.RatingRequest;
import com.example.houduan.entity.UserRating;
import com.example.houduan.service.UserRatingService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户评分控制器
 */
@RestController
@RequestMapping("/ratings")
@RequiredArgsConstructor
public class UserRatingController {

    private final UserRatingService userRatingService;

    /**
     * 提交评分
     */
    @PostMapping("/submit")
    public Result<String> submitRating(@Valid @RequestBody RatingRequest request, 
                                      Authentication authentication) {
        try {
            String userId = getUserId(authentication);
            userRatingService.submitRating(userId, request);
            return Result.success("评分提交成功");
        } catch (Exception e) {
            return Result.error("评分提交失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户对电影的评分
     */
    @GetMapping("/user/{movieId}")
    public Result<UserRating> getUserRating(@PathVariable String movieId, Authentication authentication) {
        try {
            String userId = getUserId(authentication);
            UserRating rating = userRatingService.getUserRating(userId, movieId);
            return Result.success(rating);
        } catch (Exception e) {
            return Result.error("获取用户评分失败: " + e.getMessage());
        }
    }

    /**
     * 删除评分
     */
    @DeleteMapping("/{movieId}")
    public Result<String> deleteRating(@PathVariable String movieId, Authentication authentication) {
        try {
            String userId = getUserId(authentication);
            userRatingService.deleteRating(userId, movieId);
            return Result.success("删除评分成功");
        } catch (Exception e) {
            return Result.error("删除评分失败: " + e.getMessage());
        }
    }

    /**
     * 获取电影的所有评分
     */
    @GetMapping("/movie/{movieId}")
    public Result<List<UserRating>> getMovieRatings(@PathVariable String movieId) {
        try {
            List<UserRating> ratings = userRatingService.getMovieRatings(movieId);
            return Result.success(ratings);
        } catch (Exception e) {
            return Result.error("获取电影评分失败: " + e.getMessage());
        }
    }

    /**
     * 从认证信息中获取用户ID
     */
    private String getUserId(Authentication authentication) {
        if (authentication == null || authentication.getPrincipal() == null) {
            throw new RuntimeException("用户未登录");
        }
        
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        return userDetails.getUsername(); // 这里需要根据实际情况调整获取用户ID的方式
    }
}
