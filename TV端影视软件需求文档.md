# TV端影视软件需求文档（优化版）

## 一、项目概述

本项目为智能电视端（TV端）影视娱乐平台，支持遥控器操作，界面美观，内容丰富，包含影视播放、应用、游戏、商城、个人中心等模块。后端采用Spring Boot，后台管理采用Vue3。目标是为家庭用户提供一站式影视娱乐体验。

---

## 二、页面结构与功能需求

### 1. 首页
#### 1.1 页面结构
- **顶部Logo与主菜单**：左侧为Logo，右侧为主菜单，包含"首页/推荐"、"电影"、"应用"、"游戏"、"商城"、"我的"六大入口。
- **顶部信息栏**：显示时间、星期、城市、天气、WiFi状态。
- **内容区**：
  - 上方为三大推荐电影卡片，展示电影封面、名称、评分、播放按钮。
  - 下方为空白卡片区，预留更多推荐、广告或动态内容。

#### 1.2 主要功能
- 菜单切换与高亮，支持遥控器左右切换。
- 推荐电影卡片点击进入详情页或直接播放。
- 顶部信息栏数据动态获取（如天气、时间、网络状态）。
- 支持后续扩展Banner、活动、广告等内容。

#### 1.3 交互说明
- 遥控器方向键切换菜单与内容卡片，回车键进入详情或播放。
- 菜单焦点高亮，卡片获得焦点时有动画提示。
- 长时间无操作可自动轮播推荐内容。

#### 1.4 数据结构建议
- 菜单项：`[{id, name, icon, link}]`
- 推荐电影：`[{id, title, cover, rating, playUrl}]`
- 顶部信息：`{time, week, city, weather, wifiStatus}`

---

### 2. 电影列表页
#### 2.1 页面结构
- 返回首页按钮。
- 搜索框，支持影视内容模糊搜索。
- 分类标签（如"全部"、"动作"、"科幻"、"儿童"等），支持切换。
- 电影列表网格，展示电影封面、名称、评分、播放按钮。
- 分页或"加载更多"按钮。

#### 2.2 主要功能
- 支持多条件筛选（类型、地区、年份、评分等）。
- 支持排序（最新、最热、评分、字母等）。
- 搜索历史与热词提示。
- 电影卡片点击进入详情页。
- 支持分页或无限滚动加载。

#### 2.3 交互说明
- 遥控器方向键切换标签、电影卡片，回车进入详情。
- 搜索框获得焦点时弹出输入法，支持拼音、首字母、语音（可选）。
- 支持键盘Tab/方向键导航。
- 网格导航支持上下左右移动，边界自动跳转。

#### 2.4 数据结构建议
- 电影：`[{id, title, cover, rating, type, year, area, playUrl}]`
- 分类标签：`[{id, name}]`
- 搜索历史：`[keyword]`

#### 2.5 异常与边界场景
- 无搜索结果时显示友好提示。
- 网络异常时显示重试按钮。
- 列表为空时显示占位图。

---

### 3. 电影播放页
#### 3.1 页面结构
- 播放器区域：视频窗口、顶部返回按钮、电影信息、播放控制区、进度条、音量、字幕、视频要全屏显示等。

#### 3.2 主要功能
- 支持播放/暂停、快进快退、音量调节、字幕开关、全屏切换。
- 支持分集切换（如电视剧、分段电影）。
- 进度条拖拽、显示当前时间/总时长。
- 相关推荐点击可切换播放。
- 自动记录播放进度，下次可继续播放。
- 支持遥控器/键盘快捷键（空格、左右、上下、回车、返回等）。

#### 3.3 交互说明
- 遥控器方向键切换控制按钮、章节、相关推荐。
- 回车键执行播放/暂停/切换。
- Tab键焦点切换。
- 长时间无操作自动隐藏控制栏。

#### 3.4 数据结构建议
- 当前电影：`{id, title, cover, year, duration, type, playUrl, chapters: [{name, start, end}]}`
- 相关推荐：`[{id, title, cover, year, duration}]`

#### 3.5 异常与边界场景
- 播放失败时自动重试或提示用户。
- 无可用分集时隐藏章节栏。
- 网络异常时暂停播放并提示。

---

### 4. 应用页
#### 4.1 页面结构
- 应用分类（如"推荐"、"播放器"、"音乐"、"教育"、"儿童"、"生活服务"、"新闻资讯"等）。
- 应用列表卡片，含图标、名称、类型、评分。

#### 4.2 主要功能
- 分类切换，展示不同类型应用。
- 应用卡片点击进入详情或下载。
- 支持评分展示、下载状态提示。

#### 4.3 交互说明
- 遥控器方向键切换分类、应用卡片，回车进入详情或下载。
- 分类获得焦点时高亮。

#### 4.4 数据结构建议
- 应用：`[{id, name, icon, type, rating, downloadUrl, desc}]`
- 分类：`[{id, name}]`

#### 4.5 异常与边界场景
- 下载失败时提示重试。
- 无应用时显示占位图。

---

### 5. 游戏页
#### 5.1 页面结构
- 游戏分类（如"热门"、"动作"、"策略"、"竞速"、"儿童"、"益智"、"体育"等）。
- 精选游戏轮播，横向滚动展示。
- 游戏列表网格，含封面、名称、类型、评分。

#### 5.2 主要功能
- 分类切换，展示不同类型游戏。
- 精选游戏支持左右切换、自动轮播。
- 游戏卡片点击进入详情或启动。

#### 5.3 交互说明
- 遥控器方向键切换分类、轮播、游戏卡片，回车进入详情或启动。
- 轮播获得焦点时自动暂停。

#### 5.4 数据结构建议
- 游戏：`[{id, name, cover, type, rating, playUrl, desc}]`
- 分类：`[{id, name}]`

#### 5.5 异常与边界场景
- 启动失败时提示重试。
- 无游戏时显示占位图。

---

### 6. 商城页
#### 6.1 页面结构
- 商品分类（如"推荐"、"智能电视"、"音响设备"、"手机平板"、"电脑办公"、"智能家居"、"服饰"等）。
- Banner广告，顶部大图。
- 商品列表网格，含图片、名称、简介、价格、评分。

#### 6.2 主要功能
- 分类切换，展示不同类型商品。
- Banner支持点击跳转活动页。
- 商品卡片点击进入详情或购买。
- 支持价格、评分、库存等信息展示。

#### 6.3 交互说明
- 遥控器方向键切换分类、商品卡片，回车进入详情或购买。
- 分类、Banner、商品卡片均有焦点高亮。

#### 6.4 数据结构建议
- 商品：`[{id, name, image, price, rating, desc, stock, buyUrl}]`
- 分类：`[{id, name}]`
- Banner：`[{id, image, link}]`

#### 6.5 异常与边界场景
- 购买失败时提示。
- 无商品时显示占位图。

---

### 7. 个人中心页
#### 7.1 页面结构
- 用户信息区：头像、昵称、会员等级、统计信息（观看、收藏、评分数）。
- 操作按钮：设置、退出登录。
- 功能标签：收藏的电影、观看历史、已评分电影、账号设置。
- 内容区：
  - 收藏/历史/评分电影以网格或列表展示，支持播放、移除。
  - 会员升级Banner。
  - 推荐电影列表。

#### 7.2 主要功能
- 用户信息展示与编辑。
- 收藏、历史、评分电影管理。
- 会员升级、续费、权益展示。
- 账号设置（如修改密码、绑定手机号等）。
- 支持退出登录。

#### 7.3 交互说明
- 遥控器方向键切换标签、电影卡片，回车播放或操作。
- 操作按钮获得焦点时高亮。

#### 7.4 数据结构建议
- 用户：`{id, name, avatar, memberLevel, stats: {watched, favorite, rated}}`
- 收藏/历史/评分电影：同电影结构

#### 7.5 异常与边界场景
- 未登录时跳转登录页或显示登录按钮。
- 收藏/历史为空时显示占位图。

---

## 三、主要交互与遥控器适配
- 全站支持遥控器方向键、回车、返回、Tab等操作。
- 所有可操作元素（按钮、卡片、标签、Banner等）均有焦点高亮，焦点动画明显。
- 支持键盘/遥控器网格导航、焦点循环，边界自动跳转或回到首项。
- 播放页支持键盘快捷键（空格播放/暂停，左右快进快退，上下音量，回车确认，返回退出等）。
- 长时间无操作自动隐藏控制栏或焦点提示。
- 支持遥控器返回键返回上一级页面。
- 支持语音输入（可选，需硬件支持）。

---

## 四、后端接口建议（Spring Boot）
### 4.1 用户相关
- 注册、登录、登出（支持TV端扫码登录、手机号验证码登录等）。
- 用户信息查询与修改。
- 用户收藏、历史、评分、会员管理。

### 4.2 影视内容
- 电影/电视剧/综艺/动漫等内容的列表、详情、分类、搜索、推荐、播放地址、分集。
- 支持多条件筛选、排序、分页。

### 4.3 应用/游戏/商品
- 应用、游戏、商品的列表、详情、分类、下载/购买、推荐。

### 4.4 统计分析
- 播放量、收藏量、活跃度、热度排行等。

### 4.5 系统管理
- 权限、日志、参数配置。

#### 接口示例：
- `POST /api/user/login` 用户登录
- `GET /api/user/info` 获取用户信息
- `POST /api/user/favorite` 收藏/取消收藏
- `GET /api/user/history` 获取观看历史
- `GET /api/movies` 获取电影列表（支持分页、筛选、排序）
- `GET /api/movie/{id}` 获取电影详情
- `GET /api/movie/{id}/play` 获取播放地址
- `GET /api/apps` 获取应用列表
- `GET /api/games` 获取游戏列表
- `GET /api/products` 获取商品列表
- `POST /api/order` 下单购买
- `GET /api/statistics` 获取统计数据

#### 数据返回格式建议：
- 统一JSON格式，包含`code`、`msg`、`data`字段。
- 错误码与错误信息规范化。

---

## 五、后台管理建议（Vue3）
### 5.1 登录/权限管理
- 支持多角色（超级管理员、内容编辑、运营、客服等）。
- 权限分级，菜单动态展示。

### 5.2 影视内容管理
- 影视内容的增删改查。
- 分类、分集、标签、地区、年份等元数据维护。
- 推荐位、Banner管理。
- 批量导入/导出。

### 5.3 应用/游戏/商品管理
- 应用、游戏、商品的增删改查、分类、推荐。
- 下载/购买统计。

### 5.4 用户管理
- 用户信息、会员、收藏、历史、禁用、封号。
- 用户行为日志。

### 5.5 数据统计
- 访问量、播放量、收藏量、活跃度、热度排行等可视化报表。
- 支持按时间、内容、用户等多维度统计。

### 5.6 系统设置
- 参数配置、日志管理、权限分配。

#### 后台管理交互细节：
- 支持表格筛选、批量操作、导出Excel。
- 支持图片/视频上传、富文本编辑。
- 支持多端适配（PC、平板）。

---

## 六、非功能需求
- **性能**：页面响应<1s，播放流畅不卡顿，接口高并发支持。
- **安全**：接口鉴权，数据加密，防止盗链、SQL注入、XSS等。
- **兼容性**：适配主流Android TV、OTT盒子，兼容不同分辨率。
- **可扩展性**：支持内容源、广告、会员、第三方登录等扩展。
- **易用性**：遥控器友好，UI美观，焦点明显，操作路径简洁。
- **可维护性**：代码结构清晰，接口文档完善，日志可追溯。
- **多语言支持**：支持多语言切换（如简体、维语、英文等）。

---

## 七、页面UI/UX风格说明
- 采用深色主题，色彩对比强烈，突出内容。
- 卡片式布局，圆角、阴影，提升层次感。
- 大字号、图标清晰，适合远距离观看。
- 焦点高亮明显，交互动画流畅。
- 交互反馈及时，操作有音效/动画提示。
- 支持响应式布局，适配不同尺寸电视。
- 重要操作有二次确认弹窗，防止误操作。

---

## 八、其他建议
- **日志与监控**：前后端均需埋点，便于问题追踪与数据分析。
- **异常处理**：所有接口和页面需有异常兜底与用户友好提示。
- **测试要求**：覆盖主流遥控器、Android TV系统，兼容性测试。
- **上线运维**：支持灰度发布、版本回滚、自动化部署。

---

如需更详细的接口文档、数据库设计、后台原型、UI原型图等，可进一步补充。

---

## 九、主要后端接口字段说明

### 1. 电影相关接口
#### 1.1 获取电影列表
- 接口：`GET /api/movies`
- 请求参数：
  | 参数名     | 类型   | 必填 | 说明           |
  | ---------- | ------ | ---- | -------------- |
  | page       | int    | 否   | 页码           |
  | size       | int    | 否   | 每页数量       |
  | type       | string | 否   | 电影类型       |
  | keyword    | string | 否   | 搜索关键字     |
  | sort       | string | 否   | 排序方式       |

- 返回字段：
  | 字段名     | 类型         | 说明           |
  | ---------- | ------------| -------------- |
  | code       | int         | 状态码         |
  | msg        | string      | 提示信息       |
  | data       | object      | 数据主体       |
  | └ list     | array       | 电影列表       |
  |   └ id     | string      | 电影ID         |
  |   └ title  | string      | 电影名称       |
  |   └ cover  | string      | 封面图片URL    |
  |   └ rating | float       | 评分           |
  |   └ type   | string      | 类型           |
  |   └ year   | int         | 上映年份       |
  |   └ area   | string      | 地区           |
  |   └ desc   | string      | 简介           |
  |   └ playUrl| string      | 播放地址       |
  | └ total    | int         | 总数           |

#### 1.2 获取电影详情
- 接口：`GET /api/movie/{id}`
- 返回字段：
  | 字段名     | 类型         | 说明           |
  | ---------- | ------------| -------------- |
  | id         | string      | 电影ID         |
  | title      | string      | 电影名称       |
  | cover      | string      | 封面图片URL    |
  | rating     | float       | 评分           |
  | type       | string      | 类型           |
  | year       | int         | 上映年份       |
  | area       | string      | 地区           |
  | desc       | string      | 简介           |
  | duration   | int         | 时长（分钟）   |
  | chapters   | array       | 分集/章节      |
  | └ name     | string      | 章节名         |
  | └ start    | string      | 开始时间       |
  | └ end      | string      | 结束时间       |
  | playUrl    | string      | 播放地址       |

### 2. 用户相关接口
#### 2.1 用户登录
- 接口：`POST /api/user/login`
- 请求参数：
  | 参数名     | 类型   | 必填 | 说明           |
  | ---------- | ------ | ---- | -------------- |
  | username   | string | 是   | 用户名/手机号  |
  | password   | string | 是   | 密码           |

- 返回字段：
  | 字段名     | 类型         | 说明           |
  | ---------- | ------------| -------------- |
  | code       | int         | 状态码         |
  | msg        | string      | 提示信息       |
  | data       | object      | 用户信息       |
  | └ id       | string      | 用户ID         |
  | └ name     | string      | 用户名         |
  | └ avatar   | string      | 头像           |
  | └ memberLevel | string   | 会员等级       |
  | └ token    | string      | 登录token      |

#### 2.2 获取用户收藏
- 接口：`GET /api/user/favorite`
- 返回字段同电影列表

#### 2.3 添加/取消收藏
- 接口：`POST /api/user/favorite`
- 请求参数：
  | 参数名     | 类型   | 必填 | 说明           |
  | ---------- | ------ | ---- | -------------- |
  | movieId    | string | 是   | 电影ID         |
  | action     | string | 是   | add/remove     |

- 返回字段：
  | 字段名     | 类型         | 说明           |
  | ---------- | ------------| -------------- |
  | code       | int         | 状态码         |
  | msg        | string      | 提示信息       |

### 3. 播放相关接口
#### 3.1 获取播放地址
- 接口：`GET /api/movie/{id}/play`
- 返回字段：
  | 字段名     | 类型         | 说明           |
  | ---------- | ------------| -------------- |
  | playUrl    | string      | 播放地址       |
  | subtitles  | array       | 字幕列表       |
  | └ lang     | string      | 语言           |
  | └ url      | string      | 字幕文件URL    |

---

## 十、数据库ER图（表结构与关系）

> 说明：以下为主要表结构及关系，建议用专业ER工具绘制ER图，开发初期可参考下表。

### 1. 主要表结构

| 表名         | 字段名         | 类型        | 说明           |
| ------------ | -------------- | ---------- | -------------- |
| user         | id             | varchar    | 用户ID         |
|              | name           | varchar    | 用户名         |
|              | password       | varchar    | 密码           |
|              | avatar         | varchar    | 头像           |
|              | member_level   | varchar    | 会员等级       |
|              | create_time    | datetime   | 注册时间       |
| movie        | id             | varchar    | 电影ID         |
|              | title          | varchar    | 电影名称       |
|              | cover          | varchar    | 封面           |
|              | rating         | float      | 评分           |
|              | type           | varchar    | 类型           |
|              | year           | int        | 上映年份       |
|              | area           | varchar    | 地区           |
|              | desc           | text       | 简介           |
|              | duration       | int        | 时长           |
|              | create_time    | datetime   | 创建时间       |
| chapter      | id             | varchar    | 章节ID         |
|              | movie_id       | varchar    | 电影ID         |
|              | name           | varchar    | 章节名         |
|              | start_time     | varchar    | 开始时间       |
|              | end_time       | varchar    | 结束时间       |
| favorite     | id             | varchar    | 主键           |
|              | user_id        | varchar    | 用户ID         |
|              | movie_id       | varchar    | 电影ID         |
|              | create_time    | datetime   | 收藏时间       |
| history      | id             | varchar    | 主键           |
|              | user_id        | varchar    | 用户ID         |
|              | movie_id       | varchar    | 电影ID         |
|              | progress       | int        | 播放进度(秒)   |
|              | update_time    | datetime   | 更新时间       |

### 2. 主要关系
- user 与 favorite/history 为一对多
- movie 与 chapter 为一对多
- user 与 movie 通过 favorite/history 形成多对多

---

## 十一、后台管理原型（结构与操作流程）

### 1. 登录页
- 账号、密码输入，验证码（可选），登录按钮。
- 登录后根据权限跳转不同首页。

### 2. 影视内容管理
- 影视列表：支持搜索、筛选、分页、批量操作。
- 新增/编辑影视：填写名称、封面、类型、年份、简介、分集等。
- 删除、上下架、推荐位设置。
- Banner管理：上传图片、设置跳转链接、排序。

### 3. 用户管理
- 用户列表：查看、搜索、禁用、重置密码。
- 会员管理：升级、续费、到期提醒。
- 用户行为日志：查看用户操作记录。

### 4. 应用/游戏/商品管理
- 列表、分类、推荐、上下架、编辑、删除。
- 统计下载/购买量。

### 5. 数据统计
- 访问量、播放量、收藏量、热度排行等图表。
- 支持按时间、内容、用户等多维度筛选。

### 6. 系统设置
- 角色权限分配、参数配置、日志管理。

---

## 十二、UI交互流程图（文字流程描述）

### 1. 首页交互流程
1. 用户进入首页，默认焦点在主菜单第一个。
2. 左右切换菜单，回车进入对应频道页。
3. 下移焦点到推荐电影卡片，左右切换，回车进入详情或播放。
4. 长时间无操作自动轮播推荐内容。

### 2. 电影列表页交互流程
1. 默认焦点在搜索框，输入关键字可搜索。
2. 下移焦点到分类标签，左右切换类型。
3. 下移焦点到电影网格，方向键切换卡片，回车进入详情。
4. 到达底部可加载更多。

### 3. 播放页交互流程
1. 默认焦点在播放/暂停按钮。
2. 左右切换快进、快退、音量、字幕、全屏等按钮。
3. 右移焦点到分集列表，方向键切换章节，回车切换播放。
4. 下移焦点到相关推荐，左右切换，回车切换播放。
5. 返回键返回上一页。

### 4. 个人中心交互流程
1. 默认焦点在功能标签（收藏、历史、评分、设置）。
2. 下移焦点到内容区，方向键切换卡片，回车播放或操作。
3. 右上角操作按钮（设置、退出登录）可随时切换。

---

如需进一步补充详细接口文档、数据库ER图图片、后台UI原型图、流程图等，请联系产品/设计团队或使用专业工具绘制。 