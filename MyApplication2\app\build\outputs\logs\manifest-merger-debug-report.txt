-- Merging decision tree log ---
manifest
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:2:1-59:12
INJECTED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:2:1-59:12
INJECTED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:2:1-59:12
INJECTED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:2:1-59:12
MERGED from [androidx.leanback:leanback:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\89d10e7acdc9e8617fe8b024a2336641\transformed\leanback-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.11.0] D:\Android\GradleRepository\caches\8.12\transforms\59e3767b96f4ee90f4b57a0c56b92a96\transformed\glide-4.11.0\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.fragment:fragment:1.6.2] D:\Android\GradleRepository\caches\8.12\transforms\ccfd53c838c812e96e4910096138f89c\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] D:\Android\GradleRepository\caches\8.12\transforms\374ceb5124eacb51f9cd2e0f0855c4f3\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] D:\Android\GradleRepository\caches\8.12\transforms\cffe277412d6e71dab1701f200c8c21e\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\22d0274585fcaae534e1fc3e6eb76e6e\transformed\vectordrawable-animated-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\ab3683f7a4d9a57eaa986edb4e5262fb\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\d1cb9debee0ec7aff0e9116da1dc9922\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\2bd59f59ae2972a6ae2824d06af616c8\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\cec8346c4e2ab21f142e352fe0bfc4ee\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\5c68e73db408f53b49ab0ec6aa072138\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\3b3a598714bb2410226fd2e2c54e7e86\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\8b16bf72240f7d4350db4f5688c2644d\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\e4d5bf82e4d5c2bf484b0d2d23ce3117\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\e8d642ec3f0deb889900fe6cc9fe5cef\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\d3acc5e891e2e7a0ee325b25454f074d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\3f0c6eafd88bfcc275cea1cacdb0338e\transformed\vectordrawable-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.2] D:\Android\GradleRepository\caches\8.12\transforms\22ec3492cd38aff0fdd3abd4ae94272b\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] D:\Android\GradleRepository\caches\8.12\transforms\9717948c970c19acce1692941c22c532\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\8c1f2d282f20138de146e7f45bde41c9\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\6c7c511ec8b68e45e391fb30b6d5f144\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\Android\GradleRepository\caches\8.12\transforms\220f8c0f15f6aa3f199c619409bad374\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\Android\GradleRepository\caches\8.12\transforms\b9b3bed66d3a00e5a31ccb8c12557bab\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\e986c459db27042bd4bd99648aeb71d5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\6d91530bd68c1a5268acfee80590353b\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\0b77184f006223771d62c67bbd676e73\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\09b2070107f58b91baaaa03824de0d80\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\2cd992ff3743b623f80bb69d849f50ca\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\e2630f862baa837dd8db5f1a6cd2b6cf\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\44b9bab188f5f5366f3115537e09a453\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\9262bdae479e1b658eb70c7bbd10b157\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\e71662f1e872c884e7f2d1e2bf0f11d8\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\6758fc4f4664cc3e72a8df28e7d3f5da\transformed\core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\03f0f7d3357ba78868c8f6b45210dab1\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] D:\Android\GradleRepository\caches\8.12\transforms\e9cf136272e11e6df2f8a99b3e3d46e1\transformed\gifdecoder-4.11.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\2820c43de1ddfedc63a9c2819f02c6e3\transformed\exifinterface-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\a0f862da65c20cd37353b7a213469561\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Android\GradleRepository\caches\8.12\transforms\f527d98c4d43bc99c34c0352c3afa590\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\d37b93b53f0b4b52458233873d9d92c6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\Android\GradleRepository\caches\8.12\transforms\f995e7e8cc3070312bef138ddf684057\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\2973b6edbd6255a26873f431c1cacfe3\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\0fceed22a269c56fb956e6a7c7b2505d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\1aea720ee1e923fbe1226a3f16df1f37\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Android\GradleRepository\caches\8.12\transforms\fc95bcae0ba172be27074a8a115ec3a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\18a099003c1c7bc56d0ff2fb22dccf68\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:5:22-64
uses-feature#android.hardware.touchscreen
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:7:5-9:36
	android:required
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:9:9-33
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:8:9-52
uses-feature#android.software.leanback
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:10:5-12:35
	android:required
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:12:9-32
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:11:9-49
application
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:14:5-57:19
INJECTED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:14:5-57:19
MERGED from [com.github.bumptech.glide:glide:4.11.0] D:\Android\GradleRepository\caches\8.12\transforms\59e3767b96f4ee90f4b57a0c56b92a96\transformed\glide-4.11.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.11.0] D:\Android\GradleRepository\caches\8.12\transforms\59e3767b96f4ee90f4b57a0c56b92a96\transformed\glide-4.11.0\AndroidManifest.xml:10:5-20
MERGED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] D:\Android\GradleRepository\caches\8.12\transforms\e9cf136272e11e6df2f8a99b3e3d46e1\transformed\gifdecoder-4.11.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] D:\Android\GradleRepository\caches\8.12\transforms\e9cf136272e11e6df2f8a99b3e3d46e1\transformed\gifdecoder-4.11.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Android\GradleRepository\caches\8.12\transforms\f527d98c4d43bc99c34c0352c3afa590\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Android\GradleRepository\caches\8.12\transforms\f527d98c4d43bc99c34c0352c3afa590\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Android\GradleRepository\caches\8.12\transforms\fc95bcae0ba172be27074a8a115ec3a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Android\GradleRepository\caches\8.12\transforms\fc95bcae0ba172be27074a8a115ec3a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:18:9-35
	android:label
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:17:9-41
	android:icon
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:16:9-43
	android:allowBackup
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:15:9-35
	android:theme
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:19:9-53
	android:usesCleartextTraffic
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:20:9-44
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:21:9-38
activity#com.example.myapplicationtv.MainActivity
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:22:9-35:20
	android:screenOrientation
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:29:13-50
	android:label
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:27:13-45
	android:exported
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:25:13-36
	android:icon
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:26:13-59
	android:banner
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:24:13-61
	android:logo
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:28:13-59
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:23:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:30:13-34:29
action#android.intent.action.MAIN
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:31:17-69
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:31:25-66
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:33:17-86
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:33:27-83
activity#com.example.myapplicationtv.DetailsActivity
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:38:13-37
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:37:13-44
activity#com.example.myapplicationtv.PlaybackActivity
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:39:9-41:40
	android:exported
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:41:13-37
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:40:13-45
activity#com.example.myapplicationtv.BrowseErrorActivity
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:42:9-44:40
	android:exported
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:44:13-37
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:43:13-48
activity#com.example.myapplicationtv.SearchActivity
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:45:9-47:40
	android:exported
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:47:13-37
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:46:13-43
activity#com.example.myapplicationtv.LoginActivity
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:48:9-50:40
	android:exported
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:50:13-37
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:49:13-42
activity#com.example.myapplicationtv.UserCenterActivity
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:51:9-53:40
	android:exported
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:53:13-37
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:52:13-47
activity#com.example.myapplicationtv.TestActivity
ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:54:9-56:40
	android:exported
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:56:13-37
	android:name
		ADDED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml:55:13-41
uses-sdk
INJECTED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml
INJECTED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml
MERGED from [androidx.leanback:leanback:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\89d10e7acdc9e8617fe8b024a2336641\transformed\leanback-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.leanback:leanback:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\89d10e7acdc9e8617fe8b024a2336641\transformed\leanback-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.11.0] D:\Android\GradleRepository\caches\8.12\transforms\59e3767b96f4ee90f4b57a0c56b92a96\transformed\glide-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.11.0] D:\Android\GradleRepository\caches\8.12\transforms\59e3767b96f4ee90f4b57a0c56b92a96\transformed\glide-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.fragment:fragment:1.6.2] D:\Android\GradleRepository\caches\8.12\transforms\ccfd53c838c812e96e4910096138f89c\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] D:\Android\GradleRepository\caches\8.12\transforms\ccfd53c838c812e96e4910096138f89c\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] D:\Android\GradleRepository\caches\8.12\transforms\374ceb5124eacb51f9cd2e0f0855c4f3\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] D:\Android\GradleRepository\caches\8.12\transforms\374ceb5124eacb51f9cd2e0f0855c4f3\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] D:\Android\GradleRepository\caches\8.12\transforms\cffe277412d6e71dab1701f200c8c21e\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] D:\Android\GradleRepository\caches\8.12\transforms\cffe277412d6e71dab1701f200c8c21e\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\22d0274585fcaae534e1fc3e6eb76e6e\transformed\vectordrawable-animated-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\22d0274585fcaae534e1fc3e6eb76e6e\transformed\vectordrawable-animated-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\ab3683f7a4d9a57eaa986edb4e5262fb\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\ab3683f7a4d9a57eaa986edb4e5262fb\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\d1cb9debee0ec7aff0e9116da1dc9922\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\d1cb9debee0ec7aff0e9116da1dc9922\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\2bd59f59ae2972a6ae2824d06af616c8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\2bd59f59ae2972a6ae2824d06af616c8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\cec8346c4e2ab21f142e352fe0bfc4ee\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\cec8346c4e2ab21f142e352fe0bfc4ee\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\5c68e73db408f53b49ab0ec6aa072138\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\5c68e73db408f53b49ab0ec6aa072138\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\3b3a598714bb2410226fd2e2c54e7e86\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\3b3a598714bb2410226fd2e2c54e7e86\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\8b16bf72240f7d4350db4f5688c2644d\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\8b16bf72240f7d4350db4f5688c2644d\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\e4d5bf82e4d5c2bf484b0d2d23ce3117\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\e4d5bf82e4d5c2bf484b0d2d23ce3117\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\e8d642ec3f0deb889900fe6cc9fe5cef\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\e8d642ec3f0deb889900fe6cc9fe5cef\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\d3acc5e891e2e7a0ee325b25454f074d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\d3acc5e891e2e7a0ee325b25454f074d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\3f0c6eafd88bfcc275cea1cacdb0338e\transformed\vectordrawable-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\3f0c6eafd88bfcc275cea1cacdb0338e\transformed\vectordrawable-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] D:\Android\GradleRepository\caches\8.12\transforms\22ec3492cd38aff0fdd3abd4ae94272b\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] D:\Android\GradleRepository\caches\8.12\transforms\22ec3492cd38aff0fdd3abd4ae94272b\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] D:\Android\GradleRepository\caches\8.12\transforms\9717948c970c19acce1692941c22c532\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] D:\Android\GradleRepository\caches\8.12\transforms\9717948c970c19acce1692941c22c532\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\8c1f2d282f20138de146e7f45bde41c9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\8c1f2d282f20138de146e7f45bde41c9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\6c7c511ec8b68e45e391fb30b6d5f144\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\6c7c511ec8b68e45e391fb30b6d5f144\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\Android\GradleRepository\caches\8.12\transforms\220f8c0f15f6aa3f199c619409bad374\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\Android\GradleRepository\caches\8.12\transforms\220f8c0f15f6aa3f199c619409bad374\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\Android\GradleRepository\caches\8.12\transforms\b9b3bed66d3a00e5a31ccb8c12557bab\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\Android\GradleRepository\caches\8.12\transforms\b9b3bed66d3a00e5a31ccb8c12557bab\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\e986c459db27042bd4bd99648aeb71d5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\e986c459db27042bd4bd99648aeb71d5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\6d91530bd68c1a5268acfee80590353b\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\6d91530bd68c1a5268acfee80590353b\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\0b77184f006223771d62c67bbd676e73\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\0b77184f006223771d62c67bbd676e73\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\09b2070107f58b91baaaa03824de0d80\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\09b2070107f58b91baaaa03824de0d80\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\2cd992ff3743b623f80bb69d849f50ca\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\2cd992ff3743b623f80bb69d849f50ca\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\e2630f862baa837dd8db5f1a6cd2b6cf\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\e2630f862baa837dd8db5f1a6cd2b6cf\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\44b9bab188f5f5366f3115537e09a453\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\44b9bab188f5f5366f3115537e09a453\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\9262bdae479e1b658eb70c7bbd10b157\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\9262bdae479e1b658eb70c7bbd10b157\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\e71662f1e872c884e7f2d1e2bf0f11d8\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] D:\Android\GradleRepository\caches\8.12\transforms\e71662f1e872c884e7f2d1e2bf0f11d8\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\6758fc4f4664cc3e72a8df28e7d3f5da\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\6758fc4f4664cc3e72a8df28e7d3f5da\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\03f0f7d3357ba78868c8f6b45210dab1\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\03f0f7d3357ba78868c8f6b45210dab1\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] D:\Android\GradleRepository\caches\8.12\transforms\e9cf136272e11e6df2f8a99b3e3d46e1\transformed\gifdecoder-4.11.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.11.0] D:\Android\GradleRepository\caches\8.12\transforms\e9cf136272e11e6df2f8a99b3e3d46e1\transformed\gifdecoder-4.11.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\2820c43de1ddfedc63a9c2819f02c6e3\transformed\exifinterface-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\2820c43de1ddfedc63a9c2819f02c6e3\transformed\exifinterface-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\a0f862da65c20cd37353b7a213469561\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\a0f862da65c20cd37353b7a213469561\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Android\GradleRepository\caches\8.12\transforms\f527d98c4d43bc99c34c0352c3afa590\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Android\GradleRepository\caches\8.12\transforms\f527d98c4d43bc99c34c0352c3afa590\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\d37b93b53f0b4b52458233873d9d92c6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\d37b93b53f0b4b52458233873d9d92c6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\Android\GradleRepository\caches\8.12\transforms\f995e7e8cc3070312bef138ddf684057\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\Android\GradleRepository\caches\8.12\transforms\f995e7e8cc3070312bef138ddf684057\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\2973b6edbd6255a26873f431c1cacfe3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\2973b6edbd6255a26873f431c1cacfe3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\0fceed22a269c56fb956e6a7c7b2505d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\0fceed22a269c56fb956e6a7c7b2505d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\1aea720ee1e923fbe1226a3f16df1f37\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\1aea720ee1e923fbe1226a3f16df1f37\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Android\GradleRepository\caches\8.12\transforms\fc95bcae0ba172be27074a8a115ec3a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Android\GradleRepository\caches\8.12\transforms\fc95bcae0ba172be27074a8a115ec3a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\18a099003c1c7bc56d0ff2fb22dccf68\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] D:\Android\GradleRepository\caches\8.12\transforms\18a099003c1c7bc56d0ff2fb22dccf68\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\aikaifa\MovieTV\MyApplication2\app\src\main\AndroidManifest.xml
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.example.myapplicationtv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.myapplicationtv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Android\GradleRepository\caches\8.12\transforms\fc95bcae0ba172be27074a8a115ec3a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Android\GradleRepository\caches\8.12\transforms\fc95bcae0ba172be27074a8a115ec3a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
