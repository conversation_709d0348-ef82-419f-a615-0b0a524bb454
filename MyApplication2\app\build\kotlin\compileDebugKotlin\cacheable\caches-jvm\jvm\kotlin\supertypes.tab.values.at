/ Header Record For PersistentHashMapValueStorage' &androidx.fragment.app.FragmentActivity androidx.fragment.app.Fragment# "androidx.leanback.widget.Presenter0 /com.example.myapplicationtv.base.BaseTVActivity= <androidx.leanback.widget.AbstractDetailsDescriptionPresenter+ *androidx.leanback.app.ErrorSupportFragment0 /com.example.myapplicationtv.base.BaseTVActivity androidx.fragment.app.Fragment0 /com.example.myapplicationtv.base.BaseTVActivity androidx.fragment.app.Fragment0 /com.example.myapplicationtv.base.BaseTVActivity androidx.fragment.app.Fragment, +androidx.leanback.app.BrowseSupportFragment3 2androidx.leanback.widget.OnItemViewClickedListener4 3androidx.leanback.widget.OnItemViewSelectedListener java.util.TimerTask# "androidx.leanback.widget.Presenter java.io.Serializable java.io.Serializable0 /com.example.myapplicationtv.base.BaseTVActivity+ *androidx.leanback.app.VideoSupportFragment0 /com.example.myapplicationtv.base.BaseTVActivitym +<EMAIL>.SearchResultProvider3 2androidx.leanback.widget.OnItemViewClickedListener0 /com.example.myapplicationtv.base.BaseTVActivity androidx.fragment.app.Fragment android.app.Application0 /com.example.myapplicationtv.base.BaseTVActivity0 /com.example.myapplicationtv.base.BaseTVActivity, +androidx.leanback.app.BrowseSupportFragment3 2androidx.leanback.widget.OnItemViewClickedListener4 3androidx.leanback.widget.OnItemViewSelectedListener# "androidx.leanback.widget.Presenter- ,androidx.leanback.app.DetailsSupportFragment3 2androidx.leanback.widget.OnItemViewClickedListener2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder' &androidx.fragment.app.FragmentActivity java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable kotlin.Enum!  com.google.gson.JsonDeserializer androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel' &androidx.leanback.widget.ImageCardView0 /com.example.myapplicationtv.base.BaseTVActivity androidx.fragment.app.Fragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.lifecycle.ViewModel' &androidx.fragment.app.FragmentActivity androidx.fragment.app.Fragment# "androidx.leanback.widget.Presenter0 /com.example.myapplicationtv.base.BaseTVActivity+ *androidx.leanback.app.ErrorSupportFragment0 /com.example.myapplicationtv.base.BaseTVActivity androidx.fragment.app.Fragment0 /com.example.myapplicationtv.base.BaseTVActivity androidx.fragment.app.Fragment0 /com.example.myapplicationtv.base.BaseTVActivity androidx.fragment.app.Fragment, +androidx.leanback.app.BrowseSupportFragment3 2androidx.leanback.widget.OnItemViewClickedListener4 3androidx.leanback.widget.OnItemViewSelectedListener java.util.TimerTask# "androidx.leanback.widget.Presenter0 /com.example.myapplicationtv.base.BaseTVActivity0 /com.example.myapplicationtv.base.BaseTVActivity androidx.fragment.app.Fragment0 /com.example.myapplicationtv.base.BaseTVActivity0 /com.example.myapplicationtv.base.BaseTVActivity- ,androidx.leanback.app.DetailsSupportFragment3 2androidx.leanback.widget.OnItemViewClickedListener2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder