/ Header Record For PersistentHashMapValueStorage' &androidx.fragment.app.FragmentActivity androidx.fragment.app.Fragment# "androidx.leanback.widget.Presenter' &androidx.fragment.app.FragmentActivity= <androidx.leanback.widget.AbstractDetailsDescriptionPresenter+ *androidx.leanback.app.ErrorSupportFragment' &androidx.fragment.app.FragmentActivity, +androidx.leanback.app.BrowseSupportFragment3 2androidx.leanback.widget.OnItemViewClickedListener4 3androidx.leanback.widget.OnItemViewSelectedListener java.util.TimerTask# "androidx.leanback.widget.Presenter java.io.Serializable' &androidx.fragment.app.FragmentActivity+ *androidx.leanback.app.VideoSupportFragment- ,androidx.leanback.app.DetailsSupportFragment3 2androidx.leanback.widget.OnItemViewClickedListener0 /com.example.myapplicationtv.base.BaseTVActivity androidx.fragment.app.Fragment java.io.Serializable0 /com.example.myapplicationtv.base.BaseTVActivitym +<EMAIL>.SearchResultProvider3 2androidx.leanback.widget.OnItemViewClickedListener android.app.Application0 /com.example.myapplicationtv.base.BaseTVActivity0 /com.example.myapplicationtv.base.BaseTVActivity, +androidx.leanback.app.BrowseSupportFragment3 2androidx.leanback.widget.OnItemViewClickedListener4 3androidx.leanback.widget.OnItemViewSelectedListener# "androidx.leanback.widget.Presenter' &androidx.fragment.app.FragmentActivity java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel' &androidx.leanback.widget.ImageCardView' &androidx.fragment.app.FragmentActivity androidx.fragment.app.Fragment# "androidx.leanback.widget.Presenter0 /com.example.myapplicationtv.base.BaseTVActivity= <androidx.leanback.widget.AbstractDetailsDescriptionPresenter+ *androidx.leanback.app.ErrorSupportFragment0 /com.example.myapplicationtv.base.BaseTVActivity, +androidx.leanback.app.BrowseSupportFragment3 2androidx.leanback.widget.OnItemViewClickedListener4 3androidx.leanback.widget.OnItemViewSelectedListener java.util.TimerTask# "androidx.leanback.widget.Presenter java.io.Serializable0 /com.example.myapplicationtv.base.BaseTVActivity+ *androidx.leanback.app.VideoSupportFragment- ,androidx.leanback.app.DetailsSupportFragment3 2androidx.leanback.widget.OnItemViewClickedListener0 /com.example.myapplicationtv.base.BaseTVActivity androidx.lifecycle.ViewModel java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable kotlin.Enum java.io.Serializable java.io.Serializable+ *androidx.leanback.app.VideoSupportFragment androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable java.io.Serializable kotlin.Enum java.io.Serializable java.io.Serializable+ *androidx.leanback.app.VideoSupportFragment androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel