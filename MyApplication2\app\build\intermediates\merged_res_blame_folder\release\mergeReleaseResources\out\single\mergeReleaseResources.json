[{"merged": "com.example.myapplicationtv.app-release-31:/drawable_default_background.xml.flat", "source": "com.example.myapplicationtv.app-main-32:/drawable/default_background.xml"}, {"merged": "com.example.myapplicationtv.app-release-31:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.myapplicationtv.app-main-32:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.myapplicationtv.app-release-31:/layout_activity_test.xml.flat", "source": "com.example.myapplicationtv.app-main-32:/layout/activity_test.xml"}, {"merged": "com.example.myapplicationtv.app-release-31:/layout_activity_user_center.xml.flat", "source": "com.example.myapplicationtv.app-main-32:/layout/activity_user_center.xml"}, {"merged": "com.example.myapplicationtv.app-release-31:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.myapplicationtv.app-main-32:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.myapplicationtv.app-release-31:/layout_activity_search.xml.flat", "source": "com.example.myapplicationtv.app-main-32:/layout/activity_search.xml"}, {"merged": "com.example.myapplicationtv.app-release-31:/layout_activity_login.xml.flat", "source": "com.example.myapplicationtv.app-main-32:/layout/activity_login.xml"}, {"merged": "com.example.myapplicationtv.app-release-31:/drawable_movie.png.flat", "source": "com.example.myapplicationtv.app-main-32:/drawable/movie.png"}, {"merged": "com.example.myapplicationtv.app-release-31:/layout_fragment_login.xml.flat", "source": "com.example.myapplicationtv.app-main-32:/layout/fragment_login.xml"}, {"merged": "com.example.myapplicationtv.app-release-31:/drawable_button_background.xml.flat", "source": "com.example.myapplicationtv.app-main-32:/drawable/button_background.xml"}, {"merged": "com.example.myapplicationtv.app-release-31:/layout_activity_main.xml.flat", "source": "com.example.myapplicationtv.app-main-32:/layout/activity_main.xml"}, {"merged": "com.example.myapplicationtv.app-release-31:/drawable_edit_text_background.xml.flat", "source": "com.example.myapplicationtv.app-main-32:/drawable/edit_text_background.xml"}, {"merged": "com.example.myapplicationtv.app-release-31:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.myapplicationtv.app-main-32:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.myapplicationtv.app-release-31:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.myapplicationtv.app-main-32:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.myapplicationtv.app-release-31:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.myapplicationtv.app-main-32:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.myapplicationtv.app-release-31:/drawable_app_icon_your_company.png.flat", "source": "com.example.myapplicationtv.app-main-32:/drawable/app_icon_your_company.png"}, {"merged": "com.example.myapplicationtv.app-release-31:/layout_activity_details.xml.flat", "source": "com.example.myapplicationtv.app-main-32:/layout/activity_details.xml"}]