package com.example.myapplicationtv

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.core.app.ActivityOptionsCompat
import androidx.leanback.app.SearchSupportFragment
import androidx.leanback.widget.*
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.Observer
import com.example.myapplicationtv.viewmodel.SearchViewModel
import com.example.myapplicationtv.model.ContentType

/**
 * 搜索Fragment
 */
class SearchFragment : SearchSupportFragment(), SearchSupportFragment.SearchResultProvider {

    private lateinit var viewModel: SearchViewModel
    private lateinit var mRowsAdapter: ArrayObjectAdapter
    private val cardPresenter = CardPresenter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        initViewModel()
        setupUI()
    }

    private fun initViewModel() {
        viewModel = ViewModelProvider(this)[SearchViewModel::class.java]
        
        // 观察搜索结果
        viewModel.searchResults.observe(this, Observer { results ->
            updateSearchResults(results)
        })
        
        // 观察加载状态
        viewModel.isLoading.observe(this, Observer { isLoading ->
            Log.d(TAG, "Loading: $isLoading")
        })
        
        // 观察错误信息
        viewModel.error.observe(this, Observer { error ->
            if (error.isNotEmpty()) {
                Toast.makeText(context, error, Toast.LENGTH_LONG).show()
                Log.e(TAG, "Error: $error")
            }
        })
    }

    private fun setupUI() {
        setSearchResultProvider(this)
        setOnItemViewClickedListener(ItemViewClickedListener())
        
        // 设置搜索提示
        setBadgeDrawable(resources.getDrawable(R.drawable.app_icon_your_company, null))
        setTitle("搜索")
        setSearchAffordanceColors(
            SearchSupportFragment.SearchAffordanceColors(
                resources.getColor(R.color.search_opaque, null),
                resources.getColor(R.color.search_opaque, null)
            )
        )
    }

    override fun getResultsAdapter(): ObjectAdapter {
        mRowsAdapter = ArrayObjectAdapter(ListRowPresenter())
        return mRowsAdapter
    }

    override fun onQueryTextChange(newQuery: String): Boolean {
        Log.d(TAG, "Search query: $newQuery")
        if (newQuery.isNotEmpty()) {
            viewModel.search(newQuery)
        } else {
            mRowsAdapter.clear()
        }
        return true
    }

    override fun onQueryTextSubmit(query: String): Boolean {
        Log.d(TAG, "Search submit: $query")
        if (query.isNotEmpty()) {
            viewModel.search(query)
        }
        return true
    }

    private fun updateSearchResults(results: List<ContentItem>) {
        mRowsAdapter.clear()
        
        if (results.isEmpty()) {
            // 显示无结果提示
            val emptyAdapter = ArrayObjectAdapter(cardPresenter)
            val header = HeaderItem(0, "无搜索结果")
            mRowsAdapter.add(ListRow(header, emptyAdapter))
            return
        }

        // 按类型分组显示结果
        val movieResults = results.filter { it.type == ContentType.MOVIE }
        val appResults = results.filter { it.type == ContentType.APP }
        val gameResults = results.filter { it.type == ContentType.GAME }
        val productResults = results.filter { it.type == ContentType.PRODUCT }

        var rowIndex = 0

        if (movieResults.isNotEmpty()) {
            val movieAdapter = ArrayObjectAdapter(cardPresenter)
            movieResults.forEach { movieAdapter.add(it) }
            val header = HeaderItem(rowIndex++.toLong(), "电影 (${movieResults.size})")
            mRowsAdapter.add(ListRow(header, movieAdapter))
        }

        if (appResults.isNotEmpty()) {
            val appAdapter = ArrayObjectAdapter(cardPresenter)
            appResults.forEach { appAdapter.add(it) }
            val header = HeaderItem(rowIndex++.toLong(), "应用 (${appResults.size})")
            mRowsAdapter.add(ListRow(header, appAdapter))
        }

        if (gameResults.isNotEmpty()) {
            val gameAdapter = ArrayObjectAdapter(cardPresenter)
            gameResults.forEach { gameAdapter.add(it) }
            val header = HeaderItem(rowIndex++.toLong(), "游戏 (${gameResults.size})")
            mRowsAdapter.add(ListRow(header, gameAdapter))
        }

        if (productResults.isNotEmpty()) {
            val productAdapter = ArrayObjectAdapter(cardPresenter)
            productResults.forEach { productAdapter.add(it) }
            val header = HeaderItem(rowIndex++.toLong(), "商品 (${productResults.size})")
            mRowsAdapter.add(ListRow(header, productAdapter))
        }
    }

    private inner class ItemViewClickedListener : OnItemViewClickedListener {
        override fun onItemClicked(
            itemViewHolder: Presenter.ViewHolder,
            item: Any,
            rowViewHolder: RowPresenter.ViewHolder,
            row: Row
        ) {
            if (item is ContentItem) {
                Log.d(TAG, "ContentItem clicked: ${item.type} - ${item.title}")
                val intent = Intent(activity!!, DetailsActivity::class.java)
                intent.putExtra(DetailsActivity.CONTENT_ITEM, item)

                val bundle = ActivityOptionsCompat.makeSceneTransitionAnimation(
                    activity!!,
                    (itemViewHolder.view as ImageCardView).mainImageView,
                    DetailsActivity.SHARED_ELEMENT_NAME
                ).toBundle()
                startActivity(intent, bundle)
            }
        }
    }

    companion object {
        private const val TAG = "SearchFragment"
    }
}
