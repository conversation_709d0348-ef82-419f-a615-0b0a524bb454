{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-uk/values-uk.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,307,408,509,614,719,5303", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "200,302,403,504,609,714,827,5399"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,507,628,739,835,926,1049,1172,1282,1410,1571,1694,1817,1923,2020,2158,2253,2357,2463,2576,2679,2801,2920,3038,3156,3274,3397,3525,3657,3780,3907,3994,4077,4189,4325,4485", "endColumns": "106,100,99,93,120,110,95,90,122,122,109,127,160,122,122,105,96,137,94,103,105,112,102,121,118,117,117,117,122,127,131,122,126,86,82,111,135,159,90", "endOffsets": "207,308,408,502,623,734,830,921,1044,1167,1277,1405,1566,1689,1812,1918,2015,2153,2248,2352,2458,2571,2674,2796,2915,3033,3151,3269,3392,3520,3652,3775,3902,3989,4072,4184,4320,4480,4571"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,939,1040,1140,1234,1355,1466,1562,1653,1776,1899,2009,2137,2298,2421,2544,2650,2747,2885,2980,3084,3190,3303,3406,3528,3647,3765,3883,4001,4124,4252,4384,4507,4634,4721,4804,4916,5052,5212", "endColumns": "106,100,99,93,120,110,95,90,122,122,109,127,160,122,122,105,96,137,94,103,105,112,102,121,118,117,117,117,122,127,131,122,126,86,82,111,135,159,90", "endOffsets": "934,1035,1135,1229,1350,1461,1557,1648,1771,1894,2004,2132,2293,2416,2539,2645,2742,2880,2975,3079,3185,3298,3401,3523,3642,3760,3878,3996,4119,4247,4379,4502,4629,4716,4799,4911,5047,5207,5298"}}]}]}