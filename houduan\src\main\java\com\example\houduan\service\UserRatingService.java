package com.example.houduan.service;

import com.example.houduan.dto.RatingRequest;
import com.example.houduan.entity.Movie;
import com.example.houduan.entity.UserRating;
import com.example.houduan.mapper.UserRatingMapper;
import com.example.houduan.mapper.MovieMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户评分服务类
 */
@Service
@RequiredArgsConstructor
public class UserRatingService {

    private final UserRatingMapper userRatingMapper;
    private final MovieMapper movieMapper;

    /**
     * 提交评分
     */
    public void submitRating(String userId, RatingRequest request) {
        // 检查电影是否存在
        Movie movie = movieMapper.findById(request.getMovieId()).orElse(null);
        if (movie == null) {
            throw new RuntimeException("影片不存在");
        }

        // 查找是否已有评分记录
        UserRating rating = userRatingMapper.findByUserIdAndMovieId(userId, request.getMovieId()).orElse(null);

        if (rating == null) {
            // 创建新的评分记录
            rating = new UserRating();
            rating.setUserId(userId);
            rating.setMovieId(request.getMovieId());
            rating.setRating(request.getRating());
            rating.setComment(request.getComment());
            rating.setCreateTime(LocalDateTime.now());
            rating.setUpdateTime(LocalDateTime.now());
            userRatingMapper.save(rating);
        } else {
            // 更新现有评分
            rating.setRating(request.getRating());
            rating.setComment(request.getComment());
            rating.setUpdateTime(LocalDateTime.now());
            userRatingMapper.save(rating);
        }

        // 更新电影的平均评分
        updateMovieRating(request.getMovieId());
    }

    /**
     * 获取用户对电影的评分
     */
    public UserRating getUserRating(String userId, String movieId) {
        return userRatingMapper.findByUserIdAndMovieId(userId, movieId).orElse(null);
    }

    /**
     * 删除评分
     */
    public void deleteRating(String userId, String movieId) {
        UserRating rating = userRatingMapper.findByUserIdAndMovieId(userId, movieId).orElse(null);
        if (rating == null) {
            throw new RuntimeException("评分记录不存在");
        }

        userRatingMapper.deleteById(rating.getId());

        // 更新电影的平均评分
        updateMovieRating(movieId);
    }

    /**
     * 获取电影的所有评分
     */
    public List<UserRating> getMovieRatings(String movieId) {
        return userRatingMapper.findByMovieIdOrderByCreateTimeDesc(movieId);
    }

    /**
     * 更新电影的平均评分
     */
    private void updateMovieRating(String movieId) {
        List<UserRating> ratings = userRatingMapper.findByMovieIdOrderByCreateTimeDesc(movieId);

        Movie movie = movieMapper.findById(movieId).orElse(null);
        if (movie == null) {
            return;
        }

        if (ratings.isEmpty()) {
            // 没有评分，重置为0
            movie.setRating(BigDecimal.ZERO);
            movie.setRatingCount(0);
        } else {
            // 计算平均评分
            BigDecimal totalRating = ratings.stream()
                    .map(UserRating::getRating)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal avgRating = totalRating.divide(
                    BigDecimal.valueOf(ratings.size()),
                    1,
                    RoundingMode.HALF_UP
            );

            movie.setRating(avgRating);
            movie.setRatingCount(ratings.size());
        }

        movieMapper.save(movie);
    }
}
