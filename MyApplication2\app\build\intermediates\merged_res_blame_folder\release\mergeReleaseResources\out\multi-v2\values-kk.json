{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-kk/values-kk.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,404,507,611,708,5209", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "195,297,399,502,606,703,814,5305"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,507,630,743,841,932,1056,1178,1285,1408,1582,1702,1820,1930,2023,2160,2251,2359,2472,2579,2681,2800,2931,3047,3161,3263,3370,3486,3610,3722,3842,3929,4012,4116,4250,4405", "endColumns": "106,100,99,93,122,112,97,90,123,121,106,122,173,119,117,109,92,136,90,107,112,106,101,118,130,115,113,101,106,115,123,111,119,86,82,103,133,154,89", "endOffsets": "207,308,408,502,625,738,836,927,1051,1173,1280,1403,1577,1697,1815,1925,2018,2155,2246,2354,2467,2574,2676,2795,2926,3042,3156,3258,3365,3481,3605,3717,3837,3924,4007,4111,4245,4400,4490"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "819,926,1027,1127,1221,1344,1457,1555,1646,1770,1892,1999,2122,2296,2416,2534,2644,2737,2874,2965,3073,3186,3293,3395,3514,3645,3761,3875,3977,4084,4200,4324,4436,4556,4643,4726,4830,4964,5119", "endColumns": "106,100,99,93,122,112,97,90,123,121,106,122,173,119,117,109,92,136,90,107,112,106,101,118,130,115,113,101,106,115,123,111,119,86,82,103,133,154,89", "endOffsets": "921,1022,1122,1216,1339,1452,1550,1641,1765,1887,1994,2117,2291,2411,2529,2639,2732,2869,2960,3068,3181,3288,3390,3509,3640,3756,3870,3972,4079,4195,4319,4431,4551,4638,4721,4825,4959,5114,5204"}}]}]}