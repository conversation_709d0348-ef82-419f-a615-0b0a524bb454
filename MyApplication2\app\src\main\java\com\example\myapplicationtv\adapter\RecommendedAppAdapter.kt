package com.example.myapplicationtv.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RatingBar
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.ContentItem
import com.example.myapplicationtv.R
import com.example.myapplicationtv.model.ContentType

/**
 * 推荐应用适配器
 */
class RecommendedAppAdapter(
    private val onAppClick: (ContentItem) -> Unit
) : RecyclerView.Adapter<RecommendedAppAdapter.AppViewHolder>() {

    private var apps = listOf<ContentItem>()

    fun updateApps(newApps: List<ContentItem>) {
        apps = newApps
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AppViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_recommended_app, parent, false)
        return AppViewHolder(view)
    }

    override fun onBindViewHolder(holder: AppViewHolder, position: Int) {
        holder.bind(apps[position])
    }

    override fun getItemCount(): Int = apps.size

    inner class AppViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivAppIcon: ImageView = itemView.findViewById(R.id.iv_app_icon)
        private val tvAppName: TextView = itemView.findViewById(R.id.tv_app_name)
        private val tvAppDescription: TextView = itemView.findViewById(R.id.tv_app_description)
        private val ratingBar: RatingBar = itemView.findViewById(R.id.rating_bar)

        fun bind(app: ContentItem) {
            tvAppName.text = app.title
            tvAppDescription.text = app.description
            
            // 设置评分
            app.rating?.let { rating ->
                ratingBar.rating = rating.toFloat() / 2f // 转换为5星制
            }

            // 根据应用类型设置图标
            val iconRes = when {
                app.title?.contains("爱奇艺") == true -> R.drawable.ic_video
                app.title?.contains("腾讯视频") == true -> R.drawable.ic_video
                app.title?.contains("抖音") == true -> R.drawable.ic_video
                app.title?.contains("支付宝") == true -> R.drawable.ic_payment
                app.title?.contains("淘宝") == true -> R.drawable.ic_shopping
                app.title?.contains("高德地图") == true -> R.drawable.ic_map
                app.title?.contains("网易云音乐") == true -> R.drawable.ic_music
                app.title?.contains("QQ") == true -> R.drawable.ic_social
                app.title?.contains("微信") == true -> R.drawable.ic_social
                app.title?.contains("百度网盘") == true -> R.drawable.ic_cloud
                app.title?.contains("钉钉") == true -> R.drawable.ic_work
                app.title?.contains("美团") == true -> R.drawable.ic_service
                app.title?.contains("学而思") == true -> R.drawable.ic_education
                app.title?.contains("WPS") == true -> R.drawable.ic_tools
                app.title?.contains("今日头条") == true -> R.drawable.ic_news
                else -> R.drawable.ic_apps
            }
            ivAppIcon.setImageResource(iconRes)

            itemView.setOnClickListener {
                onAppClick(app)
            }
        }
    }
}
