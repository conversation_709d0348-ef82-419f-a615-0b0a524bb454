package com.example.myapplicationtv

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.adapter.NavigationAdapter
import com.example.myapplicationtv.base.BaseTVActivity
import com.example.myapplicationtv.model.NavigationItem
import com.example.myapplicationtv.utils.UserManager
import java.text.SimpleDateFormat
import java.util.*

/**
 * 自定义TV主界面
 */
class MainActivity : BaseTVActivity() {

    private lateinit var tvTime: TextView
    private lateinit var tvDate: TextView
    private lateinit var tvWeather: TextView
    private lateinit var rvNavigation: RecyclerView
    private lateinit var navigationAdapter: NavigationAdapter

    private val timeHandler = Handler(Looper.getMainLooper())
    private val timeRunnable = object : Runnable {
        override fun run() {
            updateTimeAndDate()
            timeHandler.postDelayed(this, 1000)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 检查用户登录状态
        if (!UserManager.isLoggedIn()) {
            // 未登录，跳转到登录页面
            val intent = Intent(this, LoginActivity::class.java)
            startActivity(intent)
            finish()
            return
        }

        setContentView(R.layout.activity_main)

        initViews()
        setupNavigation()
        setupMainContent()

        // 开始时间更新
        timeHandler.post(timeRunnable)
    }

    private fun initViews() {
        tvTime = findViewById(R.id.tv_time)
        tvDate = findViewById(R.id.tv_date)
        tvWeather = findViewById(R.id.tv_weather)
        rvNavigation = findViewById(R.id.rv_navigation)

        // 搜索按钮点击事件
        findViewById<android.widget.ImageButton>(R.id.btn_search).setOnClickListener {
            val intent = Intent(this, SearchActivity::class.java)
            startActivity(intent)
        }
    }

    private fun setupNavigation() {
        val navigationItems = listOf(
            NavigationItem("直播", R.drawable.ic_live_tv, "live"),
            NavigationItem("电影", R.drawable.ic_movie, "movie"),
            NavigationItem("应用", R.drawable.ic_apps, "app"),
            NavigationItem("游戏", R.drawable.ic_games, "game"),
            NavigationItem("商品", R.drawable.ic_shopping, "product"),
            NavigationItem("我的", R.drawable.ic_person, "profile")
        )

        navigationAdapter = NavigationAdapter(navigationItems) { item ->
            handleNavigationClick(item)
        }

        rvNavigation.layoutManager = LinearLayoutManager(this)
        rvNavigation.adapter = navigationAdapter
    }

    private fun setupMainContent() {
        if (supportFragmentManager.findFragmentById(R.id.main_content_fragment) == null) {
            supportFragmentManager.beginTransaction()
                .replace(R.id.main_content_fragment, MainContentFragment())
                .commitNow()
        }

        // 延迟设置初始焦点，确保Fragment已经完全加载
        findViewById<View>(R.id.main_content_fragment)?.post {
            // 尝试找到第一个可聚焦的元素并设置焦点
            val mainContentView = findViewById<View>(R.id.main_content_fragment)
            val firstFocusable = mainContentView?.focusSearch(View.FOCUS_DOWN)
            firstFocusable?.requestFocus()
        }
    }

    private fun handleNavigationClick(item: NavigationItem) {
        Log.d("MainActivity", "Navigation clicked: ${item.title} (${item.type})")
        when (item.type) {
            "live" -> {
                // 处理直播点击
                Log.d("MainActivity", "Live clicked - not implemented yet")
            }
            "movie" -> {
                // 处理电影点击
                Log.d("MainActivity", "Movie clicked - not implemented yet")
            }
            "app" -> {
                // 处理应用点击
                Log.d("MainActivity", "App clicked - launching ApplicationActivity")
                val intent = Intent(this, ApplicationActivity::class.java)
                startActivity(intent)
            }
            "game" -> {
                // 处理游戏点击
                Log.d("MainActivity", "Navigating to GameActivity")
                val intent = Intent(this, GameActivity::class.java)
                startActivity(intent)
            }
            "product" -> {
                // 处理商品点击
                Log.d("MainActivity", "Navigating to ShopActivity")
                val intent = Intent(this, ShopActivity::class.java)
                startActivity(intent)
            }
            "profile" -> {
                Log.d("MainActivity", "Navigating to UserCenter")
                val intent = Intent(this, UserCenterActivity::class.java)
                startActivity(intent)
            }
        }
    }

    private fun updateTimeAndDate() {
        val now = Calendar.getInstance()

        // 更新时间
        val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
        tvTime.text = timeFormat.format(now.time)

        // 更新日期
        val dateFormat = SimpleDateFormat("EEEE M月d日", Locale.CHINESE)
        tvDate.text = dateFormat.format(now.time)

        // 模拟天气信息
        tvWeather.text = "晴朗 28°C"
    }

    override fun onDestroy() {
        super.onDestroy()
        timeHandler.removeCallbacks(timeRunnable)
    }
}