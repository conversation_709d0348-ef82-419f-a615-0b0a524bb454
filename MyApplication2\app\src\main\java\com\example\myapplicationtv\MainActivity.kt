package com.example.myapplicationtv

import android.content.Intent
import android.os.Bundle
import com.example.myapplicationtv.base.BaseTVActivity
import com.example.myapplicationtv.utils.UserManager

/**
 * Loads [MainFragment].
 */
class MainActivity : BaseTVActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 检查用户登录状态
        if (!UserManager.isLoggedIn()) {
            // 未登录，跳转到登录页面
            val intent = Intent(this, LoginActivity::class.java)
            startActivity(intent)
            finish()
            return
        }

        setContentView(R.layout.activity_main)
        if (savedInstanceState == null) {
            getSupportFragmentManager().beginTransaction()
                .replace(R.id.main_browse_fragment, MainFragment())
                .commitNow()
        }
    }
}