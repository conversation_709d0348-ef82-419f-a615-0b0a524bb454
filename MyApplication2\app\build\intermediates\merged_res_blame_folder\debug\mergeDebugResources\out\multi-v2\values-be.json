{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-be/values-be.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,506,612,715,5313", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "198,300,400,501,607,710,831,5409"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,510,638,749,846,937,1067,1195,1304,1429,1587,1712,1835,1941,2032,2169,2263,2367,2478,2592,2689,2808,2933,3051,3167,3288,3414,3536,3670,3787,3916,4003,4086,4194,4334,4500", "endColumns": "106,100,99,96,127,110,96,90,129,127,108,124,157,124,122,105,90,136,93,103,110,113,96,118,124,117,115,120,125,121,133,116,128,86,82,107,139,165,81", "endOffsets": "207,308,408,505,633,744,841,932,1062,1190,1299,1424,1582,1707,1830,1936,2027,2164,2258,2362,2473,2587,2684,2803,2928,3046,3162,3283,3409,3531,3665,3782,3911,3998,4081,4189,4329,4495,4577"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,943,1044,1144,1241,1369,1480,1577,1668,1798,1926,2035,2160,2318,2443,2566,2672,2763,2900,2994,3098,3209,3323,3420,3539,3664,3782,3898,4019,4145,4267,4401,4518,4647,4734,4817,4925,5065,5231", "endColumns": "106,100,99,96,127,110,96,90,129,127,108,124,157,124,122,105,90,136,93,103,110,113,96,118,124,117,115,120,125,121,133,116,128,86,82,107,139,165,81", "endOffsets": "938,1039,1139,1236,1364,1475,1572,1663,1793,1921,2030,2155,2313,2438,2561,2667,2758,2895,2989,3093,3204,3318,3415,3534,3659,3777,3893,4014,4140,4262,4396,4513,4642,4729,4812,4920,5060,5226,5308"}}]}]}