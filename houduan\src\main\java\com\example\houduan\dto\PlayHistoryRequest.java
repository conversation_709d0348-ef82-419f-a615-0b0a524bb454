package com.example.houduan.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 播放历史请求DTO
 */
@Data
public class PlayHistoryRequest {

    @NotBlank(message = "电影ID不能为空")
    private String movieId;

    private String chapterId;

    @NotNull(message = "播放进度不能为空")
    private Integer progress;

    @NotNull(message = "总时长不能为空")
    private Integer duration;
}
