package com.example.myapplicationtv.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplicationtv.ContentItem
import com.example.myapplicationtv.repository.ContentRepository
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay

/**
 * 搜索ViewModel
 */
class SearchViewModel : ViewModel() {
    
    private val repository = ContentRepository()
    
    // 搜索结果
    private val _searchResults = MutableLiveData<List<ContentItem>>()
    val searchResults: LiveData<List<ContentItem>> = _searchResults
    
    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    // 错误信息
    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    
    // 搜索任务
    private var searchJob: Job? = null
    
    /**
     * 搜索内容
     */
    fun search(query: String) {
        // 取消之前的搜索任务
        searchJob?.cancel()
        
        searchJob = viewModelScope.launch {
            try {
                // 防抖处理，延迟300ms执行搜索
                delay(300)
                
                _isLoading.value = true
                _error.value = ""
                
                val results = mutableListOf<ContentItem>()
                
                // 并行搜索所有类型的内容
                searchMovies(query, results)
                searchApps(query, results)
                searchGames(query, results)
                searchProducts(query, results)
                
                _searchResults.value = results
                
            } catch (e: Exception) {
                _error.value = "搜索失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 搜索电影
     */
    private suspend fun searchMovies(query: String, results: MutableList<ContentItem>) {
        repository.getMovies(keyword = query, size = 20).fold(
            onSuccess = { pageResult ->
                val movieItems = pageResult.list.map { ContentItem.fromMovieResponse(it) }
                results.addAll(movieItems)
            },
            onFailure = { e ->
                // 记录错误但不中断其他搜索
                android.util.Log.e("SearchViewModel", "搜索电影失败: ${e.message}")
            }
        )
    }
    
    /**
     * 搜索应用
     */
    private suspend fun searchApps(query: String, results: MutableList<ContentItem>) {
        repository.getApps(keyword = query, size = 20).fold(
            onSuccess = { pageResult ->
                val appItems = pageResult.list.map { ContentItem.fromAppResponse(it) }
                results.addAll(appItems)
            },
            onFailure = { e ->
                android.util.Log.e("SearchViewModel", "搜索应用失败: ${e.message}")
            }
        )
    }
    
    /**
     * 搜索游戏
     */
    private suspend fun searchGames(query: String, results: MutableList<ContentItem>) {
        repository.getGames(keyword = query, size = 20).fold(
            onSuccess = { pageResult ->
                val gameItems = pageResult.list.map { ContentItem.fromGameResponse(it) }
                results.addAll(gameItems)
            },
            onFailure = { e ->
                android.util.Log.e("SearchViewModel", "搜索游戏失败: ${e.message}")
            }
        )
    }
    
    /**
     * 搜索商品
     */
    private suspend fun searchProducts(query: String, results: MutableList<ContentItem>) {
        repository.getProducts(keyword = query, size = 20).fold(
            onSuccess = { pageResult ->
                val productItems = pageResult.list.map { ContentItem.fromProductResponse(it) }
                results.addAll(productItems)
            },
            onFailure = { e ->
                android.util.Log.e("SearchViewModel", "搜索商品失败: ${e.message}")
            }
        )
    }
    
    /**
     * 清除搜索结果
     */
    fun clearResults() {
        _searchResults.value = emptyList()
        _error.value = ""
    }
    
    override fun onCleared() {
        super.onCleared()
        searchJob?.cancel()
    }
}
