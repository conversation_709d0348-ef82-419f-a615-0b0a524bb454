package com.example.houduan.mapper;

import com.example.houduan.entity.Movie;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 电影Repository接口
 */
@Repository
public interface MovieMapper extends JpaRepository<Movie, String> {

    Page<Movie> findByTypeAndStatus(String type, Integer status, Pageable pageable);
    Page<Movie> findByStatus(Integer status, Pageable pageable);
    Page<Movie> findByIsRecommendedAndStatusOrderByRatingDesc(Integer isRecommended, Integer status, Pageable pageable);
    Page<Movie> findByIsHotAndStatusOrderByViewCountDesc(Integer isHot, Integer status, Pageable pageable);
    Page<Movie> findByIsNewAndStatusOrderByCreateTimeDesc(Integer isNew, Integer status, Pageable pageable);

    @Query("SELECT m FROM Movie m WHERE m.status = :status AND " +
           "(m.title LIKE %:keyword% OR m.originalTitle LIKE %:keyword% OR " +
           "m.director LIKE %:keyword% OR m.actors LIKE %:keyword%)")
    Page<Movie> findByKeywordAndStatus(@Param("keyword") String keyword, @Param("status") Integer status, Pageable pageable);
}
