<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="搜索用户或内容"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="用户" width="120">
        <template #default="{ row }">
          <div class="user-info">
            <el-avatar :size="40" :src="row.user.avatar" />
            <span class="username">{{ row.user.username }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="收藏内容" min-width="200">
        <template #default="{ row }">
          <div class="content-info">
            <div class="content-details">
              <div class="content-title">{{ row.content.title }}</div>
              <div class="content-type">{{ row.content.type }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="收藏时间" width="180">
        <template #default="{ row }">
          {{ row.createdAt }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = reactive({
  page: 1,
  size: 20,
  keyword: undefined,
  type: undefined
})

const getList = async () => {
  try {
    listLoading.value = true
    // 模拟收藏数据
    const mockData = {
      data: {
        list: [
          {
            id: 1,
            user: {
              id: 1,
              username: 'user001',
              avatar: '/images/avatars/user1.jpg'
            },
            content: {
              id: 1,
              title: '复仇者联盟4',
              type: '电影'
            },
            createdAt: '2025-01-01 10:30:00'
          },
          {
            id: 2,
            user: {
              id: 2,
              username: 'user002',
              avatar: '/images/avatars/user2.jpg'
            },
            content: {
              id: 2,
              title: '微信',
              type: '应用'
            },
            createdAt: '2025-01-02 15:20:00'
          }
        ],
        total: 2
      }
    }
    list.value = mockData.data.list
    total.value = mockData.data.total
  } catch (error) {
    console.error('获取收藏列表失败:', error)
    ElMessage.error('获取收藏列表失败')
  } finally {
    listLoading.value = false
  }
}

const handleFilter = () => {
  listQuery.page = 1
  getList()
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('此操作将删除该收藏记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    ElMessage.success('删除成功!')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}
.username {
  font-weight: 500;
}
.content-info {
  display: flex;
  align-items: center;
  gap: 10px;
}
.content-details {
  flex: 1;
}
.content-title {
  font-weight: 500;
  margin-bottom: 4px;
}
.content-type {
  color: #909399;
  font-size: 12px;
}
</style>