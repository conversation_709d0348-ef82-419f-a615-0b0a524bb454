package com.example.houduan.mapper;

import com.example.houduan.entity.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品数据访问层
 */
@Repository
public interface ProductMapper extends JpaRepository<Product, String> {

    /**
     * 根据分类查询商品列表
     */
    Page<Product> findByCategoryAndStatus(String category, Integer status, Pageable pageable);

    /**
     * 根据状态查询商品列表
     */
    Page<Product> findByStatus(Integer status, Pageable pageable);

    /**
     * 根据名称模糊查询商品
     */
    @Query("SELECT p FROM Product p WHERE p.name LIKE %:keyword% AND p.status = :status")
    Page<Product> findByNameContainingAndStatus(@Param("keyword") String keyword, @Param("status") Integer status, Pageable pageable);

    /**
     * 根据品牌查询商品
     */
    Page<Product> findByBrandAndStatus(String brand, Integer status, Pageable pageable);

    /**
     * 根据价格区间查询商品
     */
    @Query("SELECT p FROM Product p WHERE p.price BETWEEN :minPrice AND :maxPrice AND p.status = :status")
    Page<Product> findByPriceBetweenAndStatus(@Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice, @Param("status") Integer status, Pageable pageable);

    /**
     * 查询推荐商品
     */
    List<Product> findByIsRecommendedAndStatusOrderByCreateTimeDesc(Integer isRecommended, Integer status);

    /**
     * 查询热销商品
     */
    List<Product> findByIsHotAndStatusOrderBySalesDesc(Integer isHot, Integer status);

    /**
     * 根据分类查询商品数量
     */
    long countByCategoryAndStatus(String category, Integer status);

    /**
     * 查询热门商品(按销量排序)
     */
    @Query("SELECT p FROM Product p WHERE p.status = :status ORDER BY p.sales DESC")
    List<Product> findHotProducts(@Param("status") Integer status, Pageable pageable);

    /**
     * 查询最新商品
     */
    List<Product> findByStatusOrderByCreateTimeDesc(Integer status, Pageable pageable);

    /**
     * 根据评分查询商品
     */
    @Query("SELECT p FROM Product p WHERE p.rating >= :minRating AND p.status = :status ORDER BY p.rating DESC")
    List<Product> findByRatingGreaterThanEqualAndStatus(@Param("minRating") Double minRating, @Param("status") Integer status, Pageable pageable);

    /**
     * 查询库存不足的商品
     */
    @Query("SELECT p FROM Product p WHERE p.stock <= :threshold AND p.status = :status")
    List<Product> findLowStockProducts(@Param("threshold") Integer threshold, @Param("status") Integer status);
}
