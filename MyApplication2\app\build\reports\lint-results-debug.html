<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
.options {
   padding-left: 16px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 50 errors and 50 warnings</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">Check performed at Mon Jul 07 15:37:02 CST 2025 by AGP (8.10.1)</nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#MissingPermission"><i class="material-icons error-icon">error</i>Missing Permissions (3)</a>
      <a class="mdl-navigation__link" href="#DefaultLocale"><i class="material-icons warning-icon">warning</i>Implied default locale in case conversion (1)</a>
      <a class="mdl-navigation__link" href="#NotificationPermission"><i class="material-icons error-icon">error</i>Notifications Without Permission (1)</a>
      <a class="mdl-navigation__link" href="#UnusedAttribute"><i class="material-icons warning-icon">warning</i>Attribute unused on older versions (1)</a>
      <a class="mdl-navigation__link" href="#UseRequireInsteadOfGet"><i class="material-icons error-icon">error</i>Use the 'require_____()' API rather than 'get____()' API for more descriptive error messages when it's null. (34)</a>
      <a class="mdl-navigation__link" href="#FragmentLiveDataObserve"><i class="material-icons error-icon">error</i>Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object. (12)</a>
      <a class="mdl-navigation__link" href="#FragmentTagUsage"><i class="material-icons warning-icon">warning</i>Use FragmentContainerView instead of the &lt;fragment> tag (3)</a>
      <a class="mdl-navigation__link" href="#RedundantLabel"><i class="material-icons warning-icon">warning</i>Redundant label on activity (1)</a>
      <a class="mdl-navigation__link" href="#GradleDependency"><i class="material-icons warning-icon">warning</i>Obsolete Gradle Dependency (2)</a>
      <a class="mdl-navigation__link" href="#DiscouragedApi"><i class="material-icons warning-icon">warning</i>Using discouraged APIs (1)</a>
      <a class="mdl-navigation__link" href="#StaticFieldLeak"><i class="material-icons warning-icon">warning</i>Static Field Leaks (2)</a>
      <a class="mdl-navigation__link" href="#MergeRootFrame"><i class="material-icons warning-icon">warning</i>FrameLayout can be replaced with <code>&lt;merge></code> tag (1)</a>
      <a class="mdl-navigation__link" href="#Overdraw"><i class="material-icons warning-icon">warning</i>Overdraw: Painting regions more than once (2)</a>
      <a class="mdl-navigation__link" href="#IconLauncherShape"><i class="material-icons warning-icon">warning</i>The launcher icon shape should use a distinct silhouette (1)</a>
      <a class="mdl-navigation__link" href="#IconLocation"><i class="material-icons warning-icon">warning</i>Image defined in density-independent drawable folder (2)</a>
      <a class="mdl-navigation__link" href="#ButtonStyle"><i class="material-icons warning-icon">warning</i>Button should be borderless (2)</a>
      <a class="mdl-navigation__link" href="#Autofill"><i class="material-icons warning-icon">warning</i>Use Autofill (2)</a>
      <a class="mdl-navigation__link" href="#UseKtx"><i class="material-icons warning-icon">warning</i>Use KTX extension function (5)</a>
      <a class="mdl-navigation__link" href="#UseTomlInstead"><i class="material-icons warning-icon">warning</i>Use TOML Version Catalog Instead (11)</a>
      <a class="mdl-navigation__link" href="#SetTextI18n"><i class="material-icons warning-icon">warning</i>TextView Internationalization (2)</a>
      <a class="mdl-navigation__link" href="#HardcodedText"><i class="material-icons warning-icon">warning</i>Hardcoded text (11)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness">Correctness</a>
</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#MissingPermission">MissingPermission</a>: Missing Permissions</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DefaultLocale">DefaultLocale</a>: Implied default locale in case conversion</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#NotificationPermission">NotificationPermission</a>: Notifications Without Permission</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedAttribute">UnusedAttribute</a>: Attribute unused on older versions</td></tr>
<tr>
<td class="countColumn">34</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#UseRequireInsteadOfGet">UseRequireInsteadOfGet</a>: Use the 'require_____()' API rather than 'get____()' API for more descriptive error messages when it's null.</td></tr>
<tr>
<td class="countColumn">12</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#FragmentLiveDataObserve">FragmentLiveDataObserve</a>: Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object.</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#FragmentTagUsage">FragmentTagUsage</a>: Use FragmentContainerView instead of the &lt;fragment> tag</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RedundantLabel">RedundantLabel</a>: Redundant label on activity</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#GradleDependency">GradleDependency</a>: Obsolete Gradle Dependency</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DiscouragedApi">DiscouragedApi</a>: Using discouraged APIs</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Performance">Performance</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#StaticFieldLeak">StaticFieldLeak</a>: Static Field Leaks</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#MergeRootFrame">MergeRootFrame</a>: FrameLayout can be replaced with <code>&lt;merge></code> tag</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#Overdraw">Overdraw</a>: Overdraw: Painting regions more than once</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability:Icons">Usability:Icons</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#IconLauncherShape">IconLauncherShape</a>: The launcher icon shape should use a distinct silhouette</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#IconLocation">IconLocation</a>: Image defined in density-independent drawable folder</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability">Usability</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ButtonStyle">ButtonStyle</a>: Button should be borderless</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#Autofill">Autofill</a>: Use Autofill</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Productivity">Productivity</a>
</td></tr>
<tr>
<td class="countColumn">5</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UseKtx">UseKtx</a>: Use KTX extension function</td></tr>
<tr>
<td class="countColumn">11</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UseTomlInstead">UseTomlInstead</a>: Use TOML Version Catalog Instead</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization">Internationalization</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SetTextI18n">SetTextI18n</a>: TextView Internationalization</td></tr>
<tr>
<td class="countColumn">11</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#HardcodedText">HardcodedText</a>: Hardcoded text</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#ExtraIssues">Included Additional Checks (19)</a>
</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#MissingIssues">Disabled Checks (39)</a>
</td></tr>
</table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness"></a>
<a name="MissingPermission"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingPermissionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing Permissions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/utils/TestHelper.kt">../../src/main/java/com/example/myapplicationtv/utils/TestHelper.kt</a>:60</span>: <span class="message">Missing permissions required by ConnectivityManager.getActiveNetwork: android.permission.ACCESS_NETWORK_STATE</span><br /><pre class="errorlines">
<span class="lineno">  57 </span>  <span class="keyword">val</span> connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) <span class="keyword">as</span> ConnectivityManager
<span class="lineno">  58 </span>  
<span class="lineno">  59 </span>  <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
<span class="caretline"><span class="lineno">  60 </span>      <span class="keyword">val</span> network = <span class="error">connectivityManager.activeNetwork</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  61 </span>      <span class="keyword">val</span> capabilities = connectivityManager.getNetworkCapabilities(network)
<span class="lineno">  62 </span>      
<span class="lineno">  63 </span>      <span class="keyword">return</span> <span class="keyword">when</span> {
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/utils/TestHelper.kt">../../src/main/java/com/example/myapplicationtv/utils/TestHelper.kt</a>:61</span>: <span class="message">Missing permissions required by ConnectivityManager.getNetworkCapabilities: android.permission.ACCESS_NETWORK_STATE</span><br /><pre class="errorlines">
<span class="lineno">  58 </span>  
<span class="lineno">  59 </span>  <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
<span class="lineno">  60 </span>      <span class="keyword">val</span> network = connectivityManager.activeNetwork
<span class="caretline"><span class="lineno">  61 </span>      <span class="keyword">val</span> capabilities = <span class="error">connectivityManager.getNetworkCapabilities(network)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  62 </span>      
<span class="lineno">  63 </span>      <span class="keyword">return</span> <span class="keyword">when</span> {
<span class="lineno">  64 </span>          capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == <span class="keyword">true</span> -> <span class="string">"WiFi连接"</span></pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/utils/TestHelper.kt">../../src/main/java/com/example/myapplicationtv/utils/TestHelper.kt</a>:71</span>: <span class="message">Missing permissions required by ConnectivityManager.getActiveNetworkInfo: android.permission.ACCESS_NETWORK_STATE</span><br /><pre class="errorlines">
<span class="lineno">  68 </span>            }
<span class="lineno">  69 </span>        } <span class="keyword">else</span> {
<span class="lineno">  70 </span>            <span class="annotation">@Suppress</span>(<span class="string">"DEPRECATION"</span>)
<span class="caretline"><span class="lineno">  71 </span>            <span class="keyword">val</span> networkInfo = <span class="error">connectivityManager.activeNetworkInfo</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  72 </span>            <span class="keyword">return</span> <span class="keyword">if</span> (networkInfo?.isConnected == <span class="keyword">true</span>) {
<span class="lineno">  73 </span>                <span class="string">"网络已连接 (${</span>networkInfo.typeName<span class="string">})"</span>
<span class="lineno">  74 </span>            } <span class="keyword">else</span> {
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationMissingPermission" style="display: none;">
This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.<br/>
<br/>
Furthermore, for permissions that are revocable (with <code>targetSdkVersion</code> 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MissingPermission" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">MissingPermission</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 9/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationMissingPermissionLink" onclick="reveal('explanationMissingPermission');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingPermissionCardLink" onclick="hideid('MissingPermissionCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="DefaultLocale"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DefaultLocaleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Implied default locale in case conversion</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/utils/TestHelper.kt">../../src/main/java/com/example/myapplicationtv/utils/TestHelper.kt</a>:115</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 112 </span>            unitIndex++
<span class="lineno"> 113 </span>        }
<span class="lineno"> 114 </span>        
<span class="caretline"><span class="lineno"> 115 </span>        <span class="keyword">return</span> <span class="warning">String.format(<span class="string">"%.2f %s"</span>, size, units[unitIndex])</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 116 </span>    }
<span class="lineno"> 117 </span>    
<span class="lineno"> 118 </span>    <span class="javadoc">/**
</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationDefaultLocale" style="display: none;">
Calling <code>String#toLowerCase()</code> or <code>#toUpperCase()</code> <b>without specifying an explicit locale</b> is a common source of bugs. The reason for that is that those methods will use the current locale on the user's device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for <code>i</code> is <b>not</b> <code>I</code>.<br/>
<br/>
If you want the methods to just perform ASCII replacement, for example to convert an enum name, call <code>String#toUpperCase(Locale.ROOT)</code> instead. If you really want to use the current locale, call <code>String#toUpperCase(Locale.getDefault())</code> instead.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/util/Locale.html#default_locale">https://developer.android.com/reference/java/util/Locale.html#default_locale</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "DefaultLocale" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DefaultLocale</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDefaultLocaleLink" onclick="reveal('explanationDefaultLocale');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DefaultLocaleCardLink" onclick="hideid('DefaultLocaleCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="NotificationPermission"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="NotificationPermissionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Notifications Without Permission</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a></span>: <span class="message">When targeting Android 13 or higher, posting a permission requires holding the <code>POST_NOTIFICATIONS</code> permission (usage from com.bumptech.glide.request.target.NotificationTarget)</span><br />
</div>
<div class="metadata"><div class="explanation" id="explanationNotificationPermission" style="display: none;">
When targeting Android 13 and higher, posting permissions requires holding the runtime permission <code>android.permission.POST_NOTIFICATIONS</code>.<br/>To suppress this error, use the issue id "NotificationPermission" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">NotificationPermission</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationNotificationPermissionLink" onclick="reveal('explanationNotificationPermission');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="NotificationPermissionCardLink" onclick="hideid('NotificationPermissionCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnusedAttribute"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedAttributeCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Attribute unused on older versions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:20</span>: <span class="message">Attribute <code>usesCleartextTraffic</code> is only used in API level 23 and higher (current min is 21)</span><br /><pre class="errorlines">
<span class="lineno"> 17 </span>        <span class="prefix">android:</span><span class="attribute">label</span>=<span class="value">"@string/app_name"</span>
<span class="lineno"> 18 </span>        <span class="prefix">android:</span><span class="attribute">supportsRtl</span>=<span class="value">"true"</span>
<span class="lineno"> 19 </span>        <span class="prefix">android:</span><span class="attribute">theme</span>=<span class="value">"@style/Theme.MyApplicationTV"</span>
<span class="caretline"><span class="lineno"> 20 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">usesCleartextTraffic</span>=<span class="value">"true"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 21 </span>        <span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">".TVApplication"</span>>
<span class="lineno"> 22 </span>        <span class="tag">&lt;activity</span><span class="attribute">
</span><span class="lineno"> 23 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">".MainActivity"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUnusedAttribute" style="display: none;">
This check finds attributes set in XML files that were introduced in a version newer than the oldest version targeted by your application (with the <code>minSdkVersion</code> attribute).<br/>
<br/>
This is not an error; the application will simply ignore the attribute. However, if the attribute is important to the appearance or functionality of your application, you should consider finding an alternative way to achieve the same result with only available attributes, and then you can optionally create a copy of the layout in a layout-vNN folder which will be used on API NN or higher where you can take advantage of the newer attribute.<br/>
<br/>
Note: This check does not only apply to attributes. For example, some tags can be unused too, such as the new <code>&lt;tag></code> element in layouts introduced in API 21.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedAttribute" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedAttribute</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedAttributeLink" onclick="reveal('explanationUnusedAttribute');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedAttributeCardLink" onclick="hideid('UnusedAttributeCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UseRequireInsteadOfGet"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseRequireInsteadOfGetCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use the 'require_____()' API rather than 'get____()' API for more descriptive error messages when it's null.</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/ErrorFragment.kt">../../src/main/java/com/example/myapplicationtv/ErrorFragment.kt</a>:21</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 18 </span>
<span class="lineno"> 19 </span>   internal <span class="keyword">fun</span> setErrorContent() {
<span class="lineno"> 20 </span>       imageDrawable =
<span class="caretline"><span class="lineno"> 21 </span>           ContextCompat.getDrawable(<span class="error">activity!!</span>, androidx.leanback.R.drawable.lb_ic_sad_cloud)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 22 </span>       message = resources.getString(R.string.error_fragment_message)
<span class="lineno"> 23 </span>       setDefaultBackground(TRANSLUCENT)
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/ErrorFragment.kt">../../src/main/java/com/example/myapplicationtv/ErrorFragment.kt</a>:21</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 18 </span>
<span class="lineno"> 19 </span>   internal <span class="keyword">fun</span> setErrorContent() {
<span class="lineno"> 20 </span>       imageDrawable =
<span class="caretline"><span class="lineno"> 21 </span>           ContextCompat.getDrawable(<span class="error">activity!!</span>, androidx.leanback.R.drawable.lb_ic_sad_cloud)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 22 </span>       message = resources.getString(R.string.error_fragment_message)
<span class="lineno"> 23 </span>       setDefaultBackground(TRANSLUCENT)
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/ErrorFragment.kt">../../src/main/java/com/example/myapplicationtv/ErrorFragment.kt</a>:27</span>: <span class="message">Use requireFragmentManager() instead of fragmentManager!!</span><br /><pre class="errorlines">
<span class="lineno"> 24 </span>
<span class="lineno"> 25 </span>        buttonText = resources.getString(R.string.dismiss_error)
<span class="lineno"> 26 </span>        buttonClickListener = View.OnClickListener {
<span class="caretline"><span class="lineno"> 27 </span>            <span class="error">fragmentManager!!</span>.beginTransaction().remove(<span class="keyword">this</span><span class="annotation">@ErrorFragment</span>).commit()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 28 </span>        }
<span class="lineno"> 29 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/ErrorFragment.kt">../../src/main/java/com/example/myapplicationtv/ErrorFragment.kt</a>:27</span>: <span class="message">Use requireFragmentManager() instead of fragmentManager!!</span><br /><pre class="errorlines">
<span class="lineno"> 24 </span>
<span class="lineno"> 25 </span>        buttonText = resources.getString(R.string.dismiss_error)
<span class="lineno"> 26 </span>        buttonClickListener = View.OnClickListener {
<span class="caretline"><span class="lineno"> 27 </span>            <span class="error">fragmentManager!!</span>.beginTransaction().remove(<span class="keyword">this</span><span class="annotation">@ErrorFragment</span>).commit()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 28 </span>        }
<span class="lineno"> 29 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:76</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno">  73 </span>  private <span class="keyword">fun</span> prepareBackgroundManager() {
<span class="lineno">  74 </span>
<span class="lineno">  75 </span>      mBackgroundManager = BackgroundManager.getInstance(activity)
<span class="caretline"><span class="lineno">  76 </span>      mBackgroundManager.attach(<span class="error">activity!!</span>.window)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  77 </span>      mDefaultBackground = ContextCompat.getDrawable(activity!!, R.drawable.default_background)
<span class="lineno">  78 </span>      mMetrics = DisplayMetrics()
<span class="lineno">  79 </span>      activity!!.windowManager.defaultDisplay.getMetrics(mMetrics)
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="UseRequireInsteadOfGetDivLink" onclick="reveal('UseRequireInsteadOfGetDiv');" />+ 29 More Occurrences...</button>
<div id="UseRequireInsteadOfGetDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:77</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno">  74 </span>
<span class="lineno">  75 </span>      mBackgroundManager = BackgroundManager.getInstance(activity)
<span class="lineno">  76 </span>      mBackgroundManager.attach(activity!!.window)
<span class="caretline"><span class="lineno">  77 </span>      mDefaultBackground = ContextCompat.getDrawable(<span class="error">activity!!</span>, R.drawable.default_background)&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  78 </span>      mMetrics = DisplayMetrics()
<span class="lineno">  79 </span>      activity!!.windowManager.defaultDisplay.getMetrics(mMetrics)
<span class="lineno">  80 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:79</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno">  76 </span>      mBackgroundManager.attach(activity!!.window)
<span class="lineno">  77 </span>      mDefaultBackground = ContextCompat.getDrawable(activity!!, R.drawable.default_background)
<span class="lineno">  78 </span>      mMetrics = DisplayMetrics()
<span class="caretline"><span class="lineno">  79 </span>      <span class="error">activity!!</span>.windowManager.defaultDisplay.getMetrics(mMetrics)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  80 </span>  }
<span class="lineno">  81 </span>
<span class="lineno">  82 </span>  private <span class="keyword">fun</span> setupUIElements() {
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:89</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno">  86 </span>        isHeadersTransitionOnBackEnabled = <span class="keyword">true</span>
<span class="lineno">  87 </span>
<span class="lineno">  88 </span>        <span class="comment">// set fastLane (or headers) background color</span>
<span class="caretline"><span class="lineno">  89 </span>        brandColor = ContextCompat.getColor(<span class="error">activity!!</span>, R.color.fastlane_background)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  90 </span>        <span class="comment">// set search icon color</span>
<span class="lineno">  91 </span>        searchAffordanceColor = ContextCompat.getColor(activity!!, R.color.search_opaque)
<span class="lineno">  92 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:89</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno">  86 </span>        isHeadersTransitionOnBackEnabled = <span class="keyword">true</span>
<span class="lineno">  87 </span>
<span class="lineno">  88 </span>        <span class="comment">// set fastLane (or headers) background color</span>
<span class="caretline"><span class="lineno">  89 </span>        brandColor = ContextCompat.getColor(<span class="error">activity!!</span>, R.color.fastlane_background)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  90 </span>        <span class="comment">// set search icon color</span>
<span class="lineno">  91 </span>        searchAffordanceColor = ContextCompat.getColor(activity!!, R.color.search_opaque)
<span class="lineno">  92 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:91</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno">  88 </span>        <span class="comment">// set fastLane (or headers) background color</span>
<span class="lineno">  89 </span>        brandColor = ContextCompat.getColor(activity!!, R.color.fastlane_background)
<span class="lineno">  90 </span>        <span class="comment">// set search icon color</span>
<span class="caretline"><span class="lineno">  91 </span>        searchAffordanceColor = ContextCompat.getColor(<span class="error">activity!!</span>, R.color.search_opaque)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  92 </span>    }
<span class="lineno">  93 </span>
<span class="lineno">  94 </span>    private <span class="keyword">fun</span> initViewModel() {
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:91</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno">  88 </span>        <span class="comment">// set fastLane (or headers) background color</span>
<span class="lineno">  89 </span>        brandColor = ContextCompat.getColor(activity!!, R.color.fastlane_background)
<span class="lineno">  90 </span>        <span class="comment">// set search icon color</span>
<span class="caretline"><span class="lineno">  91 </span>        searchAffordanceColor = ContextCompat.getColor(<span class="error">activity!!</span>, R.color.search_opaque)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  92 </span>    }
<span class="lineno">  93 </span>
<span class="lineno">  94 </span>    private <span class="keyword">fun</span> initViewModel() {
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:221</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 218 </span>
<span class="lineno"> 219 </span>    private <span class="keyword">fun</span> setupEventListeners() {
<span class="lineno"> 220 </span>        setOnSearchClickedListener {
<span class="caretline"><span class="lineno"> 221 </span>            <span class="keyword">val</span> intent = Intent(<span class="error">activity!!</span>, SearchActivity::<span class="keyword">class</span>.java)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 222 </span>            startActivity(intent)
<span class="lineno"> 223 </span>        }
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:310</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 307 </span>    private <span class="keyword">fun</span> updateBackground(uri: String?) {
<span class="lineno"> 308 </span>        <span class="keyword">val</span> width = mMetrics.widthPixels
<span class="lineno"> 309 </span>        <span class="keyword">val</span> height = mMetrics.heightPixels
<span class="caretline"><span class="lineno"> 310 </span>        Glide.with(<span class="error">activity!!</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 311 </span>            .load(uri)
<span class="lineno"> 312 </span>            .centerCrop()
<span class="lineno"> 313 </span>            .error(mDefaultBackground)
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:57</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno">  54 </span>  mDetailsBackground = DetailsSupportFragmentBackgroundController(<span class="keyword">this</span>)
<span class="lineno">  55 </span>
<span class="lineno">  56 </span>  <span class="comment">// 尝试获取ContentItem</span>
<span class="caretline"><span class="lineno">  57 </span>  mSelectedContentItem = <span class="error">activity!!</span>.intent.getSerializableExtra(DetailsActivity.CONTENT_ITEM) <span class="keyword">as</span>? ContentItem</span>
<span class="lineno">  58 </span>
<span class="lineno">  59 </span>  <span class="comment">// 如果没有ContentItem，尝试获取Movie（向后兼容）</span>
<span class="lineno">  60 </span>  <span class="keyword">if</span> (mSelectedContentItem == <span class="keyword">null</span>) {
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:61</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno">  58 </span>
<span class="lineno">  59 </span>  <span class="comment">// 如果没有ContentItem，尝试获取Movie（向后兼容）</span>
<span class="lineno">  60 </span>  <span class="keyword">if</span> (mSelectedContentItem == <span class="keyword">null</span>) {
<span class="caretline"><span class="lineno">  61 </span>      mSelectedMovie = <span class="error">activity!!</span>.intent.getSerializableExtra(DetailsActivity.MOVIE) <span class="keyword">as</span>? Movie</span>
<span class="lineno">  62 </span>  }
<span class="lineno">  63 </span>
<span class="lineno">  64 </span>  <span class="keyword">if</span> (mSelectedContentItem != <span class="keyword">null</span> || mSelectedMovie != <span class="keyword">null</span>) {
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:74</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno">  71 </span>            initializeBackground()
<span class="lineno">  72 </span>            onItemViewClickedListener = ItemViewClickedListener()
<span class="lineno">  73 </span>        } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno">  74 </span>            <span class="keyword">val</span> intent = Intent(<span class="error">activity!!</span>, MainActivity::<span class="keyword">class</span>.java)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  75 </span>            startActivity(intent)
<span class="lineno">  76 </span>        }
<span class="lineno">  77 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:83</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno">  80 </span>  mDetailsBackground.enableParallax()
<span class="lineno">  81 </span>  <span class="keyword">val</span> backgroundUrl = mSelectedContentItem?.backgroundImageUrl ?: mSelectedMovie?.backgroundImageUrl
<span class="lineno">  82 </span>
<span class="caretline"><span class="lineno">  83 </span>  Glide.with(<span class="error">activity!!</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  84 </span>      .asBitmap()
<span class="lineno">  85 </span>      .centerCrop()
<span class="lineno">  86 </span>      .error(R.drawable.default_background)
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:104</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 101 </span>  Log.d(TAG, <span class="string">"doInBackground: "</span> + item?.toString())
<span class="lineno"> 102 </span>
<span class="lineno"> 103 </span>  <span class="keyword">val</span> row = DetailsOverviewRow(item)
<span class="caretline"><span class="lineno"> 104 </span>  row.imageDrawable = ContextCompat.getDrawable(<span class="error">activity!!</span>, R.drawable.default_background)&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 105 </span>  <span class="keyword">val</span> width = convertDpToPixel(activity!!, DETAIL_THUMB_WIDTH)
<span class="lineno"> 106 </span>  <span class="keyword">val</span> height = convertDpToPixel(activity!!, DETAIL_THUMB_HEIGHT)
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:104</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 101 </span>  Log.d(TAG, <span class="string">"doInBackground: "</span> + item?.toString())
<span class="lineno"> 102 </span>
<span class="lineno"> 103 </span>  <span class="keyword">val</span> row = DetailsOverviewRow(item)
<span class="caretline"><span class="lineno"> 104 </span>  row.imageDrawable = ContextCompat.getDrawable(<span class="error">activity!!</span>, R.drawable.default_background)&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 105 </span>  <span class="keyword">val</span> width = convertDpToPixel(activity!!, DETAIL_THUMB_WIDTH)
<span class="lineno"> 106 </span>  <span class="keyword">val</span> height = convertDpToPixel(activity!!, DETAIL_THUMB_HEIGHT)
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:105</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 102 </span>
<span class="lineno"> 103 </span>  <span class="keyword">val</span> row = DetailsOverviewRow(item)
<span class="lineno"> 104 </span>  row.imageDrawable = ContextCompat.getDrawable(activity!!, R.drawable.default_background)
<span class="caretline"><span class="lineno"> 105 </span>  <span class="keyword">val</span> width = convertDpToPixel(<span class="error">activity!!</span>, DETAIL_THUMB_WIDTH)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 106 </span>  <span class="keyword">val</span> height = convertDpToPixel(activity!!, DETAIL_THUMB_HEIGHT)
<span class="lineno"> 107 </span>
<span class="lineno"> 108 </span>  <span class="keyword">val</span> imageUrl = mSelectedContentItem?.cardImageUrl ?: mSelectedMovie?.cardImageUrl
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:106</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 103 </span>  <span class="keyword">val</span> row = DetailsOverviewRow(item)
<span class="lineno"> 104 </span>  row.imageDrawable = ContextCompat.getDrawable(activity!!, R.drawable.default_background)
<span class="lineno"> 105 </span>  <span class="keyword">val</span> width = convertDpToPixel(activity!!, DETAIL_THUMB_WIDTH)
<span class="caretline"><span class="lineno"> 106 </span>  <span class="keyword">val</span> height = convertDpToPixel(<span class="error">activity!!</span>, DETAIL_THUMB_HEIGHT)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 107 </span>
<span class="lineno"> 108 </span>  <span class="keyword">val</span> imageUrl = mSelectedContentItem?.cardImageUrl ?: mSelectedMovie?.cardImageUrl
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:110</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 107 </span>
<span class="lineno"> 108 </span>        <span class="keyword">val</span> imageUrl = mSelectedContentItem?.cardImageUrl ?: mSelectedMovie?.cardImageUrl
<span class="lineno"> 109 </span>
<span class="caretline"><span class="lineno"> 110 </span>        Glide.with(<span class="error">activity!!</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 111 </span>            .load(imageUrl)
<span class="lineno"> 112 </span>            .centerCrop()
<span class="lineno"> 113 </span>            .error(R.drawable.default_background)
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:234</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 231 </span>  <span class="comment">// Set detail background.</span>
<span class="lineno"> 232 </span>  <span class="keyword">val</span> detailsPresenter = FullWidthDetailsOverviewRowPresenter(DetailsDescriptionPresenter())
<span class="lineno"> 233 </span>  detailsPresenter.backgroundColor =
<span class="caretline"><span class="lineno"> 234 </span>      ContextCompat.getColor(<span class="error">activity!!</span>, R.color.selected_background)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 235 </span>
<span class="lineno"> 236 </span>  <span class="comment">// Hook up transition element.</span>
<span class="lineno"> 237 </span>  <span class="keyword">val</span> sharedElementHelper = FullWidthDetailsOverviewSharedElementHelper()
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:234</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 231 </span>  <span class="comment">// Set detail background.</span>
<span class="lineno"> 232 </span>  <span class="keyword">val</span> detailsPresenter = FullWidthDetailsOverviewRowPresenter(DetailsDescriptionPresenter())
<span class="lineno"> 233 </span>  detailsPresenter.backgroundColor =
<span class="caretline"><span class="lineno"> 234 </span>      ContextCompat.getColor(<span class="error">activity!!</span>, R.color.selected_background)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 235 </span>
<span class="lineno"> 236 </span>  <span class="comment">// Hook up transition element.</span>
<span class="lineno"> 237 </span>  <span class="keyword">val</span> sharedElementHelper = FullWidthDetailsOverviewSharedElementHelper()
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:247</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 244 </span>        detailsPresenter.onActionClickedListener = OnActionClickedListener { action ->
<span class="lineno"> 245 </span>            <span class="keyword">when</span> (action.id) {
<span class="lineno"> 246 </span>                ACTION_WATCH_TRAILER -> {
<span class="caretline"><span class="lineno"> 247 </span>                    <span class="keyword">val</span> intent = Intent(<span class="error">activity!!</span>, PlaybackActivity::<span class="keyword">class</span>.java)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 248 </span>                    intent.putExtra(DetailsActivity.MOVIE, mSelectedMovie)
<span class="lineno"> 249 </span>                    startActivity(intent)
<span class="lineno"> 250 </span>                }
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:247</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 244 </span>        detailsPresenter.onActionClickedListener = OnActionClickedListener { action ->
<span class="lineno"> 245 </span>            <span class="keyword">when</span> (action.id) {
<span class="lineno"> 246 </span>                ACTION_WATCH_TRAILER -> {
<span class="caretline"><span class="lineno"> 247 </span>                    <span class="keyword">val</span> intent = Intent(<span class="error">activity!!</span>, PlaybackActivity::<span class="keyword">class</span>.java)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 248 </span>                    intent.putExtra(DetailsActivity.MOVIE, mSelectedMovie)
<span class="lineno"> 249 </span>                    startActivity(intent)
<span class="lineno"> 250 </span>                }
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:252</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 249 </span>      startActivity(intent)
<span class="lineno"> 250 </span>  }
<span class="lineno"> 251 </span>  ACTION_PLAY -> {
<span class="caretline"><span class="lineno"> 252 </span>      Toast.makeText(<span class="error">activity!!</span>, <span class="string">"启动游戏: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()</span>
<span class="lineno"> 253 </span>  }
<span class="lineno"> 254 </span>  ACTION_DOWNLOAD -> {
<span class="lineno"> 255 </span>      Toast.makeText(activity!!, <span class="string">"开始下载: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:252</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 249 </span>      startActivity(intent)
<span class="lineno"> 250 </span>  }
<span class="lineno"> 251 </span>  ACTION_PLAY -> {
<span class="caretline"><span class="lineno"> 252 </span>      Toast.makeText(<span class="error">activity!!</span>, <span class="string">"启动游戏: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()</span>
<span class="lineno"> 253 </span>  }
<span class="lineno"> 254 </span>  ACTION_DOWNLOAD -> {
<span class="lineno"> 255 </span>      Toast.makeText(activity!!, <span class="string">"开始下载: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:255</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 252 </span>      Toast.makeText(activity!!, <span class="string">"启动游戏: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 253 </span>  }
<span class="lineno"> 254 </span>  ACTION_DOWNLOAD -> {
<span class="caretline"><span class="lineno"> 255 </span>      Toast.makeText(<span class="error">activity!!</span>, <span class="string">"开始下载: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()</span>
<span class="lineno"> 256 </span>  }
<span class="lineno"> 257 </span>  ACTION_FAVORITE -> {
<span class="lineno"> 258 </span>      Toast.makeText(activity!!, <span class="string">"已添加到收藏: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:255</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 252 </span>      Toast.makeText(activity!!, <span class="string">"启动游戏: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 253 </span>  }
<span class="lineno"> 254 </span>  ACTION_DOWNLOAD -> {
<span class="caretline"><span class="lineno"> 255 </span>      Toast.makeText(<span class="error">activity!!</span>, <span class="string">"开始下载: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()</span>
<span class="lineno"> 256 </span>  }
<span class="lineno"> 257 </span>  ACTION_FAVORITE -> {
<span class="lineno"> 258 </span>      Toast.makeText(activity!!, <span class="string">"已添加到收藏: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:258</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 255 </span>      Toast.makeText(activity!!, <span class="string">"开始下载: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 256 </span>  }
<span class="lineno"> 257 </span>  ACTION_FAVORITE -> {
<span class="caretline"><span class="lineno"> 258 </span>      Toast.makeText(<span class="error">activity!!</span>, <span class="string">"已添加到收藏: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()</span>
<span class="lineno"> 259 </span>  }
<span class="lineno"> 260 </span>  <span class="keyword">else</span> -> {
<span class="lineno"> 261 </span>      Toast.makeText(activity!!, action.toString(), Toast.LENGTH_SHORT).show()
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:258</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 255 </span>      Toast.makeText(activity!!, <span class="string">"开始下载: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 256 </span>  }
<span class="lineno"> 257 </span>  ACTION_FAVORITE -> {
<span class="caretline"><span class="lineno"> 258 </span>      Toast.makeText(<span class="error">activity!!</span>, <span class="string">"已添加到收藏: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()</span>
<span class="lineno"> 259 </span>  }
<span class="lineno"> 260 </span>  <span class="keyword">else</span> -> {
<span class="lineno"> 261 </span>      Toast.makeText(activity!!, action.toString(), Toast.LENGTH_SHORT).show()
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:261</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 258 </span>              Toast.makeText(activity!!, <span class="string">"已添加到收藏: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 259 </span>          }
<span class="lineno"> 260 </span>          <span class="keyword">else</span> -> {
<span class="caretline"><span class="lineno"> 261 </span>              Toast.makeText(<span class="error">activity!!</span>, action.toString(), Toast.LENGTH_SHORT).show()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 262 </span>          }
<span class="lineno"> 263 </span>      }
<span class="lineno"> 264 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt">../../src/main/java/com/example/myapplicationtv/VideoDetailsFragment.kt</a>:261</span>: <span class="message">Use requireActivity() instead of activity!!</span><br /><pre class="errorlines">
<span class="lineno"> 258 </span>              Toast.makeText(activity!!, <span class="string">"已添加到收藏: ${</span>mSelectedContentItem?.title<span class="string">}"</span>, Toast.LENGTH_SHORT).show()
<span class="lineno"> 259 </span>          }
<span class="lineno"> 260 </span>          <span class="keyword">else</span> -> {
<span class="caretline"><span class="lineno"> 261 </span>              Toast.makeText(<span class="error">activity!!</span>, action.toString(), Toast.LENGTH_SHORT).show()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 262 </span>          }
<span class="lineno"> 263 </span>      }
<span class="lineno"> 264 </span>  }
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationUseRequireInsteadOfGet" style="display: none;">
AndroidX added new "require____()" versions of common "get___()" APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message.<br/>To suppress this error, use the issue id "UseRequireInsteadOfGet" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseRequireInsteadOfGet</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseRequireInsteadOfGetLink" onclick="reveal('explanationUseRequireInsteadOfGet');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseRequireInsteadOfGetCardLink" onclick="hideid('UseRequireInsteadOfGetCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="FragmentLiveDataObserve"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="FragmentLiveDataObserveCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use getViewLifecycleOwner() as the LifecycleOwner instead of a Fragment instance when observing a LiveData object.</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:109</span>: <span class="message">Use viewLifecycleOwner as the LifecycleOwner.</span><br /><pre class="errorlines">
<span class="lineno"> 106 </span>        <span class="keyword">val</span> cardPresenter = CardPresenter()
<span class="lineno"> 107 </span>
<span class="lineno"> 108 </span>        <span class="comment">// 观察推荐电影</span>
<span class="caretline"><span class="lineno"> 109 </span>        viewModel.recommendedMovies.observe(<span class="error"><span class="keyword">this</span></span>, Observer { movies ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 110 </span>            <span class="keyword">if</span> (movies.isNotEmpty()) {
<span class="lineno"> 111 </span>                <span class="keyword">val</span> listRowAdapter = ArrayObjectAdapter(cardPresenter)
<span class="lineno"> 112 </span>                movies.forEach { movie ->
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:121</span>: <span class="message">Use viewLifecycleOwner as the LifecycleOwner.</span><br /><pre class="errorlines">
<span class="lineno"> 118 </span>        })
<span class="lineno"> 119 </span>
<span class="lineno"> 120 </span>        <span class="comment">// 观察热门电影</span>
<span class="caretline"><span class="lineno"> 121 </span>        viewModel.hotMovies.observe(<span class="error"><span class="keyword">this</span></span>, Observer { movies ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 122 </span>            <span class="keyword">if</span> (movies.isNotEmpty()) {
<span class="lineno"> 123 </span>                <span class="keyword">val</span> listRowAdapter = ArrayObjectAdapter(cardPresenter)
<span class="lineno"> 124 </span>                movies.forEach { movie ->
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:133</span>: <span class="message">Use viewLifecycleOwner as the LifecycleOwner.</span><br /><pre class="errorlines">
<span class="lineno"> 130 </span>        })
<span class="lineno"> 131 </span>
<span class="lineno"> 132 </span>        <span class="comment">// 观察推荐应用</span>
<span class="caretline"><span class="lineno"> 133 </span>        viewModel.recommendedApps.observe(<span class="error"><span class="keyword">this</span></span>, Observer { apps ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 134 </span>            <span class="keyword">if</span> (apps.isNotEmpty()) {
<span class="lineno"> 135 </span>                <span class="keyword">val</span> listRowAdapter = ArrayObjectAdapter(cardPresenter)
<span class="lineno"> 136 </span>                apps.forEach { app ->
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:145</span>: <span class="message">Use viewLifecycleOwner as the LifecycleOwner.</span><br /><pre class="errorlines">
<span class="lineno"> 142 </span>        })
<span class="lineno"> 143 </span>
<span class="lineno"> 144 </span>        <span class="comment">// 观察推荐游戏</span>
<span class="caretline"><span class="lineno"> 145 </span>        viewModel.recommendedGames.observe(<span class="error"><span class="keyword">this</span></span>, Observer { games ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 146 </span>            <span class="keyword">if</span> (games.isNotEmpty()) {
<span class="lineno"> 147 </span>                <span class="keyword">val</span> listRowAdapter = ArrayObjectAdapter(cardPresenter)
<span class="lineno"> 148 </span>                games.forEach { game ->
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:157</span>: <span class="message">Use viewLifecycleOwner as the LifecycleOwner.</span><br /><pre class="errorlines">
<span class="lineno"> 154 </span>        })
<span class="lineno"> 155 </span>
<span class="lineno"> 156 </span>        <span class="comment">// 观察精选游戏</span>
<span class="caretline"><span class="lineno"> 157 </span>        viewModel.featuredGames.observe(<span class="error"><span class="keyword">this</span></span>, Observer { games ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 158 </span>            <span class="keyword">if</span> (games.isNotEmpty()) {
<span class="lineno"> 159 </span>                <span class="keyword">val</span> listRowAdapter = ArrayObjectAdapter(cardPresenter)
<span class="lineno"> 160 </span>                games.forEach { game ->
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="FragmentLiveDataObserveDivLink" onclick="reveal('FragmentLiveDataObserveDiv');" />+ 7 More Occurrences...</button>
<div id="FragmentLiveDataObserveDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:169</span>: <span class="message">Use viewLifecycleOwner as the LifecycleOwner.</span><br /><pre class="errorlines">
<span class="lineno"> 166 </span>        })
<span class="lineno"> 167 </span>
<span class="lineno"> 168 </span>        <span class="comment">// 观察推荐商品</span>
<span class="caretline"><span class="lineno"> 169 </span>        viewModel.recommendedProducts.observe(<span class="error"><span class="keyword">this</span></span>, Observer { products ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 170 </span>            <span class="keyword">if</span> (products.isNotEmpty()) {
<span class="lineno"> 171 </span>                <span class="keyword">val</span> listRowAdapter = ArrayObjectAdapter(cardPresenter)
<span class="lineno"> 172 </span>                products.forEach { product ->
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:184</span>: <span class="message">Use viewLifecycleOwner as the LifecycleOwner.</span><br /><pre class="errorlines">
<span class="lineno"> 181 </span>        addPreferencesRow()
<span class="lineno"> 182 </span>
<span class="lineno"> 183 </span>        <span class="comment">// 观察加载状态</span>
<span class="caretline"><span class="lineno"> 184 </span>        viewModel.isLoading.observe(<span class="error"><span class="keyword">this</span></span>, Observer { isLoading ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 185 </span>            <span class="comment">// 可以在这里显示加载指示器</span>
<span class="lineno"> 186 </span>            Log.d(TAG, <span class="string">"Loading: $isLoading"</span>)
<span class="lineno"> 187 </span>        })
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/MainFragment.kt">../../src/main/java/com/example/myapplicationtv/MainFragment.kt</a>:190</span>: <span class="message">Use viewLifecycleOwner as the LifecycleOwner.</span><br /><pre class="errorlines">
<span class="lineno"> 187 </span>        })
<span class="lineno"> 188 </span>
<span class="lineno"> 189 </span>        <span class="comment">// 观察错误信息</span>
<span class="caretline"><span class="lineno"> 190 </span>        viewModel.error.observe(<span class="error"><span class="keyword">this</span></span>, Observer { error ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 191 </span>            <span class="keyword">if</span> (error.isNotEmpty()) {
<span class="lineno"> 192 </span>                Toast.makeText(context, error, Toast.LENGTH_LONG).show()
<span class="lineno"> 193 </span>                Log.e(TAG, <span class="string">"Error: $error"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/UserCenterFragment.kt">../../src/main/java/com/example/myapplicationtv/UserCenterFragment.kt</a>:48</span>: <span class="message">Use viewLifecycleOwner as the LifecycleOwner.</span><br /><pre class="errorlines">
<span class="lineno">  45 </span>        viewModel = ViewModelProvider(<span class="keyword">this</span>)[UserCenterViewModel::<span class="keyword">class</span>.java]
<span class="lineno">  46 </span>        
<span class="lineno">  47 </span>        <span class="comment">// 观察收藏列表</span>
<span class="caretline"><span class="lineno">  48 </span>        viewModel.favorites.observe(<span class="error"><span class="keyword">this</span></span>, Observer { favorites ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  49 </span>            <span class="keyword">if</span> (favorites.isNotEmpty()) {
<span class="lineno">  50 </span>                <span class="keyword">val</span> listRowAdapter = ArrayObjectAdapter(cardPresenter)
<span class="lineno">  51 </span>                favorites.forEach { favorite ->
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/UserCenterFragment.kt">../../src/main/java/com/example/myapplicationtv/UserCenterFragment.kt</a>:60</span>: <span class="message">Use viewLifecycleOwner as the LifecycleOwner.</span><br /><pre class="errorlines">
<span class="lineno">  57 </span>        })
<span class="lineno">  58 </span>        
<span class="lineno">  59 </span>        <span class="comment">// 观察历史记录</span>
<span class="caretline"><span class="lineno">  60 </span>        viewModel.history.observe(<span class="error"><span class="keyword">this</span></span>, Observer { history ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  61 </span>            <span class="keyword">if</span> (history.isNotEmpty()) {
<span class="lineno">  62 </span>                <span class="keyword">val</span> listRowAdapter = ArrayObjectAdapter(cardPresenter)
<span class="lineno">  63 </span>                history.forEach { item ->
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/UserCenterFragment.kt">../../src/main/java/com/example/myapplicationtv/UserCenterFragment.kt</a>:75</span>: <span class="message">Use viewLifecycleOwner as the LifecycleOwner.</span><br /><pre class="errorlines">
<span class="lineno">  72 </span>        addSettingsRow()
<span class="lineno">  73 </span>        
<span class="lineno">  74 </span>        <span class="comment">// 观察加载状态</span>
<span class="caretline"><span class="lineno">  75 </span>        viewModel.isLoading.observe(<span class="error"><span class="keyword">this</span></span>, Observer { isLoading ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  76 </span>            Log.d(TAG, <span class="string">"Loading: $isLoading"</span>)
<span class="lineno">  77 </span>        })
<span class="lineno">  78 </span>        
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/UserCenterFragment.kt">../../src/main/java/com/example/myapplicationtv/UserCenterFragment.kt</a>:80</span>: <span class="message">Use viewLifecycleOwner as the LifecycleOwner.</span><br /><pre class="errorlines">
<span class="lineno">  77 </span>        })
<span class="lineno">  78 </span>        
<span class="lineno">  79 </span>        <span class="comment">// 观察错误信息</span>
<span class="caretline"><span class="lineno">  80 </span>        viewModel.error.observe(<span class="error"><span class="keyword">this</span></span>, Observer { error ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  81 </span>            <span class="keyword">if</span> (error.isNotEmpty()) {
<span class="lineno">  82 </span>                Toast.makeText(context, error, Toast.LENGTH_LONG).show()
<span class="lineno">  83 </span>                Log.e(TAG, <span class="string">"Error: $error"</span>)
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationFragmentLiveDataObserve" style="display: none;">
When observing a LiveData object from a fragment's onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment's view is active.<br/>To suppress this error, use the issue id "FragmentLiveDataObserve" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">FragmentLiveDataObserve</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationFragmentLiveDataObserveLink" onclick="reveal('explanationFragmentLiveDataObserve');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="FragmentLiveDataObserveCardLink" onclick="hideid('FragmentLiveDataObserveCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="FragmentTagUsage"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="FragmentTagUsageCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use FragmentContainerView instead of the &lt;fragment> tag</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:2</span>: <span class="message">Replace the &lt;fragment> tag with FragmentContainerView.</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno"> 2 </span><span class="tag">&lt;</span><span class="warning"><span class="tag">fragment</span></span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span>    <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/login_fragment"</span>
<span class="lineno"> 4 </span>    <span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.example.myapplicationtv.LoginFragment"</span>
<span class="lineno"> 5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_search.xml">../../src/main/res/layout/activity_search.xml</a>:2</span>: <span class="message">Replace the &lt;fragment> tag with FragmentContainerView.</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno"> 2 </span><span class="tag">&lt;</span><span class="warning"><span class="tag">fragment</span></span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span>    <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/search_fragment"</span>
<span class="lineno"> 4 </span>    <span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.example.myapplicationtv.SearchFragment"</span>
<span class="lineno"> 5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_user_center.xml">../../src/main/res/layout/activity_user_center.xml</a>:2</span>: <span class="message">Replace the &lt;fragment> tag with FragmentContainerView.</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno"> 2 </span><span class="tag">&lt;</span><span class="warning"><span class="tag">fragment</span></span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span>    <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/user_center_fragment"</span>
<span class="lineno"> 4 </span>    <span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.example.myapplicationtv.UserCenterFragment"</span>
<span class="lineno"> 5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationFragmentTagUsage" style="display: none;">
FragmentContainerView replaces the &lt;fragment> tag as the preferred                 way of adding fragments via XML. Unlike the &lt;fragment> tag, FragmentContainerView                 uses a normal <code>FragmentTransaction</code> under the hood to add the initial fragment,                 allowing further FragmentTransaction operations on the FragmentContainerView                 and providing a consistent timing for lifecycle events.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html">https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html</a>
</div>To suppress this error, use the issue id "FragmentTagUsage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">FragmentTagUsage</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationFragmentTagUsageLink" onclick="reveal('explanationFragmentTagUsage');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="FragmentTagUsageCardLink" onclick="hideid('FragmentTagUsageCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="RedundantLabel"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RedundantLabelCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Redundant label on activity</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:27</span>: <span class="message">Redundant label can be removed</span><br /><pre class="errorlines">
<span class="lineno"> 24 </span>            <span class="prefix">android:</span><span class="attribute">banner</span>=<span class="value">"@drawable/app_icon_your_company"</span>
<span class="lineno"> 25 </span>            <span class="prefix">android:</span><span class="attribute">exported</span>=<span class="value">"true"</span>
<span class="lineno"> 26 </span>            <span class="prefix">android:</span><span class="attribute">icon</span>=<span class="value">"@drawable/app_icon_your_company"</span>
<span class="caretline"><span class="lineno"> 27 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">label</span>=<span class="value">"@string/app_name"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 28 </span>            <span class="prefix">android:</span><span class="attribute">logo</span>=<span class="value">"@drawable/app_icon_your_company"</span>
<span class="lineno"> 29 </span>            <span class="prefix">android:</span><span class="attribute">screenOrientation</span>=<span class="value">"landscape"</span>>
<span class="lineno"> 30 </span>            <span class="tag">&lt;intent-filter></span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationRedundantLabel" style="display: none;">
When an activity does not have a label attribute, it will use the one from the application tag. Since the application has already specified the same label, the label on this activity can be omitted.<br/>To suppress this error, use the issue id "RedundantLabel" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RedundantLabel</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRedundantLabelLink" onclick="reveal('explanationRedundantLabel');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RedundantLabelCardLink" onclick="hideid('RedundantLabelCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="GradleDependency"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="GradleDependencyCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Gradle Dependency</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:47</span>: <span class="message">A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0</span><br /><pre class="errorlines">
<span class="lineno"> 44 </span>    implementation("com.squareup.retrofit2:retrofit:2.9.0")
<span class="lineno"> 45 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
<span class="lineno"> 46 </span>    implementation("com.squareup.okhttp3:logging-interceptor:4.11.0")
<span class="caretline"><span class="lineno"> 47 </span>    implementation(<span class="warning">"com.google.code.gson:gson:2.10.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 48 </span>
<span class="lineno"> 49 </span>    // 协程
<span class="lineno"> 50 </span>    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:3</span>: <span class="message">A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.1.0</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="lineno">  2 </span>agp = "8.10.1"
<span class="caretline"><span class="lineno">  3 </span>kotlin = <span class="warning">"2.0.21"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>leanback = "1.0.0"
<span class="lineno">  6 </span>glide = "4.11.0"
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationGradleDependency" style="display: none;">
This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleDependency" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">GradleDependency</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationGradleDependencyLink" onclick="reveal('explanationGradleDependency');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="GradleDependencyCardLink" onclick="hideid('GradleDependencyCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="DiscouragedApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DiscouragedApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using discouraged APIs</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:29</span>: <span class="message">Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.</span><br /><pre class="errorlines">
<span class="lineno"> 26 </span>            <span class="prefix">android:</span><span class="attribute">icon</span>=<span class="value">"@drawable/app_icon_your_company"</span>
<span class="lineno"> 27 </span>            <span class="prefix">android:</span><span class="attribute">label</span>=<span class="value">"@string/app_name"</span>
<span class="lineno"> 28 </span>            <span class="prefix">android:</span><span class="attribute">logo</span>=<span class="value">"@drawable/app_icon_your_company"</span>
<span class="caretline"><span class="lineno"> 29 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">screenOrientation</span>=<span class="value">"landscape"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 30 </span>            <span class="tag">&lt;intent-filter></span>
<span class="lineno"> 31 </span>                <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.intent.action.MAIN"</span> />
<span class="lineno"> 32 </span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationDiscouragedApi" style="display: none;">
Discouraged APIs are allowed and are not deprecated, but they may be unfit for common use (e.g. due to slow performance or subtle behavior).<br/>To suppress this error, use the issue id "DiscouragedApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DiscouragedApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 2/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDiscouragedApiLink" onclick="reveal('explanationDiscouragedApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DiscouragedApiCardLink" onclick="hideid('DiscouragedApiCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Performance"></a>
<a name="StaticFieldLeak"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="StaticFieldLeakCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Static Field Leaks</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/network/ApiClient.kt">../../src/main/java/com/example/myapplicationtv/network/ApiClient.kt</a>:13</span>: <span class="message">Do not place Android context classes in static fields (static reference to <code>ApiClient</code> which has field <code>context</code> pointing to <code>Context</code>); this is a memory leak</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>import retrofit2.converter.gson.GsonConverterFactory
<span class="lineno"> 11 </span>import java.util.concurrent.TimeUnit
<span class="lineno"> 12 </span>
<span class="caretline"><span class="lineno"> 13 </span><span class="warning"><span class="javadoc">/**</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="javadoc">
</span><span class="lineno"> 14 </span><span class="javadoc"> * API客户端
</span><span class="lineno"> 15 </span><span class="javadoc"> */</span>
<span class="lineno"> 16 </span><span class="keyword">object</span> ApiClient {
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/network/ApiClient.kt">../../src/main/java/com/example/myapplicationtv/network/ApiClient.kt</a>:22</span>: <span class="message">Do not place Android context classes in static fields; this is a memory leak</span><br /><pre class="errorlines">
<span class="lineno"> 19 </span>    private const <span class="keyword">val</span> TIMEOUT = <span class="number">30L</span>
<span class="lineno"> 20 </span>    
<span class="lineno"> 21 </span>    private <span class="keyword">var</span> retrofit: Retrofit? = <span class="keyword">null</span>
<span class="caretline"><span class="lineno"> 22 </span>    <span class="warning">private <span class="keyword">var</span> context: Context? = <span class="keyword">null</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 23 </span>    
<span class="lineno"> 24 </span>    <span class="keyword">fun</span> init(context: Context) {
<span class="lineno"> 25 </span>        <span class="keyword">this</span>.context = context
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationStaticFieldLeak" style="display: none;">
A static field will leak contexts.<br/>
<br/>
Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a <code>Fragment</code> or <code>Activity</code>, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.<br/>
<br/>
Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.<br/>
<br/>
ViewModel classes should never point to Views or non-application Contexts.<br/>To suppress this error, use the issue id "StaticFieldLeak" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">StaticFieldLeak</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationStaticFieldLeakLink" onclick="reveal('explanationStaticFieldLeak');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="StaticFieldLeakCardLink" onclick="hideid('StaticFieldLeakCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="MergeRootFrame"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MergeRootFrameCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">FrameLayout can be replaced with &lt;merge> tag</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_details.xml">../../src/main/res/layout/activity_details.xml</a>:2</span>: <span class="message">This <code>&lt;FrameLayout></code> can be replaced with a <code>&lt;merge></code> tag</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno"> 2 </span><span class="warning"><span class="tag">&lt;FrameLayout</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno"> 4 </span>    <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/details_fragment"</span>
<span class="lineno"> 5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationMergeRootFrame" style="display: none;">
If a <code>&lt;FrameLayout></code> is the root of a layout and does not provide background or padding etc, it can often be replaced with a <code>&lt;merge></code> tag which is slightly more efficient. Note that this depends on context, so make sure you understand how the <code>&lt;merge></code> tag works before proceeding.<br/><div class="moreinfo">More info: <a href="https://android-developers.googleblog.com/2009/03/android-layout-tricks-3-optimize-by.html">https://android-developers.googleblog.com/2009/03/android-layout-tricks-3-optimize-by.html</a>
</div>To suppress this error, use the issue id "MergeRootFrame" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">MergeRootFrame</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationMergeRootFrameLink" onclick="reveal('explanationMergeRootFrame');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MergeRootFrameCardLink" onclick="hideid('MergeRootFrameCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="Overdraw"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverdrawCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overdraw: Painting regions more than once</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_test.xml">../../src/main/res/layout/activity_test.xml</a>:8</span>: <span class="message">Possible overdraw: Root element paints background <code>@color/default_background</code> with a theme that also paints a background (inferred theme is <code>@style/Theme.MyApplicationTV</code>)</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="lineno">  7 </span>    <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"48dp"</span>
<span class="caretline"><span class="lineno">  8 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/default_background"</span></span>>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 11 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span></pre>

<span class="location"><a href="../../src/main/res/layout/fragment_login.xml">../../src/main/res/layout/fragment_login.xml</a>:8</span>: <span class="message">Possible overdraw: Root element paints background <code>@color/default_background</code> with a theme that also paints a background (inferred theme is <code>@style/Theme.MyApplicationTV</code>)</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="lineno">  7 </span>    <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"48dp"</span>
<span class="caretline"><span class="lineno">  8 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/default_background"</span></span>>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;ImageView</span><span class="attribute">
</span><span class="lineno"> 11 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"120dp"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationOverdraw" style="display: none;">
If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called "overdraw".<br/>
<br/>
NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it's currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.<br/>
<br/>
If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.<br/>
<br/>
Of course it's possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead.<br/>To suppress this error, use the issue id "Overdraw" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Overdraw</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationOverdrawLink" onclick="reveal('explanationOverdraw');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverdrawCardLink" onclick="hideid('OverdrawCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability:Icons"></a>
<a name="IconLauncherShape"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="IconLauncherShapeCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">The launcher icon shape should use a distinct silhouette</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable/app_icon_your_company.png">../../src/main/res/drawable/app_icon_your_company.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/app_icon_your_company.png" /><span class="message">Launcher icons should not fill every pixel of their square region; see the design guide for details</span><br clear="right"/>
</div>
<div class="metadata"><div class="explanation" id="explanationIconLauncherShape" style="display: none;">
According to the Android Design Guide (<a href="https://d.android.com/r/studio-ui/designer/material/iconography">https://d.android.com/r/studio-ui/designer/material/iconography</a>) your launcher icons should "use a distinct silhouette", a "three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth."<br/>
<br/>
The unique silhouette implies that your launcher icon should not be a filled square.<br/>To suppress this error, use the issue id "IconLauncherShape" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">IconLauncherShape</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Icons</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationIconLauncherShapeLink" onclick="reveal('explanationIconLauncherShape');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IconLauncherShapeCardLink" onclick="hideid('IconLauncherShapeCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="IconLocation"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="IconLocationCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Image defined in density-independent drawable folder</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable/app_icon_your_company.png">../../src/main/res/drawable/app_icon_your_company.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/app_icon_your_company.png" /><span class="message">Found bitmap drawable <code>res/drawable/app_icon_your_company.png</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/movie.png">../../src/main/res/drawable/movie.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/movie.png" /><span class="message">Found bitmap drawable <code>res/drawable/movie.png</code> in densityless folder</span><br clear="right"/>
</div>
<div class="metadata"><div class="explanation" id="explanationIconLocation" style="display: none;">
The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to <code>drawable-mdpi</code> and consider providing higher and lower resolution versions in <code>drawable-ldpi</code>, <code>drawable-hdpi</code> and <code>drawable-xhdpi</code>. If the icon <b>really</b> is density independent (for example a solid color) you can place it in <code>drawable-nodpi</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/practices/screens_support.html">https://developer.android.com/guide/practices/screens_support.html</a>
</div>To suppress this error, use the issue id "IconLocation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">IconLocation</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Icons</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationIconLocationLink" onclick="reveal('explanationIconLocation');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IconLocationCardLink" onclick="hideid('IconLocationCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability"></a>
<a name="ButtonStyle"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ButtonStyleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Button should be borderless</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/fragment_login.xml">../../src/main/res/layout/fragment_login.xml</a>:60</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno"> 57 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
<span class="lineno"> 58 </span>        <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>>
<span class="lineno"> 59 </span>
<span class="caretline"><span class="lineno"> 60 </span>        <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 61 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/login_button"</span>
<span class="lineno"> 62 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 63 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"56dp"</span></pre>

<span class="location"><a href="../../src/main/res/layout/fragment_login.xml">../../src/main/res/layout/fragment_login.xml</a>:73</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno"> 70 </span>            <span class="prefix">android:</span><span class="attribute">focusable</span>=<span class="value">"true"</span>
<span class="lineno"> 71 </span>            <span class="prefix">android:</span><span class="attribute">focusableInTouchMode</span>=<span class="value">"true"</span> />
<span class="lineno"> 72 </span>
<span class="caretline"><span class="lineno"> 73 </span>        <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 74 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/register_button"</span>
<span class="lineno"> 75 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 76 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"56dp"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationButtonStyle" style="display: none;">
Button bars typically use a borderless style for the buttons. Set the <code>style="?android:attr/buttonBarButtonStyle"</code> attribute on each of the buttons, and set <code>style="?android:attr/buttonBarStyle"</code> on the parent layout<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/dialogs">https://d.android.com/r/studio-ui/designer/material/dialogs</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ButtonStyle" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ButtonStyle</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationButtonStyleLink" onclick="reveal('explanationButtonStyle');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ButtonStyleCardLink" onclick="hideid('ButtonStyleCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="Autofill"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="AutofillCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use Autofill</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/fragment_login.xml">../../src/main/res/layout/fragment_login.xml</a>:26</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno"> 23 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"48dp"</span>
<span class="lineno"> 24 </span>        <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span> />
<span class="lineno"> 25 </span>
<span class="caretline"><span class="lineno"> 26 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 27 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/username_edit_text"</span>
<span class="lineno"> 28 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"400dp"</span>
<span class="lineno"> 29 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"56dp"</span></pre>

<span class="location"><a href="../../src/main/res/layout/fragment_login.xml">../../src/main/res/layout/fragment_login.xml</a>:40</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno"> 37 </span>        <span class="prefix">android:</span><span class="attribute">focusable</span>=<span class="value">"true"</span>
<span class="lineno"> 38 </span>        <span class="prefix">android:</span><span class="attribute">focusableInTouchMode</span>=<span class="value">"true"</span> />
<span class="lineno"> 39 </span>
<span class="caretline"><span class="lineno"> 40 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 41 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/password_edit_text"</span>
<span class="lineno"> 42 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"400dp"</span>
<span class="lineno"> 43 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"56dp"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationAutofill" style="display: none;">
Specify an <code>autofillHints</code> attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.<br/>
<br/>
The hints can have any value, but it is recommended to use predefined values like 'username' for a username or 'creditCardNumber' for a credit card number. For a list of all predefined autofill hint constants, see the <code>AUTOFILL_HINT_</code> constants in the <code>View</code> reference at <a href="https://developer.android.com/reference/android/view/View.html">https://developer.android.com/reference/android/view/View.html</a>.<br/>
<br/>
You can mark a view unimportant for autofill by specifying an <code>importantForAutofill</code> attribute on that view or a parent view. See <a href="https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)">https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)</a>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/text/autofill.html">https://developer.android.com/guide/topics/text/autofill.html</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "Autofill" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Autofill</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationAutofillLink" onclick="reveal('explanationAutofill');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="AutofillCardLink" onclick="hideid('AutofillCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Productivity"></a>
<a name="UseKtx"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseKtxCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use KTX extension function</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/network/ApiClient.kt">../../src/main/java/com/example/myapplicationtv/network/ApiClient.kt</a>:84</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 81 </span>    <span class="keyword">fun</span> saveAuthToken(token: String) {
<span class="lineno"> 82 </span>        context?.let { ctx ->
<span class="lineno"> 83 </span>            <span class="keyword">val</span> prefs = ctx.getSharedPreferences(<span class="string">"app_prefs"</span>, Context.MODE_PRIVATE)
<span class="caretline"><span class="lineno"> 84 </span>            <span class="warning">prefs.edit()</span>.putString(<span class="string">"auth_token"</span>, token).apply()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 85 </span>        }
<span class="lineno"> 86 </span>    }
<span class="lineno"> 87 </span>    
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/network/ApiClient.kt">../../src/main/java/com/example/myapplicationtv/network/ApiClient.kt</a>:91</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 88 </span>    <span class="keyword">fun</span> clearAuthToken() {
<span class="lineno"> 89 </span>        context?.let { ctx ->
<span class="lineno"> 90 </span>            <span class="keyword">val</span> prefs = ctx.getSharedPreferences(<span class="string">"app_prefs"</span>, Context.MODE_PRIVATE)
<span class="caretline"><span class="lineno"> 91 </span>            <span class="warning">prefs.edit()</span>.remove(<span class="string">"auth_token"</span>).apply()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 92 </span>        }
<span class="lineno"> 93 </span>    }
<span class="lineno"> 94 </span>    
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/PlaybackVideoFragment.kt">../../src/main/java/com/example/myapplicationtv/PlaybackVideoFragment.kt</a>:100</span>: <span class="message">Use the KTX extension function <code>String.toUri</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  97 </span>        mTransportControlGlue.playWhenPrepared()
<span class="lineno">  98 </span>
<span class="lineno">  99 </span>        <span class="keyword">try</span> {
<span class="caretline"><span class="lineno"> 100 </span>            playerAdapter.setDataSource(<span class="warning">Uri.parse(videoUrl)</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 101 </span>
<span class="lineno"> 102 </span>            <span class="comment">// 记录观看历史</span>
<span class="lineno"> 103 </span>            recordWatchHistory()
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/utils/UserManager.kt">../../src/main/java/com/example/myapplicationtv/utils/UserManager.kt</a>:32</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  29 </span><span class="javadoc">     * 保存用户信息
</span><span class="lineno">  30 </span><span class="javadoc">     */</span>
<span class="lineno">  31 </span>    <span class="keyword">fun</span> saveUser(user: UserResponse, token: String) {
<span class="caretline"><span class="lineno">  32 </span>        <span class="keyword">val</span> editor = <span class="warning">sharedPreferences.edit()</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  33 </span>        editor.putString(KEY_USER_INFO, gson.toJson(user))
<span class="lineno">  34 </span>        editor.putString(KEY_TOKEN, token)
<span class="lineno">  35 </span>        editor.putBoolean(KEY_IS_LOGGED_IN, <span class="keyword">true</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/utils/UserManager.kt">../../src/main/java/com/example/myapplicationtv/utils/UserManager.kt</a>:73</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  70 </span><span class="javadoc">     * 退出登录
</span><span class="lineno">  71 </span><span class="javadoc">     */</span>
<span class="lineno">  72 </span>    <span class="keyword">fun</span> logout() {
<span class="caretline"><span class="lineno">  73 </span>        <span class="keyword">val</span> editor = <span class="warning">sharedPreferences.edit()</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  74 </span>        editor.remove(KEY_USER_INFO)
<span class="lineno">  75 </span>        editor.remove(KEY_TOKEN)
<span class="lineno">  76 </span>        editor.putBoolean(KEY_IS_LOGGED_IN, <span class="keyword">false</span>)
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUseKtx" style="display: none;">
The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>remove-defaults</b> (default is true):<br/>
Whether to skip arguments that match the defaults provided by the extension.<br/>
<br/>
Extensions often provide default values for some of the parameters. For example:
<pre>
fun Path.readLines(charset: Charset = Charsets.UTF_8): List&lt;String> { return Files.readAllLines(this, charset) }
</pre>
This lint check will by default automatically omit parameters that match the default, so if your code was calling<br/>

<pre>
Files.readAllLines(file, Charset.UTF_8)
</pre>
lint would replace this with
<pre>
file.readLines()
</pre>
rather than<br/>

<pre>
file.readLines(Charset.UTF_8
</pre>
You can turn this behavior off using this option.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UseKtx"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"remove-defaults"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
<b>require-present</b> (default is true):<br/>
Whether to only offer extensions already available.<br/>
<br/>
This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UseKtx"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"require-present"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div>To suppress this error, use the issue id "UseKtx" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseKtx</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Productivity</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseKtxLink" onclick="reveal('explanationUseKtx');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseKtxCardLink" onclick="hideid('UseKtxCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UseTomlInstead"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseTomlInsteadCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use TOML Version Catalog Instead</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:44</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 41 </span>    implementation(libs.glide)
<span class="lineno"> 42 </span>
<span class="lineno"> 43 </span>    // 网络请求
<span class="caretline"><span class="lineno"> 44 </span>    implementation(<span class="warning">"com.squareup.retrofit2:retrofit:2.9.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 45 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
<span class="lineno"> 46 </span>    implementation("com.squareup.okhttp3:logging-interceptor:4.11.0")
<span class="lineno"> 47 </span>    implementation("com.google.code.gson:gson:2.10.1")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:45</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 42 </span>
<span class="lineno"> 43 </span>    // 网络请求
<span class="lineno"> 44 </span>    implementation("com.squareup.retrofit2:retrofit:2.9.0")
<span class="caretline"><span class="lineno"> 45 </span>    implementation(<span class="warning">"com.squareup.retrofit2:converter-gson:2.9.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 46 </span>    implementation("com.squareup.okhttp3:logging-interceptor:4.11.0")
<span class="lineno"> 47 </span>    implementation("com.google.code.gson:gson:2.10.1")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:46</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 43 </span>    // 网络请求
<span class="lineno"> 44 </span>    implementation("com.squareup.retrofit2:retrofit:2.9.0")
<span class="lineno"> 45 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
<span class="caretline"><span class="lineno"> 46 </span>    implementation(<span class="warning">"com.squareup.okhttp3:logging-interceptor:4.11.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 47 </span>    implementation("com.google.code.gson:gson:2.10.1")
<span class="lineno"> 48 </span>
<span class="lineno"> 49 </span>    // 协程
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:47</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 44 </span>    implementation("com.squareup.retrofit2:retrofit:2.9.0")
<span class="lineno"> 45 </span>    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
<span class="lineno"> 46 </span>    implementation("com.squareup.okhttp3:logging-interceptor:4.11.0")
<span class="caretline"><span class="lineno"> 47 </span>    implementation(<span class="warning">"com.google.code.gson:gson:2.10.1"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 48 </span>
<span class="lineno"> 49 </span>    // 协程
<span class="lineno"> 50 </span>    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:50</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 47 </span>    implementation("com.google.code.gson:gson:2.10.1")
<span class="lineno"> 48 </span>
<span class="lineno"> 49 </span>    // 协程
<span class="caretline"><span class="lineno"> 50 </span>    implementation(<span class="warning">"org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 51 </span>    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
<span class="lineno"> 52 </span>
<span class="lineno"> 53 </span>    // ViewModel和LiveData
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="UseTomlInsteadDivLink" onclick="reveal('UseTomlInsteadDiv');" />+ 6 More Occurrences...</button>
<div id="UseTomlInsteadDiv" style="display: none">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:51</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 48 </span>
<span class="lineno"> 49 </span>    // 协程
<span class="lineno"> 50 </span>    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
<span class="caretline"><span class="lineno"> 51 </span>    implementation(<span class="warning">"org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 52 </span>
<span class="lineno"> 53 </span>    // ViewModel和LiveData
<span class="lineno"> 54 </span>    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:54</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 51 </span>    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
<span class="lineno"> 52 </span>
<span class="lineno"> 53 </span>    // ViewModel和LiveData
<span class="caretline"><span class="lineno"> 54 </span>    implementation(<span class="warning">"androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 55 </span>    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
<span class="lineno"> 56 </span>    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:55</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 52 </span>
<span class="lineno"> 53 </span>    // ViewModel和LiveData
<span class="lineno"> 54 </span>    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0")
<span class="caretline"><span class="lineno"> 55 </span>    implementation(<span class="warning">"androidx.lifecycle:lifecycle-livedata-ktx:2.7.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 56 </span>    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
<span class="lineno"> 57 </span>
<span class="lineno"> 58 </span>    // Fragment
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:56</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 53 </span>    // ViewModel和LiveData
<span class="lineno"> 54 </span>    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0")
<span class="lineno"> 55 </span>    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
<span class="caretline"><span class="lineno"> 56 </span>    implementation(<span class="warning">"androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 57 </span>
<span class="lineno"> 58 </span>    // Fragment
<span class="lineno"> 59 </span>    implementation("androidx.fragment:fragment-ktx:1.6.2")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:59</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 56 </span>    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
<span class="lineno"> 57 </span>
<span class="lineno"> 58 </span>    // Fragment
<span class="caretline"><span class="lineno"> 59 </span>    implementation(<span class="warning">"androidx.fragment:fragment-ktx:1.6.2"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 60 </span>
<span class="lineno"> 61 </span>    // RecyclerView
<span class="lineno"> 62 </span>    implementation("androidx.recyclerview:recyclerview:1.3.2")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:62</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 59 </span>    implementation("androidx.fragment:fragment-ktx:1.6.2")
<span class="lineno"> 60 </span>
<span class="lineno"> 61 </span>    // RecyclerView
<span class="caretline"><span class="lineno"> 62 </span>    implementation(<span class="warning">"androidx.recyclerview:recyclerview:1.3.2"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 63 </span>}</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationUseTomlInstead" style="display: none;">
If your project is using a <code>libs.versions.toml</code> file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically).<br/>To suppress this error, use the issue id "UseTomlInstead" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseTomlInstead</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Productivity</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseTomlInsteadLink" onclick="reveal('explanationUseTomlInstead');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseTomlInsteadCardLink" onclick="hideid('UseTomlInsteadCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization"></a>
<a name="SetTextI18n"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SetTextI18nCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">TextView Internationalization</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/LoginFragment.kt">../../src/main/java/com/example/myapplicationtv/LoginFragment.kt</a>:52</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  49 </span>        progressBar = view.findViewById(R.id.progress_bar)
<span class="lineno">  50 </span>        
<span class="lineno">  51 </span>        <span class="comment">// 设置默认测试账号</span>
<span class="caretline"><span class="lineno">  52 </span>        usernameEditText.setText(<span class="string">"</span><span class="warning"><span class="string">test</span></span><span class="string">"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  53 </span>        passwordEditText.setText(<span class="string">"123456"</span>)
<span class="lineno">  54 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/example/myapplicationtv/LoginFragment.kt">../../src/main/java/com/example/myapplicationtv/LoginFragment.kt</a>:53</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  50 </span>        
<span class="lineno">  51 </span>        <span class="comment">// 设置默认测试账号</span>
<span class="lineno">  52 </span>        usernameEditText.setText(<span class="string">"test"</span>)
<span class="caretline"><span class="lineno">  53 </span>        passwordEditText.setText(<span class="string">"</span><span class="warning"><span class="string">123456</span></span><span class="string">"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  54 </span>    }
<span class="lineno">  55 </span>
<span class="lineno">  56 </span>    private <span class="keyword">fun</span> initViewModel() {
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSetTextI18n" style="display: none;">
When calling <code>TextView#setText</code><br/>
* Never call <code>Number#toString()</code> to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using <code>String#format</code> with proper format specifications (<code>%d</code> or <code>%f</code>) instead.<br/>
* Do not pass a string literal (e.g. "Hello") to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.<br/>
* Do not build messages by concatenating text chunks. Such messages can not be properly translated.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/localization.html">https://developer.android.com/guide/topics/resources/localization.html</a>
</div>To suppress this error, use the issue id "SetTextI18n" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SetTextI18n</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSetTextI18nLink" onclick="reveal('explanationSetTextI18n');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SetTextI18nCardLink" onclick="hideid('SetTextI18nCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="HardcodedText"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HardcodedTextCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Hardcoded text</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_test.xml">../../src/main/res/layout/activity_test.xml</a>:13</span>: <span class="message">Hardcoded string "&#31995;&#32479;&#38598;&#25104;&#27979;&#35797;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 11 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 12 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 13 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"系统集成测试"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"32sp"</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/white"</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"32dp"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_test.xml">../../src/main/res/layout/activity_test.xml</a>:22</span>: <span class="message">Hardcoded string "&#27491;&#22312;&#36816;&#34892;&#31995;&#32479;&#27979;&#35797;...", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 19 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 20 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 21 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 22 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"正在运行系统测试..."</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 23 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 24 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/white"</span>
<span class="lineno"> 25 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"24dp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_test.xml">../../src/main/res/layout/activity_test.xml</a>:35</span>: <span class="message">Hardcoded string "&#27979;&#35797;&#39033;&#30446;&#65306;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 32 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 33 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 34 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 35 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"测试项目："</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 36 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno"> 37 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/white"</span>
<span class="lineno"> 38 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"16dp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_test.xml">../../src/main/res/layout/activity_test.xml</a>:43</span>: <span class="message">Hardcoded string "&#8226; &#35774;&#22791;&#20860;&#23481;&#24615;&#27979;&#35797;n&#8226; API&#36830;&#25509;&#27979;&#35797;n&#8226; &#20869;&#23384;&#24615;&#33021;&#27979;&#35797;n&#8226; &#36965;&#25511;&#22120;&#21151;&#33021;&#27979;&#35797;n&#8226; UI&#21709;&#24212;&#26102;&#38388;&#27979;&#35797;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 40 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 41 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 42 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 43 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"• 设备兼容性测试\n• API连接测试\n• 内存性能测试\n• 遥控器功能测试\n• UI响应时间测试"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 44 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="lineno"> 45 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/white"</span>
<span class="lineno"> 46 </span>        <span class="prefix">android:</span><span class="attribute">lineSpacingExtra</span>=<span class="value">"4dp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_test.xml">../../src/main/res/layout/activity_test.xml</a>:51</span>: <span class="message">Hardcoded string "&#35831;&#26597;&#30475;&#26085;&#24535;&#33719;&#21462;&#35814;&#32454;&#27979;&#35797;&#32467;&#26524;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 48 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 49 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 50 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 51 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"请查看日志获取详细测试结果"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 52 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"12sp"</span>
<span class="lineno"> 53 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#CCFFFFFF"</span>
<span class="lineno"> 54 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"32dp"</span> />
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="HardcodedTextDivLink" onclick="reveal('HardcodedTextDiv');" />+ 6 More Occurrences...</button>
<div id="HardcodedTextDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/fragment_login.xml">../../src/main/res/layout/fragment_login.xml</a>:15</span>: <span class="message">Hardcoded string "App Logo", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 12 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"120dp"</span>
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"32dp"</span>
<span class="lineno"> 14 </span>        <span class="prefix">android:</span><span class="attribute">src</span>=<span class="value">"@drawable/app_icon_your_company"</span>
<span class="caretline"><span class="lineno"> 15 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"App Logo"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 16 </span>
<span class="lineno"> 17 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 18 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span></pre>

<span class="location"><a href="../../src/main/res/layout/fragment_login.xml">../../src/main/res/layout/fragment_login.xml</a>:20</span>: <span class="message">Hardcoded string "MovieTV", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 17 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 18 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 19 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 20 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"MovieTV"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 21 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"32sp"</span>
<span class="lineno"> 22 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/white"</span>
<span class="lineno"> 23 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"48dp"</span></pre>

<span class="location"><a href="../../src/main/res/layout/fragment_login.xml">../../src/main/res/layout/fragment_login.xml</a>:31</span>: <span class="message">Hardcoded string "&#29992;&#25143;&#21517;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 28 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"400dp"</span>
<span class="lineno"> 29 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"56dp"</span>
<span class="lineno"> 30 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"16dp"</span>
<span class="caretline"><span class="lineno"> 31 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">hint</span>=<span class="value">"用户名"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 32 </span>        <span class="prefix">android:</span><span class="attribute">textColorHint</span>=<span class="value">"@android:color/white"</span>
<span class="lineno"> 33 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/white"</span>
<span class="lineno"> 34 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/edit_text_background"</span></pre>

<span class="location"><a href="../../src/main/res/layout/fragment_login.xml">../../src/main/res/layout/fragment_login.xml</a>:45</span>: <span class="message">Hardcoded string "&#23494;&#30721;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 42 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"400dp"</span>
<span class="lineno"> 43 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"56dp"</span>
<span class="lineno"> 44 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"32dp"</span>
<span class="caretline"><span class="lineno"> 45 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">hint</span>=<span class="value">"密码"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 46 </span>        <span class="prefix">android:</span><span class="attribute">textColorHint</span>=<span class="value">"@android:color/white"</span>
<span class="lineno"> 47 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/white"</span>
<span class="lineno"> 48 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/edit_text_background"</span></pre>

<span class="location"><a href="../../src/main/res/layout/fragment_login.xml">../../src/main/res/layout/fragment_login.xml</a>:66</span>: <span class="message">Hardcoded string "&#30331;&#24405;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 63 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"56dp"</span>
<span class="lineno"> 64 </span>            <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 65 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 66 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"登录"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 67 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 68 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/button_background"</span>
<span class="lineno"> 69 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/white"</span></pre>

<span class="location"><a href="../../src/main/res/layout/fragment_login.xml">../../src/main/res/layout/fragment_login.xml</a>:79</span>: <span class="message">Hardcoded string "&#27880;&#20876;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 76 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"56dp"</span>
<span class="lineno"> 77 </span>            <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 78 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 79 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"注册"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 80 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 81 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/button_background"</span>
<span class="lineno"> 82 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/white"</span></pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationHardcodedText" style="display: none;">
Hardcoding text attributes directly in layout files is bad for several reasons:<br/>
<br/>
* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)<br/>
<br/>
* The application cannot be translated to other languages by just adding new translations for existing string resources.<br/>
<br/>
There are quickfixes to automatically extract this hardcoded string into a resource lookup.<br/>To suppress this error, use the issue id "HardcodedText" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HardcodedText</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHardcodedTextLink" onclick="reveal('explanationHardcodedText');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HardcodedTextCardLink" onclick="hideid('HardcodedTextCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="ExtraIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ExtraIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Included Additional Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
This card lists all the extra checks run by lint, provided from libraries,
build configuration and extra flags. This is included to help you verify
whether a particular check is included in analysis when configuring builds.
(Note that the list does not include the hundreds of built-in checks into lint,
only additional ones.)
<div id="IncludedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">DetachAndAttachSameFragment<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When doing a FragmentTransaction that includes both attach()                 and detach() operations being committed on the same fragment instance, it is a                 no-op. The reason for this is that the FragmentManager optimizes all operations                 within a single transaction so the attach() and detach() cancel each other out                 and neither is actually executed. To get the desired behavior, you should separate                 the attach() and detach() calls into separate FragmentTransactions.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DialogFragmentCallbacksDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using a <code>DialogFragment</code>, the <code>setOnCancelListener</code> and                 <code>setOnDismissListener</code> callback functions within the <code>onCreateDialog</code> function                  __must not be used__ because the <code>DialogFragment</code> owns these callbacks.                  Instead the respective <code>onCancel</code> and <code>onDismiss</code> functions can be used to                  achieve the desired effect.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerMetadata<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a library defines a Initializer, it needs to be accompanied by a corresponding &lt;meta-data> entry in the AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerNoArgConstr<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Every <code>Initializer</code> must have a no argument constructor.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExperimentalAnnotationRetention<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental annotations defined in Java source should use default (<code>CLASS</code>) retention, while Kotlin-sourced annotations should use <code>BINARY</code> retention.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentAddMenuProvider<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentBackPressedCallback<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentLiveDataObserve<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When observing a LiveData object from a fragment's onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment's view is active.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentTagUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
FragmentContainerView replaces the &lt;fragment> tag as the preferred                 way of adding fragments via XML. Unlike the &lt;fragment> tag, FragmentContainerView                 uses a normal <code>FragmentTransaction</code> under the hood to add the initial fragment,                 allowing further FragmentTransaction operations on the FragmentContainerView                 and providing a consistent timing for lifecycle events.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html">https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidFragmentVersionForActivityResult<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In order to use the ActivityResult APIs you must upgrade your                 Fragment version to 1.3.0. Previous versions of FragmentActivity                 failed to call super.onRequestPermissionsResult() and used invalid request codes<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/permissions/requesting#make-the-request">https://developer.android.com/training/permissions/requesting#make-the-request</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.activity<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=527362">https://issuetracker.google.com/issues/new?component=527362</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidSetHasFixedSize<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a RecyclerView uses <code>setHasFixedSize(...)</code> you cannot use <code>wrap_content</code> for  size in the scrolling direction.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.recyclerview<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460887">https://issuetracker.google.com/issues/new?component=460887</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NullSafeMutableLiveData<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This check ensures that LiveData values are not null when explicitly                 declared as non-nullable.<br/>
<br/>
                Kotlin interoperability does not support enforcing explicit null-safety when using                 generic Java type parameters. Since LiveData is a Java class its value can always                 be null even when its type is explicitly declared as non-nullable. This can lead                 to runtime exceptions from reading a null LiveData value that is assumed to be                 non-nullable.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RepeatOnLifecycleWrongUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used when the View is created,                 that is in the <code>onCreate</code> lifecycle method for Activities, or <code>onViewCreated</code> in                 case you're using Fragments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeLifecycleWhenUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the <code>Lifecycle</code> is destroyed within the block of                     <code>Lifecycle.whenStarted</code> or any similar <code>Lifecycle.when</code> method is suspended,                     the block will be cancelled, which will also cancel any child coroutine                     launched inside the block. As as a result, If you have a try finally block                     in your code, the finally might run after the Lifecycle moves outside                     the desired state. It is recommended to check the <code>Lifecycle.isAtLeast</code>                     before accessing UI in finally block. Similarly,                     if you have a catch statement that might catch <code>CancellationException</code>,                     you should check the <code>Lifecycle.isAtLeast</code> before accessing the UI. See                     documentation of <code>Lifecycle.whenStateAtLeast</code> for more details<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageError<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with error-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageError</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageError">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageWarning<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with warning-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageWarning</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageWarning">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeRepeatOnLifecycleDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used with the viewLifecycleOwner                 in Fragments as opposed to lifecycleOwner.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseGetLayoutInflater<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Using LayoutInflater.from(Context) can return a LayoutInflater                  that does not have the correct theme.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseRequireInsteadOfGet<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
AndroidX added new "require____()" versions of common "get___()" APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IncludedIssuesLink" onclick="reveal('IncludedIssues');">
List Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ExtraIssuesCardLink" onclick="hideid('ExtraIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="MissingIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Disabled Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
One or more issues were not run by lint, either
because the check is not enabled by default, or because
it was disabled with a command line flag or via one or
more <code>lint.xml</code> configuration files in the project directories.
<div id="SuppressedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AppCompatMethod<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
When using the appcompat library, there are some methods you should be calling instead of the normal ones; for example, <code>getSupportActionBar()</code> instead of <code>getActionBar()</code>. This lint check looks for calls to the wrong method.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/libraries/support-library/">https://developer.android.com/topic/libraries/support-library/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerify<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BackButton<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Other platforms use an explicit back button with label to allow the user to navigate up the application's hierarchy. Instead, Android uses the main action bar's app icon for hierarchical navigation and the navigation bar's back button for temporal navigation."<br/>
<br/>
This check is not very sophisticated (it just looks for buttons with the label "Back"), so it is disabled by default to not trigger on common scenarios like pairs of Back/Next buttons to paginate through screens.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/design">https://d.android.com/r/studio-ui/designer/material/design</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConvertToWebp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The WebP format is typically more compact than PNG and JPEG. As of Android 4.2.1 it supports transparency and lossless conversion as well. Note that there is a quickfix in the IDE which lets you perform conversion.<br/>
<br/>
Previously, launcher icons were required to be in the PNG format but that restriction is no longer there, so lint now flags these.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DalvikOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The Dalvik virtual machine will treat a package private method in one class as overriding a package private method in its super class, even if they are in separate packages.<br/>
<br/>
If you really did intend for this method to override the other, make the method <code>protected</code> instead.<br/>
<br/>
If you did <b>not</b> intend the override, consider making the method private, or changing its name or signature.<br/>
<br/>
Note that this check is disabled be default, because ART (the successor to Dalvik) no longer has this behavior.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DefaultEncoding<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Some APIs will implicitly use the default system character encoding instead of UTF-8 when converting to or from bytes, such as when creating a default <code>FileReader</code>.<br/>
<br/>
This is <i>usually</i> not correct; you only want to do this if you need to read files created by other programs where they have deliberately written in the same encoding. The default encoding varies from platform to platform and can vary from locale to locale, so this makes it difficult to interpret files containing non-ASCII characters.<br/>
<br/>
We recommend using UTF-8 everywhere.<br/>
<br/>
Note that on Android, the default file encoding is always UTF-8 (see <a href="https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(">https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(</a>) for more), so this lint check deliberately does not flag any problems in Android code, since it is always safe to rely on the default character encoding there.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateStrings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Duplicate strings can make applications larger unnecessarily.<br/>
<br/>
This lint check looks for duplicate strings, including differences for strings where the only difference is in capitalization. Title casing and all uppercase can all be adjusted in the layout or in code.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType">https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EasterEgg<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
An "easter egg" is code deliberately hidden in the code, both from potential users and even from other developers. This lint check looks for code which looks like it may be hidden from sight.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpensiveAssertion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
In Kotlin, assertions are not handled the same way as from the Java programming language. In particular, they're just implemented as a library call, and inside the library call the error is only thrown if assertions are enabled.<br/>
<br/>
This means that the arguments to the <code>assert</code> call will <b>always</b> be evaluated. If you're doing any computation in the expression being asserted, that computation will unconditionally be performed whether or not assertions are turned on. This typically turns into wasted work in release builds.<br/>
<br/>
This check looks for cases where the assertion condition is nontrivial, e.g. it is performing method calls or doing more work than simple comparisons on local variables or fields.<br/>
<br/>
You can work around this by writing your own inline assert method instead:<br/>

<pre>
@Suppress("INVISIBLE_REFERENCE", "INVISIBLE_MEMBER")
inline fun assert(condition: () -> Boolean) {
    if (_Assertions.ENABLED &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>
In Android, because assertions are not enforced at runtime, instead use this:<br/>

<pre>
inline fun assert(condition: () -> Boolean) {
    if (BuildConfig.DEBUG &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExpectedSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
There are predefined sizes (for each density) for launcher icons. You should follow these conventions to make sure your icons fit in with the overall look of the platform.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/iconography">https://d.android.com/r/studio-ui/designer/material/iconography</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPackage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check scans through libraries looking for calls to APIs that are not included in Android.<br/>
<br/>
When you create Android projects, the classpath is set up such that you can only access classes in the API packages that are included in Android. However, if you add other projects to your libs/ folder, there is no guarantee that those .jar files were built with an Android specific classpath, and in particular, they could be accessing unsupported APIs such as java.applet.<br/>
<br/>
This check scans through library jars and looks for references to API packages that are not included in Android and flags these. This is only an error if your code calls one of the library classes which wind up referencing the unsupported package.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlinPropertyAccess<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
For a method to be represented as a property in Kotlin, strict &#8220;bean&#8221;-style prefixing must be used.<br/>
<br/>
Accessor methods require a <code>get</code> prefix or for boolean-returning methods an <code>is</code> prefix can be used.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#property-prefixes">https://android.github.io/kotlin-guides/interop.html#property-prefixes</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlincFE10<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
K2, the new version of Kotlin compiler, which encompasses the new frontend, is coming. Try to avoid using internal APIs from the old frontend if possible.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LambdaLast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve calling this code from Kotlin, parameter types eligible for SAM conversion should be last.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last">https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintDocExample<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Lint's tool for generating documentation for each issue has special support for including a code example which shows how to trigger the report. It will pick the first unit test it can find and pick out the source file referenced from the error message, but you can instead designate a unit test to be the documentation example, and in that case, all the files are included.<br/>
<br/>
To designate a unit test as the documentation example for an issue, name the test <code>testDocumentationExample</code>, or if your detector reports multiple issues, <code>testDocumentationExample</code>&lt;Id>, such as <code>testDocumentationExampleMyId</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplPsiEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
You should never compare two PSI elements for equality with <code>equals</code>; use <code>PsiEquivalenceUtil.areElementsEquivalent(PsiElement, PsiElement)</code> instead.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUnexpectedDomain<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This checks flags URLs to domains that have not been explicitly allowed for use as a documentation source.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogConditional<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>BuildConfig</code> class provides a constant, <code>DEBUG</code>, which indicates whether the code is being built in release mode or in debug mode. In release mode, you typically want to strip out all the logging calls. Since the compiler will automatically remove all code which is inside a <code>if (false)</code> check, surrounding your logging calls with a check for <code>BuildConfig.DEBUG</code> is a good idea.<br/>
<br/>
If you <b>really</b> intend for the logging to be present in release mode, you can suppress this warning with a <code>@SuppressLint</code> annotation for the intentional logging calls.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MangledCRLF<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
On Windows, line endings are typically recorded as carriage return plus newline: \r\n.<br/>
<br/>
This detector looks for invalid line endings with repeated carriage return characters (without newlines). Previous versions of the ADT plugin could accidentally introduce these into the file, and when editing the file, the editor could produce confusing visual artifacts.<br/><div class="moreinfo">More info: <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421">https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MinSdkTooLow<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The value of the <code>minSdkVersion</code> property is too low and can be incremented without noticeably reducing the number of supported devices.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NegativeMargin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Margin values should be positive. Negative values are generally a sign that you are making assumptions about views surrounding the current one, or may be tempted to turn off child clipping to allow a view to escape its parent. Turning off child clipping to do this not only leads to poor graphical performance, it also results in wrong touch event handling since touch events are based strictly on a chain of parent-rect hit tests. Finally, making assumptions about the size of strings can lead to localization problems.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NewerVersionAvailable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the <code>GradleDependency</code> check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also <b>much</b> slower.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoHardKeywords<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Do not use Kotlin&#8217;s hard keywords as the name of methods or fields. These require the use of backticks to escape when calling from Kotlin. Soft keywords, modifier keywords, and special identifiers are allowed.<br/>
<br/>
For example, ActionEvent's <code>getWhen()</code> method requires backticks when used from Kotlin:
<pre>
val timestamp = event.`when`
</pre>
<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#no-hard-keywords">https://android.github.io/kotlin-guides/interop.html#no-hard-keywords</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoOp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check looks for code which looks like it's a no-op -- usually leftover expressions from interactive debugging, but in some cases bugs where you had intended to do something with the expression such as assign it to a field.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>pure-getters</b> (default is false):<br/>
Whether to assume methods with getter-names have no side effects.<br/>
<br/>
Getter methods (where names start with <code>get</code> or <code>is</code>, and have non-void return types, and no arguments) should not have side effects. With this option turned on, lint will assume that is the case and will list any getter calls whose results are ignored as suspicious code.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"NoOp"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"pure-getters"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionNamingConvention<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Permissions should be prefixed with an app's package name, using reverse-domain-style naming. This prefix should be followed by <code>.permission.</code>, and then a description of the capability that the permission represents, in upper SNAKE_CASE. For example, <code>com.example.myapp.permission.ENGAGE_HYPERSPACE</code>.<br/>
<br/>
Following this recommendation avoids naming collisions, and helps clearly identify the owner and intention of a custom permission.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PrivacySandboxBlockedCall<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Many APIs are unavailable in the Privacy Sandbox, depending on the <code>targetSdk</code>.<br/>
<br/>
If your code is designed to run in the sandbox (and never outside the sandbox) then you should remove the blocked calls to avoid exceptions at runtime.<br/>
<br/>
If your code is part of a library that can be executed both inside and outside the sandbox, surround the code with <code>if (!Process.isSdkSandbox()) { ... }</code> (or use your own field or method annotated with <code>@ChecksRestrictedEnvironment</code>) to avoid executing blocked calls when in the sandbox. Or, add the <code>@RestrictedForEnvironment</code> annotation to the containing method if the entire method should not be called when in the sandbox.<br/>
<br/>
This check is disabled by default, and should only be enabled in modules that may execute in the Privacy Sandbox.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Registered<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Activities, services and content providers should be registered in the <code>AndroidManifest.xml</code> file using <code>&lt;activity></code>, <code>&lt;service></code> and <code>&lt;provider></code> tags.<br/>
<br/>
If your activity is simply a parent class intended to be subclassed by other "real" activities, make it an abstract class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiredSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
All views must specify an explicit <code>layout_width</code> and <code>layout_height</code> attribute. There is a runtime check for this, so if you fail to specify a size, an exception is thrown at runtime.<br/>
<br/>
It's possible to specify these widths via styles as well. GridLayout, as a special case, does not require you to specify a size.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SelectableText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
If a <code>&lt;TextView></code> is used to display data, the user might want to copy that data and paste it elsewhere. To allow this, the <code>&lt;TextView></code> should specify <code>android:textIsSelectable="true"</code>.<br/>
<br/>
This lint check looks for TextViews which are likely to be displaying data: views whose text is set dynamically.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StopShip<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Using the comment <code>// STOPSHIP</code> can be used to flag code that is incomplete but checked in. This comment marker can be used to indicate that the code should not be shipped until the issue is addressed, and lint will look for these. In Gradle projects, this is only checked for non-debug (release) builds.<br/>
<br/>
In Kotlin, the <code>TODO()</code> method is also treated as a stop ship marker; you can use it to make incomplete code compile, but it will throw an exception at runtime and therefore should be fixed before shipping releases.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatTrivial<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Every call to <code>String.format</code> creates a new <code>Formatter</code> instance, which will decrease the performance of your app. <code>String.format</code> should only be used when necessary--if the formatted string contains only trivial conversions (e.g. <code>b</code>, <code>s</code>, <code>c</code>) and there are no translation concerns, it will be more efficient to replace them and concatenate with <code>+</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SyntheticAccessor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
A private inner class which is accessed from the outer class will force the compiler to insert a synthetic accessor; this means that you are causing extra overhead. This is not important in small projects, but is important for large apps running up against the 64K method handle limit, and especially for <b>libraries</b> where you want to make sure your library is as small as possible for the cases where your library is used in an app running up against the 64K limit.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyQuotes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Straight single quotes and double quotes, when used as a pair, can be replaced by "curvy quotes" (or directional quotes). Use the right single quotation mark for apostrophes. Never use generic quotes ", ' or free-standing accents `, ´ for quotation marks, apostrophes, or primes. This can make the text more readable.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Quotation_mark">https://en.wikipedia.org/wiki/Quotation_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownNullness<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve referencing this code from Kotlin, consider adding explicit nullness information here with either <code>@NonNull</code> or <code>@Nullable</code>.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>ignore-deprecated</b> (default is false):<br/>
Whether to ignore classes and members that have been annotated with <code>@Deprecated</code>.<br/>
<br/>
Normally this lint check will flag all unannotated elements, but by setting this option to <code>true</code> it will skip any deprecated elements.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnknownNullness"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"ignore-deprecated"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><div class="moreinfo">More info: <a href="https://developer.android.com/kotlin/interop#nullability_annotations">https://developer.android.com/kotlin/interop#nullability_annotations</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported large screen hardware feature. Any &lt;uses-feature> not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a ChromeOS, large screen, or foldable device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#incompat-entries">https://developer.android.com/topic/arc/manifest.html#incompat-entries</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This resource id definition appears not to be needed since it is not referenced from anywhere. Having id definitions, even if unused, is not necessarily a bad idea since they make working on layouts and menus easier, so there is not a strong reason to delete these.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidActionsXml<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that an actions XML file is properly formed<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VulnerableCordovaVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The version of Cordova used in the app is vulnerable to security issues. Please update to the latest Apache Cordova version.<br/><div class="moreinfo">More info: <a href="https://cordova.apache.org/announcements/2015/11/20/security.html">https://cordova.apache.org/announcements/2015/11/20/security.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThreadInterprocedural<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Searches for interprocedural call paths that violate thread annotations in the program. Tracks the flow of instantiated types and lambda expressions to increase accuracy across method boundaries.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div><br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SuppressedIssuesLink" onclick="reveal('SuppressedIssues');">
List Missing Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingIssuesCardLink" onclick="hideid('MissingIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>