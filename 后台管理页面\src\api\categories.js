import request from '@/utils/request'

// 获取分类列表
export function getCategories(params) {
  return request({
    url: '/categories',
    method: 'get',
    params
  })
}

// 获取单个分类
export function getCategory(id) {
  return request({
    url: `/categories/${id}`,
    method: 'get'
  })
}

// 创建分类
export function createCategory(data) {
  return request({
    url: '/categories',
    method: 'post',
    data
  })
}

// 更新分类
export function updateCategory(data) {
  return request({
    url: `/categories/${data.id}`,
    method: 'put',
    data
  })
}

// 删除分类
export function deleteCategory(id) {
  return request({
    url: `/categories/${id}`,
    method: 'delete'
  })
}

// 更新分类状态
export function updateCategoryStatus(id, status) {
  return request({
    url: `/categories/${id}/status`,
    method: 'patch',
    data: { status }
  })
}

// 获取分类树
export function getCategoryTree(type) {
  return request({
    url: '/categories/tree',
    method: 'get',
    params: { type }
  })
}

// 批量操作分类
export function batchUpdateCategories(data) {
  return request({
    url: '/categories/batch',
    method: 'patch',
    data
  })
}
