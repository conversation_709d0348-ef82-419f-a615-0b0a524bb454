import request from '@/utils/request'

// 获取应用列表
export function getApps(params) {
  return request({
    url: '/apps',
    method: 'get',
    params
  })
}

// 获取单个应用
export function getApp(id) {
  return request({
    url: `/apps/${id}`,
    method: 'get'
  })
}

// 创建应用
export function createApp(data) {
  return request({
    url: '/apps',
    method: 'post',
    data
  })
}

// 更新应用
export function updateApp(data) {
  return request({
    url: `/apps/${data.id}`,
    method: 'put',
    data
  })
}

// 删除应用
export function deleteApp(id) {
  return request({
    url: `/apps/${id}`,
    method: 'delete'
  })
}

// 更新应用状态
export function updateAppStatus(id, status) {
  return request({
    url: `/apps/${id}/status`,
    method: 'patch',
    data: { status }
  })
}

// 批量操作应用
export function batchUpdateApps(data) {
  return request({
    url: '/apps/batch',
    method: 'patch',
    data
  })
}

// 获取应用统计
export function getAppStatistics() {
  return request({
    url: '/apps/statistics',
    method: 'get'
  })
}
