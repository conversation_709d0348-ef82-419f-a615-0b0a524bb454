com.example.myapplicationtv.app-annotation-experimental-1.3.0-0 D:\Android\GradleRepository\caches\8.12\transforms\03f0f7d3357ba78868c8f6b45210dab1\transformed\annotation-experimental-1.3.0\res
com.example.myapplicationtv.app-lifecycle-viewmodel-2.7.0-1 D:\Android\GradleRepository\caches\8.12\transforms\09b2070107f58b91baaaa03824de0d80\transformed\lifecycle-viewmodel-2.7.0\res
com.example.myapplicationtv.app-lifecycle-livedata-2.7.0-2 D:\Android\GradleRepository\caches\8.12\transforms\0b77184f006223771d62c67bbd676e73\transformed\lifecycle-livedata-2.7.0\res
com.example.myapplicationtv.app-savedstate-ktx-1.2.1-3 D:\Android\GradleRepository\caches\8.12\transforms\220f8c0f15f6aa3f199c619409bad374\transformed\savedstate-ktx-1.2.1\res
com.example.myapplicationtv.app-activity-ktx-1.7.2-4 D:\Android\GradleRepository\caches\8.12\transforms\22ec3492cd38aff0fdd3abd4ae94272b\transformed\activity-ktx-1.7.2\res
com.example.myapplicationtv.app-lifecycle-livedata-ktx-2.7.0-5 D:\Android\GradleRepository\caches\8.12\transforms\2cd992ff3743b623f80bb69d849f50ca\transformed\lifecycle-livedata-ktx-2.7.0\res
com.example.myapplicationtv.app-cardview-1.0.0-6 D:\Android\GradleRepository\caches\8.12\transforms\304761040604b9580785c2d5f3e785f2\transformed\cardview-1.0.0\res
com.example.myapplicationtv.app-fragment-ktx-1.6.2-7 D:\Android\GradleRepository\caches\8.12\transforms\374ceb5124eacb51f9cd2e0f0855c4f3\transformed\fragment-ktx-1.6.2\res
com.example.myapplicationtv.app-lifecycle-runtime-ktx-2.7.0-8 D:\Android\GradleRepository\caches\8.12\transforms\44b9bab188f5f5366f3115537e09a453\transformed\lifecycle-runtime-ktx-2.7.0\res
com.example.myapplicationtv.app-glide-4.11.0-9 D:\Android\GradleRepository\caches\8.12\transforms\59e3767b96f4ee90f4b57a0c56b92a96\transformed\glide-4.11.0\res
com.example.myapplicationtv.app-core-ktx-1.10.1-10 D:\Android\GradleRepository\caches\8.12\transforms\6758fc4f4664cc3e72a8df28e7d3f5da\transformed\core-ktx-1.10.1\res
com.example.myapplicationtv.app-customview-poolingcontainer-1.0.0-11 D:\Android\GradleRepository\caches\8.12\transforms\6c7c511ec8b68e45e391fb30b6d5f144\transformed\customview-poolingcontainer-1.0.0\res
com.example.myapplicationtv.app-lifecycle-livedata-core-ktx-2.7.0-12 D:\Android\GradleRepository\caches\8.12\transforms\6d91530bd68c1a5268acfee80590353b\transformed\lifecycle-livedata-core-ktx-2.7.0\res
com.example.myapplicationtv.app-core-1.10.1-13 D:\Android\GradleRepository\caches\8.12\transforms\850521a42fbfe952cd99f6631de94ce6\transformed\core-1.10.1\res
com.example.myapplicationtv.app-leanback-1.0.0-14 D:\Android\GradleRepository\caches\8.12\transforms\89d10e7acdc9e8617fe8b024a2336641\transformed\leanback-1.0.0\res
com.example.myapplicationtv.app-lifecycle-runtime-2.7.0-15 D:\Android\GradleRepository\caches\8.12\transforms\9262bdae479e1b658eb70c7bbd10b157\transformed\lifecycle-runtime-2.7.0\res
com.example.myapplicationtv.app-activity-1.7.2-16 D:\Android\GradleRepository\caches\8.12\transforms\9717948c970c19acce1692941c22c532\transformed\activity-1.7.2\res
com.example.myapplicationtv.app-savedstate-1.2.1-17 D:\Android\GradleRepository\caches\8.12\transforms\b9b3bed66d3a00e5a31ccb8c12557bab\transformed\savedstate-1.2.1\res
com.example.myapplicationtv.app-fragment-1.6.2-18 D:\Android\GradleRepository\caches\8.12\transforms\ccfd53c838c812e96e4910096138f89c\transformed\fragment-1.6.2\res
com.example.myapplicationtv.app-coordinatorlayout-1.0.0-19 D:\Android\GradleRepository\caches\8.12\transforms\cec8346c4e2ab21f142e352fe0bfc4ee\transformed\coordinatorlayout-1.0.0\res
com.example.myapplicationtv.app-recyclerview-1.3.2-20 D:\Android\GradleRepository\caches\8.12\transforms\cffe277412d6e71dab1701f200c8c21e\transformed\recyclerview-1.3.2\res
com.example.myapplicationtv.app-media-1.0.0-21 D:\Android\GradleRepository\caches\8.12\transforms\d1cb9debee0ec7aff0e9116da1dc9922\transformed\media-1.0.0\res
com.example.myapplicationtv.app-lifecycle-viewmodel-ktx-2.7.0-22 D:\Android\GradleRepository\caches\8.12\transforms\e2630f862baa837dd8db5f1a6cd2b6cf\transformed\lifecycle-viewmodel-ktx-2.7.0\res
com.example.myapplicationtv.app-lifecycle-viewmodel-savedstate-2.7.0-23 D:\Android\GradleRepository\caches\8.12\transforms\e71662f1e872c884e7f2d1e2bf0f11d8\transformed\lifecycle-viewmodel-savedstate-2.7.0\res
com.example.myapplicationtv.app-profileinstaller-1.3.0-24 D:\Android\GradleRepository\caches\8.12\transforms\e930fab5838d590452cd3b9f5df617df\transformed\profileinstaller-1.3.0\res
com.example.myapplicationtv.app-lifecycle-livedata-core-2.7.0-25 D:\Android\GradleRepository\caches\8.12\transforms\e986c459db27042bd4bd99648aeb71d5\transformed\lifecycle-livedata-core-2.7.0\res
com.example.myapplicationtv.app-core-runtime-2.2.0-26 D:\Android\GradleRepository\caches\8.12\transforms\f995e7e8cc3070312bef138ddf684057\transformed\core-runtime-2.2.0\res
com.example.myapplicationtv.app-startup-runtime-1.1.1-27 D:\Android\GradleRepository\caches\8.12\transforms\fc95bcae0ba172be27074a8a115ec3a7\transformed\startup-runtime-1.1.1\res
com.example.myapplicationtv.app-pngs-28 E:\aikaifa\MovieTV\MyApplication2\app\build\generated\res\pngs\debug
com.example.myapplicationtv.app-resValues-29 E:\aikaifa\MovieTV\MyApplication2\app\build\generated\res\resValues\debug
com.example.myapplicationtv.app-packageDebugResources-30 E:\aikaifa\MovieTV\MyApplication2\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.myapplicationtv.app-packageDebugResources-31 E:\aikaifa\MovieTV\MyApplication2\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.myapplicationtv.app-debug-32 E:\aikaifa\MovieTV\MyApplication2\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.myapplicationtv.app-debug-33 E:\aikaifa\MovieTV\MyApplication2\app\src\debug\res
com.example.myapplicationtv.app-main-34 E:\aikaifa\MovieTV\MyApplication2\app\src\main\res
