<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-user">
              <el-icon class="card-panel-icon"><User /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总用户数</div>
              <div class="card-panel-num">{{ statistics.totalUsers }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-movie">
              <el-icon class="card-panel-icon"><VideoPlay /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总电影数</div>
              <div class="card-panel-num">{{ statistics.totalMovies }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-app">
              <el-icon class="card-panel-icon"><Mobile /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总应用数</div>
              <div class="card-panel-num">{{ statistics.totalApps }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-game">
              <el-icon class="card-panel-icon"><Trophy /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总游戏数</div>
              <div class="card-panel-num">{{ statistics.totalGames }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-product">
              <el-icon class="card-panel-icon"><ShoppingBag /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总商品数</div>
              <div class="card-panel-num">{{ statistics.totalProducts }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-favorite">
              <el-icon class="card-panel-icon"><Star /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总收藏数</div>
              <div class="card-panel-num">{{ statistics.totalFavorites }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-history">
              <el-icon class="card-panel-icon"><Clock /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总观看数</div>
              <div class="card-panel-num">{{ statistics.totalHistory }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-category">
              <el-icon class="card-panel-icon"><Folder /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总分类数</div>
              <div class="card-panel-num">{{ statistics.totalCategories }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>内容类型分布</span>
            </div>
          </template>
          <div ref="contentChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>用户活跃度</span>
            </div>
          </template>
          <div ref="userChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
            </div>
          </template>
          <el-table :data="recentActivities" style="width: 100%">
            <el-table-column prop="type" label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getActivityTypeColor(row.type)">
                  {{ getActivityTypeName(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" />
            <el-table-column prop="user" label="用户" width="120" />
            <el-table-column prop="time" label="时间" width="180" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getStatisticsOverview } from '@/api/statistics'

export default {
  name: 'StatisticsOverview',
  data() {
    return {
      statistics: {
        totalUsers: 0,
        totalMovies: 0,
        totalApps: 0,
        totalGames: 0,
        totalProducts: 0,
        totalFavorites: 0,
        totalHistory: 0,
        totalCategories: 0
      },
      recentActivities: []
    }
  },
  mounted() {
    this.getStatistics()
    this.initCharts()
  },
  methods: {
    getStatistics() {
      getStatisticsOverview().then(response => {
        this.statistics = response.data.statistics
        this.recentActivities = response.data.recentActivities
        this.updateCharts()
      })
    },
    initCharts() {
      this.contentChart = echarts.init(this.$refs.contentChart)
      this.userChart = echarts.init(this.$refs.userChart)
    },
    updateCharts() {
      // 内容类型分布饼图
      const contentOption = {
        title: {
          text: '内容分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '内容数量',
            type: 'pie',
            radius: '50%',
            data: [
              { value: this.statistics.totalMovies, name: '电影' },
              { value: this.statistics.totalApps, name: '应用' },
              { value: this.statistics.totalGames, name: '游戏' },
              { value: this.statistics.totalProducts, name: '商品' }
            ]
          }
        ]
      }
      this.contentChart.setOption(contentOption)

      // 用户活跃度柱状图
      const userOption = {
        title: {
          text: '用户统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['总用户', '收藏', '观看历史']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [
              this.statistics.totalUsers,
              this.statistics.totalFavorites,
              this.statistics.totalHistory
            ],
            type: 'bar'
          }
        ]
      }
      this.userChart.setOption(userOption)
    },
    getActivityTypeName(type) {
      const typeMap = {
        'USER_REGISTER': '用户注册',
        'USER_LOGIN': '用户登录',
        'CONTENT_ADD': '内容添加',
        'CONTENT_UPDATE': '内容更新',
        'FAVORITE_ADD': '添加收藏',
        'HISTORY_ADD': '观看记录'
      }
      return typeMap[type] || type
    },
    getActivityTypeColor(type) {
      const colorMap = {
        'USER_REGISTER': 'success',
        'USER_LOGIN': 'primary',
        'CONTENT_ADD': 'warning',
        'CONTENT_UPDATE': 'info',
        'FAVORITE_ADD': 'danger',
        'HISTORY_ADD': 'success'
      }
      return colorMap[type] || 'info'
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-panel {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
}

.card-panel-icon-wrapper {
  float: left;
  overflow: hidden;
  color: #fff;
  padding: 16px;
  border-radius: 6px;
  margin-right: 16px;
}

.card-panel-icon {
  font-size: 48px;
}

.icon-user {
  background: linear-gradient(315deg, #4fc3f7 0%, #2196f3 74%);
}

.icon-movie {
  background: linear-gradient(315deg, #ff9800 0%, #f57c00 74%);
}

.icon-app {
  background: linear-gradient(315deg, #4caf50 0%, #388e3c 74%);
}

.icon-game {
  background: linear-gradient(315deg, #9c27b0 0%, #7b1fa2 74%);
}

.icon-product {
  background: linear-gradient(315deg, #f44336 0%, #d32f2f 74%);
}

.icon-favorite {
  background: linear-gradient(315deg, #ffeb3b 0%, #fbc02d 74%);
}

.icon-history {
  background: linear-gradient(315deg, #607d8b 0%, #455a64 74%);
}

.icon-category {
  background: linear-gradient(315deg, #795548 0%, #5d4037 74%);
}

.card-panel-description {
  float: right;
  font-weight: bold;
  margin: 26px 0 26px 0;
}

.card-panel-text {
  line-height: 18px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;
  margin-bottom: 12px;
}

.card-panel-num {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.85);
}
</style>
