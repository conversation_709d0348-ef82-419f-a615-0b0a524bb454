<template>
  <div class="category-list">
    <div class="page-header">
      <h1>分类管理</h1>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加分类
      </el-button>
    </div>
    
    <el-card>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        row-key="id"
        default-expand-all
      >
        <el-table-column prop="name" label="分类名称" min-width="200" />
        
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)">
              {{ getTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="icon" label="图标" width="80">
          <template #default="{ row }">
            <el-icon v-if="row.icon" :size="20">
              <component :is="row.icon" />
            </el-icon>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="sortOrder" label="排序" width="80" />
        
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button 
              :type="row.status === 1 ? 'warning' : 'success'" 
              size="small" 
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>
        
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型">
            <el-option label="影视" value="MOVIE" />
            <el-option label="应用" value="APP" />
            <el-option label="游戏" value="GAME" />
            <el-option label="商品" value="PRODUCT" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="图标" prop="icon">
          <el-input v-model="form.icon" placeholder="请输入图标名称" />
        </el-form-item>
        
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number 
            v-model="form.sortOrder" 
            :min="0" 
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { categoryAPI } from '@/api'

const loading = ref(false)
const dialogVisible = ref(false)
const submitLoading = ref(false)
const formRef = ref()
const tableData = ref([])
const currentRow = ref(null)

const form = reactive({
  name: '',
  type: '',
  icon: '',
  sortOrder: 0,
  status: 1
})

const rules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ]
}

const dialogTitle = computed(() => {
  return currentRow.value ? '编辑分类' : '添加分类'
})

// 获取分类列表
const getCategoryList = async () => {
  try {
    loading.value = true
    const response = await categoryAPI.getList()
    tableData.value = response.data || []
  } catch (error) {
    console.error('获取分类列表失败:', error)
    // 如果API调用失败，使用模拟数据作为后备
    tableData.value = [
      {
        id: '1',
        name: '动作片',
        type: 'MOVIE',
        icon: 'VideoPlay',
        sortOrder: 1,
        status: 1,
        createTime: '2025-07-03 10:00:00'
      },
      {
        id: '2',
        name: '科幻片',
        type: 'MOVIE',
        icon: 'VideoPlay',
        sortOrder: 2,
        status: 1,
        createTime: '2025-07-03 10:00:00'
      },
      {
        id: '3',
        name: '爱情片',
        type: 'MOVIE',
        icon: 'VideoPlay',
        sortOrder: 3,
        status: 1,
        createTime: '2025-07-03 10:00:00'
      }
    ]
  } finally {
    loading.value = false
  }
}

// 添加分类
const handleAdd = () => {
  currentRow.value = null
  resetForm()
  dialogVisible.value = true
}

// 编辑分类
const handleEdit = (row) => {
  currentRow.value = row
  Object.assign(form, row)
  dialogVisible.value = true
}

// 切换状态
const handleToggleStatus = async (row) => {
  try {
    const action = row.status === 1 ? '禁用' : '启用'
    await ElMessageBox.confirm(`确定要${action}这个分类吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 调用API切换状态
    // await categoryAPI.update(row.id, { status: row.status === 1 ? 0 : 1 })
    
    row.status = row.status === 1 ? 0 : 1
    ElMessage.success(`${action}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换状态失败:', error)
    }
  }
}

// 删除分类
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个分类吗？删除后无法恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 调用API删除
    // await categoryAPI.delete(row.id)
    
    ElMessage.success('删除成功')
    getCategoryList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (currentRow.value) {
      // 更新
      await categoryAPI.update(currentRow.value.id, form)
      ElMessage.success('更新成功')
    } else {
      // 创建
      await categoryAPI.create(form)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    getCategoryList()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleDialogClose = () => {
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach(key => {
    if (key === 'status') {
      form[key] = 1
    } else if (key === 'sortOrder') {
      form[key] = 0
    } else {
      form[key] = ''
    }
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 获取类型颜色
const getTypeColor = (type) => {
  const colors = {
    MOVIE: 'primary',
    APP: 'success',
    GAME: 'warning',
    PRODUCT: 'info'
  }
  return colors[type] || 'default'
}

// 获取类型名称
const getTypeName = (type) => {
  const names = {
    MOVIE: '影视',
    APP: '应用',
    GAME: '游戏',
    PRODUCT: '商品'
  }
  return names[type] || type
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString()
}

onMounted(() => {
  getCategoryList()
})
</script>

<style scoped>
.category-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}
</style>
