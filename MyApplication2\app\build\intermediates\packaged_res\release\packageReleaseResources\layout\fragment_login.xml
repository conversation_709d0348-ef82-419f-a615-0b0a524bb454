<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="48dp"
    android:background="@color/default_background">

    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        android:src="@drawable/app_icon_your_company"
        android:contentDescription="App Logo" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="MovieTV"
        android:textSize="32sp"
        android:textColor="@android:color/white"
        android:layout_marginBottom="48dp"
        android:textStyle="bold" />

    <EditText
        android:id="@+id/username_edit_text"
        android:layout_width="400dp"
        android:layout_height="56dp"
        android:layout_marginBottom="16dp"
        android:hint="用户名"
        android:textColorHint="@android:color/white"
        android:textColor="@android:color/white"
        android:background="@drawable/edit_text_background"
        android:padding="16dp"
        android:inputType="text"
        android:focusable="true"
        android:focusableInTouchMode="true" />

    <EditText
        android:id="@+id/password_edit_text"
        android:layout_width="400dp"
        android:layout_height="56dp"
        android:layout_marginBottom="32dp"
        android:hint="密码"
        android:textColorHint="@android:color/white"
        android:textColor="@android:color/white"
        android:background="@drawable/edit_text_background"
        android:padding="16dp"
        android:inputType="textPassword"
        android:focusable="true"
        android:focusableInTouchMode="true" />

    <LinearLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/login_button"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="登录"
            android:textSize="18sp"
            android:background="@drawable/button_background"
            android:textColor="@android:color/white"
            android:focusable="true"
            android:focusableInTouchMode="true" />

        <Button
            android:id="@+id/register_button"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="注册"
            android:textSize="18sp"
            android:background="@drawable/button_background"
            android:textColor="@android:color/white"
            android:focusable="true"
            android:focusableInTouchMode="true" />

    </LinearLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:visibility="gone" />

</LinearLayout>
