package com.example.houduan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 应用实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "application")
public class App {

    @Id
    @Column(length = 32)
    private String id;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 应用图标URL
     */
    private String icon;

    /**
     * 应用分类
     */
    private String category;

    /**
     * 版本号
     */
    private String version;

    /**
     * 应用大小(字节)
     */
    private Long size;

    /**
     * 评分(0.0-10.0)
     */
    private BigDecimal rating;

    /**
     * 评分人数
     */
    private Integer ratingCount;

    /**
     * 下载次数
     */
    private Long downloadCount;

    /**
     * 应用描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 下载地址
     */
    private String downloadUrl;

    /**
     * 截图URLs(JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String screenshots;

    /**
     * 是否推荐(0:否,1:是)
     */
    private Integer isRecommended;

    /**
     * 状态(0:下架,1:上架)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
