{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-pl/values-pl.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,402,501,615,720,5194", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "197,299,397,496,610,715,837,5290"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,503,628,741,838,933,1052,1169,1279,1405,1576,1697,1816,1923,2018,2144,2236,2343,2448,2552,2654,2773,2905,3026,3145,3249,3358,3466,3582,3684,3794,3881,3965,4070,4206,4363", "endColumns": "106,100,94,94,124,112,96,94,118,116,109,125,170,120,118,106,94,125,91,106,104,103,101,118,131,120,118,103,108,107,115,101,109,86,83,104,135,156,93", "endOffsets": "207,308,403,498,623,736,833,928,1047,1164,1274,1400,1571,1692,1811,1918,2013,2139,2231,2338,2443,2547,2649,2768,2900,3021,3140,3244,3353,3461,3577,3679,3789,3876,3960,4065,4201,4358,4452"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,949,1050,1145,1240,1365,1478,1575,1670,1789,1906,2016,2142,2313,2434,2553,2660,2755,2881,2973,3080,3185,3289,3391,3510,3642,3763,3882,3986,4095,4203,4319,4421,4531,4618,4702,4807,4943,5100", "endColumns": "106,100,94,94,124,112,96,94,118,116,109,125,170,120,118,106,94,125,91,106,104,103,101,118,131,120,118,103,108,107,115,101,109,86,83,104,135,156,93", "endOffsets": "944,1045,1140,1235,1360,1473,1570,1665,1784,1901,2011,2137,2308,2429,2548,2655,2750,2876,2968,3075,3180,3284,3386,3505,3637,3758,3877,3981,4090,4198,4314,4416,4526,4613,4697,4802,4938,5095,5189"}}]}]}