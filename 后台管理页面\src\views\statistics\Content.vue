<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="selectedType"
        placeholder="选择内容类型"
        style="width: 150px"
        class="filter-item"
        @change="handleTypeChange"
      >
        <el-option label="电影" value="MOVIE" />
        <el-option label="应用" value="APP" />
        <el-option label="游戏" value="GAME" />
        <el-option label="商品" value="PRODUCT" />
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="filter-item"
        @change="handleDateChange"
      />
    </div>

    <!-- 内容统计卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-total">
              <el-icon class="card-panel-icon"><Document /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总内容数</div>
              <div class="card-panel-num">{{ contentStats.total }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-active">
              <el-icon class="card-panel-icon"><Check /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">已启用</div>
              <div class="card-panel-num">{{ contentStats.active }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-recommended">
              <el-icon class="card-panel-icon"><Star /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">推荐内容</div>
              <div class="card-panel-num">{{ contentStats.recommended }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-views">
              <el-icon class="card-panel-icon"><View /></el-icon>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总浏览量</div>
              <div class="card-panel-num">{{ contentStats.totalViews }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>分类分布</span>
            </div>
          </template>
          <div ref="categoryChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>内容趋势</span>
            </div>
          </template>
          <div ref="trendChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 热门内容排行 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>热门内容排行</span>
            </div>
          </template>
          <el-table :data="popularContent" style="width: 100%">
            <el-table-column prop="rank" label="排名" width="80" align="center" />
            <el-table-column prop="name" label="名称" min-width="200" />
            <el-table-column prop="category" label="分类" width="120" />
            <el-table-column prop="views" label="浏览量" width="120" align="center" />
            <el-table-column prop="favorites" label="收藏数" width="120" align="center" />
            <el-table-column prop="rating" label="评分" width="120" align="center">
              <template #default="{ row }">
                <el-rate
                  v-model="row.rating"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'danger'">
                  {{ row.status === 'ACTIVE' ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getContentStatistics } from '@/api/statistics'

export default {
  name: 'ContentStatistics',
  data() {
    return {
      selectedType: 'MOVIE',
      dateRange: [],
      contentStats: {
        total: 0,
        active: 0,
        recommended: 0,
        totalViews: 0
      },
      popularContent: []
    }
  },
  mounted() {
    this.getStatistics()
    this.initCharts()
  },
  methods: {
    getStatistics() {
      const params = {
        type: this.selectedType,
        startDate: this.dateRange?.[0],
        endDate: this.dateRange?.[1]
      }
      getContentStatistics(params).then(response => {
        this.contentStats = response.data.stats
        this.popularContent = response.data.popularContent
        this.updateCharts(response.data)
      })
    },
    handleTypeChange() {
      this.getStatistics()
    },
    handleDateChange() {
      this.getStatistics()
    },
    initCharts() {
      // 这里可以初始化图表，需要引入echarts
      // this.categoryChart = echarts.init(this.$refs.categoryChart)
      // this.trendChart = echarts.init(this.$refs.trendChart)
    },
    updateCharts(data) {
      // 更新图表数据
      // 分类分布图表
      // 内容趋势图表
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.card-panel {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
}

.card-panel-icon-wrapper {
  float: left;
  overflow: hidden;
  color: #fff;
  padding: 16px;
  border-radius: 6px;
  margin-right: 16px;
}

.card-panel-icon {
  font-size: 48px;
}

.icon-total {
  background: linear-gradient(315deg, #4fc3f7 0%, #2196f3 74%);
}

.icon-active {
  background: linear-gradient(315deg, #4caf50 0%, #388e3c 74%);
}

.icon-recommended {
  background: linear-gradient(315deg, #ffeb3b 0%, #fbc02d 74%);
}

.icon-views {
  background: linear-gradient(315deg, #ff9800 0%, #f57c00 74%);
}

.card-panel-description {
  float: right;
  font-weight: bold;
  margin: 26px 0 26px 0;
}

.card-panel-text {
  line-height: 18px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;
  margin-bottom: 12px;
}

.card-panel-num {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.85);
}
</style>
