package com.example.myapplicationtv

import androidx.leanback.widget.AbstractDetailsDescriptionPresenter

class DetailsDescriptionPresenter : AbstractDetailsDescriptionPresenter() {

    override fun onBindDescription(
        viewHolder: AbstractDetailsDescriptionPresenter.ViewHolder,
        item: Any
    ) {
        when (item) {
            is Movie -> {
                viewHolder.title.text = item.title
                viewHolder.subtitle.text = item.studio
                viewHolder.body.text = item.description
            }
            is ContentItem -> {
                viewHolder.title.text = item.title
                viewHolder.subtitle.text = item.category
                viewHolder.body.text = item.description
            }
            else -> {
                viewHolder.title.text = "未知内容"
                viewHolder.subtitle.text = ""
                viewHolder.body.text = "无描述信息"
            }
        }
    }
}