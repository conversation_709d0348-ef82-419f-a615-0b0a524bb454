package com.example.houduan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户收藏实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "user_favorite")
public class UserFavorite {

    @Id
    @Column(length = 32)
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 电影ID
     */
    private String movieId;

    /**
     * 收藏时间
     */
    private LocalDateTime createTime;
}
