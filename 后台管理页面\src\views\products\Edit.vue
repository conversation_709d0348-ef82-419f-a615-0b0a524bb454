<template>
  <div class="app-container">
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="left"
      label-width="100px"
      style="width: 600px; margin-left:50px;"
    >
      <el-form-item label="商品名称" prop="name">
        <el-input v-model="temp.name" placeholder="请输入商品名称" />
      </el-form-item>
      <el-form-item label="商品图片" prop="image">
        <el-input v-model="temp.image" placeholder="请输入图片URL" />
        <div style="margin-top: 10px;">
          <el-image
            v-if="temp.image"
            style="width: 150px; height: 150px"
            :src="temp.image"
            fit="cover"
          />
        </div>
      </el-form-item>
      <el-form-item label="分类" prop="category">
        <el-select v-model="temp.category" placeholder="请选择分类">
          <el-option
            v-for="item in categoryOptions"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="品牌" prop="brand">
        <el-input v-model="temp.brand" placeholder="请输入品牌" />
      </el-form-item>
      <el-form-item label="价格" prop="price">
        <el-input-number
          v-model="temp.price"
          :min="0"
          :precision="2"
          placeholder="请输入价格"
        />
      </el-form-item>
      <el-form-item label="原价" prop="originalPrice">
        <el-input-number
          v-model="temp.originalPrice"
          :min="0"
          :precision="2"
          placeholder="请输入原价"
        />
      </el-form-item>
      <el-form-item label="库存" prop="stock">
        <el-input-number
          v-model="temp.stock"
          :min="0"
          placeholder="请输入库存数量"
        />
      </el-form-item>
      <el-form-item label="购买链接" prop="purchaseUrl">
        <el-input v-model="temp.purchaseUrl" placeholder="请输入购买链接" />
      </el-form-item>
      <el-form-item label="商品描述" prop="description">
        <el-input
          v-model="temp.description"
          :autosize="{ minRows: 3, maxRows: 6 }"
          type="textarea"
          placeholder="请输入商品描述"
        />
      </el-form-item>
      <el-form-item label="商品规格" prop="specifications">
        <el-input
          v-model="temp.specifications"
          :autosize="{ minRows: 2, maxRows: 4 }"
          type="textarea"
          placeholder="请输入商品规格"
        />
      </el-form-item>
      <el-form-item label="是否推荐">
        <el-switch
          v-model="temp.isRecommended"
          active-color="#13ce66"
          inactive-color="#ff4949"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-radio-group v-model="temp.status">
          <el-radio value="ACTIVE">上架</el-radio>
          <el-radio value="INACTIVE">下架</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div style="margin-left: 50px;">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="confirmData">确认</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getProduct, createProduct, updateProduct } from '@/api/products'
import { getCategories } from '@/api/categories'

const route = useRoute()
const router = useRouter()

const temp = reactive({
  id: undefined,
  name: '',
  image: '',
  category: '',
  brand: '',
  price: 0,
  originalPrice: 0,
  stock: 0,
  purchaseUrl: '',
  description: '',
  specifications: '',
  isRecommended: false,
  status: 'ACTIVE'
})

const categoryOptions = ref([])

const rules = {
  name: [{ required: true, message: '商品名称是必填项', trigger: 'blur' }],
  image: [{ required: true, message: '商品图片是必填项', trigger: 'blur' }],
  category: [{ required: true, message: '分类是必填项', trigger: 'change' }],
  brand: [{ required: true, message: '品牌是必填项', trigger: 'blur' }],
  price: [{ required: true, message: '价格是必填项', trigger: 'blur' }],
  purchaseUrl: [{ required: true, message: '购买链接是必填项', trigger: 'blur' }],
  description: [{ required: true, message: '商品描述是必填项', trigger: 'blur' }]
}

const getProductCategories = async () => {
  try {
    const response = await getCategories({ type: 'PRODUCT' })
    categoryOptions.value = response.data || []
  } catch (error) {
    console.error('获取分类失败:', error)
    // 如果API失败，使用模拟数据
    categoryOptions.value = [
      { id: 1, name: '手机' },
      { id: 2, name: '电脑' },
      { id: 3, name: '平板' },
      { id: 4, name: '配件' }
    ]
  }
}

const getProductData = async () => {
  try {
    const response = await getProduct(temp.id)
    Object.assign(temp, response.data)
  } catch (error) {
    console.error('获取商品数据失败:', error)
    ElMessage.error('获取商品数据失败')
  }
}

onMounted(async () => {
  await getProductCategories()
  if (route.params && route.params.id) {
    temp.id = route.params.id
    await getProductData()
  }
})
const confirmData = async () => {
  try {
    if (temp.id !== undefined) {
      await updateProduct(temp)
      ElMessage.success('更新成功')
    } else {
      await createProduct(temp)
      ElMessage.success('创建成功')
    }
    router.push('/products/list')
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败，请重试')
  }
}

const cancel = () => {
  router.push('/products/list')
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
