R_DEF: Internal format may change without notice
local
color accent_blue
color accent_cyan
color accent_green
color accent_orange
color accent_purple
color accent_yellow
color background_gradient_end
color background_gradient_start
color card_background
color custom_tv_background
color default_background
color fastlane_background
color navigation_background
color search_opaque
color selected_background
drawable app_category_background
drawable app_icon_your_company
drawable button_background
drawable default_background
drawable edit_text_background
drawable ic_apps
drawable ic_car
drawable ic_fitness
drawable ic_games
drawable ic_live_tv
drawable ic_movie
drawable ic_music
drawable ic_person
drawable ic_search
drawable ic_shopping
drawable ic_tv_shows
drawable ic_video_library
drawable movie
drawable movie_card_background
drawable movie_card_focus_overlay
drawable navigation_item_background
drawable search_button_background
id btn_search
id details_fragment
id focus_indicator
id iv_category_icon
id iv_icon
id iv_movie_poster
id login_button
id login_fragment
id main_content_fragment
id password_edit_text
id progress_bar
id register_button
id rv_app_categories
id rv_featured_movies
id rv_navigation
id search_fragment
id tv_category_title
id tv_date
id tv_movie_rating
id tv_movie_score
id tv_movie_title
id tv_time
id tv_title
id tv_weather
id user_center_fragment
id username_edit_text
layout activity_details
layout activity_login
layout activity_main
layout activity_search
layout activity_test
layout activity_user_center
layout fragment_login
layout fragment_main_content
layout item_app_category
layout item_featured_movie
layout item_navigation
mipmap ic_launcher
string app_name
string browse_title
string buy_1
string buy_2
string dismiss_error
string error_fragment
string error_fragment_message
string grid_view
string movie
string personal_settings
string related_movies
string rent_1
string rent_2
string watch_trailer_1
string watch_trailer_2
style Theme.MyApplicationTV
