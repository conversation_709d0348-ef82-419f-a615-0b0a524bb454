R_DEF: Internal format may change without notice
local
color accent_blue
color accent_cyan
color accent_green
color accent_orange
color accent_purple
color accent_yellow
color background_gradient_end
color background_gradient_start
color card_background
color category_focused_background
color category_normal_background
color custom_tv_background
color default_background
color fastlane_background
color focus_border_color
color navigation_background
color rating_color
color search_opaque
color selected_background
drawable app_category_background
drawable app_category_dynamic_background
drawable app_icon_your_company
drawable app_store_gradient
drawable banner_button_background
drawable button_background
drawable card_selector
drawable category_item_background
drawable default_background
drawable edit_text_background
drawable gradient_overlay
drawable ic_app_store
drawable ic_appliance
drawable ic_apps
drawable ic_book
drawable ic_car
drawable ic_clothing
drawable ic_cloud
drawable ic_computer
drawable ic_digital
drawable ic_education
drawable ic_fitness
drawable ic_food
drawable ic_games
drawable ic_headphone
drawable ic_live_tv
drawable ic_map
drawable ic_movie
drawable ic_music
drawable ic_news
drawable ic_payment
drawable ic_person
drawable ic_phone
drawable ic_search
drawable ic_service
drawable ic_shopping
drawable ic_smart_home
drawable ic_social
drawable ic_speaker
drawable ic_sports
drawable ic_tools
drawable ic_tv
drawable ic_tv_shows
drawable ic_video
drawable ic_video_library
drawable ic_work
drawable movie
drawable movie_card_background
drawable movie_card_focus_overlay
drawable navigation_item_background
drawable search_button_background
drawable smart_home_gradient
id application_fragment_container
id btn_search
id cv_app_store_banner
id cv_smart_home_banner
id details_fragment
id focus_indicator
id game_fragment_container
id iv_app_icon
id iv_category_icon
id iv_game_icon
id iv_game_screenshot
id iv_icon
id iv_movie_poster
id iv_product_image
id login_button
id login_fragment
id main_content_fragment
id password_edit_text
id progress_bar
id rating_bar
id register_button
id rv_app_categories
id rv_featured_games
id rv_featured_movies
id rv_game_categories
id rv_hot_games
id rv_hot_products
id rv_navigation
id rv_product_categories
id rv_recommended_apps
id search_fragment
id shop_fragment_container
id tv_app_description
id tv_app_name
id tv_category_name
id tv_date
id tv_game_description
id tv_game_name
id tv_game_title
id tv_movie_rating
id tv_movie_score
id tv_movie_title
id tv_product_name
id tv_product_price
id tv_rating
id tv_time
id tv_title
id tv_weather
id user_center_fragment
id username_edit_text
layout activity_application
layout activity_details
layout activity_game
layout activity_login
layout activity_main
layout activity_search
layout activity_shop
layout activity_test
layout activity_user_center
layout fragment_application
layout fragment_game
layout fragment_login
layout fragment_main_content
layout fragment_shop
layout item_app_category
layout item_featured_game
layout item_featured_movie
layout item_game_category
layout item_hot_game
layout item_hot_product
layout item_navigation
layout item_product_category
layout item_recommended_app
mipmap ic_launcher
string app_name
string browse_title
string buy_1
string buy_2
string dismiss_error
string error_fragment
string error_fragment_message
string grid_view
string movie
string personal_settings
string related_movies
string rent_1
string rent_2
string watch_trailer_1
string watch_trailer_2
style Theme.MyApplicationTV
