package com.example.myapplicationtv.network

import com.example.myapplicationtv.model.*
import retrofit2.Response
import retrofit2.http.*

/**
 * API服务接口
 */
interface ApiService {
    
    // 电影相关接口
    @GET("movies/list")
    suspend fun getMovies(
        @Query("page") page: Int = 1,
        @Query("size") size: Int = 20,
        @Query("keyword") keyword: String? = null,
        @Query("category") category: String? = null,
        @Query("sort") sort: String = "latest"
    ): Response<ApiResponse<PageResult<MovieResponse>>>
    
    @GET("movies/{id}")
    suspend fun getMovieById(@Path("id") id: String): Response<ApiResponse<MovieResponse>>
    
    @GET("movies/recommended")
    suspend fun getRecommendedMovies(@Query("limit") limit: Int = 10): Response<ApiResponse<List<MovieResponse>>>
    
    @GET("movies/hot")
    suspend fun getHotMovies(@Query("limit") limit: Int = 10): Response<ApiResponse<List<MovieResponse>>>
    
    // 应用相关接口
    @GET("apps/list")
    suspend fun getApps(
        @Query("page") page: Int = 1,
        @Query("size") size: Int = 20,
        @Query("keyword") keyword: String? = null,
        @Query("category") category: String? = null,
        @Query("sort") sort: String = "latest"
    ): Response<ApiResponse<PageResult<AppResponse>>>
    
    @GET("apps/{id}")
    suspend fun getAppById(@Path("id") id: String): Response<ApiResponse<AppResponse>>
    
    @GET("apps/recommended")
    suspend fun getRecommendedApps(@Query("limit") limit: Int = 10): Response<ApiResponse<List<AppResponse>>>
    
    @GET("apps/hot")
    suspend fun getHotApps(@Query("limit") limit: Int = 10): Response<ApiResponse<List<AppResponse>>>
    
    // 游戏相关接口
    @GET("games/list")
    suspend fun getGames(
        @Query("page") page: Int = 1,
        @Query("size") size: Int = 20,
        @Query("keyword") keyword: String? = null,
        @Query("category") category: String? = null,
        @Query("sort") sort: String = "latest"
    ): Response<ApiResponse<PageResult<GameResponse>>>
    
    @GET("games/{id}")
    suspend fun getGameById(@Path("id") id: String): Response<ApiResponse<GameResponse>>
    
    @GET("games/recommended")
    suspend fun getRecommendedGames(@Query("limit") limit: Int = 10): Response<ApiResponse<List<GameResponse>>>
    
    @GET("games/featured")
    suspend fun getFeaturedGames(@Query("limit") limit: Int = 10): Response<ApiResponse<List<GameResponse>>>
    
    @GET("games/hot")
    suspend fun getHotGames(@Query("limit") limit: Int = 10): Response<ApiResponse<List<GameResponse>>>
    
    // 商品相关接口
    @GET("products/list")
    suspend fun getProducts(
        @Query("page") page: Int = 1,
        @Query("size") size: Int = 20,
        @Query("keyword") keyword: String? = null,
        @Query("category") category: String? = null,
        @Query("brand") brand: String? = null,
        @Query("sort") sort: String = "latest"
    ): Response<ApiResponse<PageResult<ProductResponse>>>
    
    @GET("products/{id}")
    suspend fun getProductById(@Path("id") id: String): Response<ApiResponse<ProductResponse>>
    
    @GET("products/recommended")
    suspend fun getRecommendedProducts(@Query("limit") limit: Int = 10): Response<ApiResponse<List<ProductResponse>>>
    
    @GET("products/hot")
    suspend fun getHotProducts(@Query("limit") limit: Int = 10): Response<ApiResponse<List<ProductResponse>>>
    
    // 分类相关接口
    @GET("categories")
    suspend fun getCategories(@Query("type") type: String? = null): Response<ApiResponse<List<CategoryResponse>>>
    
    // 用户认证接口
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): Response<ApiResponse<LoginResponse>>
    
    @POST("auth/register")
    suspend fun register(@Body request: RegisterRequest): Response<ApiResponse<UserResponse>>
    
    // 用户收藏接口
    @GET("user/favorites")
    suspend fun getUserFavorites(@Query("page") page: Int = 1): Response<ApiResponse<PageResult<FavoriteResponse>>>
    
    @POST("user/favorites")
    suspend fun addToFavorites(@Body request: FavoriteRequest): Response<ApiResponse<String>>
    
    @DELETE("user/favorites/{id}")
    suspend fun removeFromFavorites(@Path("id") id: String): Response<ApiResponse<String>>
    
    // 用户历史记录接口
    @GET("user/history")
    suspend fun getUserHistory(@Query("page") page: Int = 1): Response<ApiResponse<PageResult<HistoryResponse>>>
    
    @POST("user/history")
    suspend fun addToHistory(@Body request: HistoryRequest): Response<ApiResponse<String>>
    
    // 用户评分接口
    @POST("user/rating")
    suspend fun rateContent(@Body request: RatingRequest): Response<ApiResponse<String>>
}
