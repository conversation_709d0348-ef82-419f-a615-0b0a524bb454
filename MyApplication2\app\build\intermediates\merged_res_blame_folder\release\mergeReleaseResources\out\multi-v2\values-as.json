{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-as/values-as.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,206,309,417,522,626,726,5251", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "201,304,412,517,621,721,850,5347"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,415,509,633,745,844,938,1064,1189,1295,1421,1572,1691,1818,1921,2013,2145,2238,2349,2462,2572,2671,2787,2909,3024,3138,3248,3358,3478,3597,3713,3828,3915,3999,4105,4244,4401", "endColumns": "106,100,101,93,123,111,98,93,125,124,105,125,150,118,126,102,91,131,92,110,112,109,98,115,121,114,113,109,109,119,118,115,114,86,83,105,138,156,99", "endOffsets": "207,308,410,504,628,740,839,933,1059,1184,1290,1416,1567,1686,1813,1916,2008,2140,2233,2344,2457,2567,2666,2782,2904,3019,3133,3243,3353,3473,3592,3708,3823,3910,3994,4100,4239,4396,4496"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "855,962,1063,1165,1259,1383,1495,1594,1688,1814,1939,2045,2171,2322,2441,2568,2671,2763,2895,2988,3099,3212,3322,3421,3537,3659,3774,3888,3998,4108,4228,4347,4463,4578,4665,4749,4855,4994,5151", "endColumns": "106,100,101,93,123,111,98,93,125,124,105,125,150,118,126,102,91,131,92,110,112,109,98,115,121,114,113,109,109,119,118,115,114,86,83,105,138,156,99", "endOffsets": "957,1058,1160,1254,1378,1490,1589,1683,1809,1934,2040,2166,2317,2436,2563,2666,2758,2890,2983,3094,3207,3317,3416,3532,3654,3769,3883,3993,4103,4223,4342,4458,4573,4660,4744,4850,4989,5146,5246"}}]}]}