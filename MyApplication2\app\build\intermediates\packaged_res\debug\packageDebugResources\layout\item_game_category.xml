<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="16dp"
    android:layout_marginEnd="16dp"
    android:background="@drawable/category_item_background"
    android:focusable="true"
    android:clickable="true">

    <ImageView
        android:id="@+id/iv_category_icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@drawable/ic_games"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/tv_category_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="休闲游戏"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:gravity="center" />

</LinearLayout>
