{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-is/values-is.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,501,622,734,831,929,1055,1180,1284,1404,1551,1674,1796,1904,1993,2114,2204,2309,2417,2522,2623,2741,2851,2964,3076,3178,3284,3396,3511,3619,3730,3817,3900,4004,4140,4297", "endColumns": "106,100,94,92,120,111,96,97,125,124,103,119,146,122,121,107,88,120,89,104,107,104,100,117,109,112,111,101,105,111,114,107,110,86,82,103,135,156,88", "endOffsets": "207,308,403,496,617,729,826,924,1050,1175,1279,1399,1546,1669,1791,1899,1988,2109,2199,2304,2412,2517,2618,2736,2846,2959,3071,3173,3279,3391,3506,3614,3725,3812,3895,3999,4135,4292,4381"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "822,929,1030,1125,1218,1339,1451,1548,1646,1772,1897,2001,2121,2268,2391,2513,2621,2710,2831,2921,3026,3134,3239,3340,3458,3568,3681,3793,3895,4001,4113,4228,4336,4447,4534,4617,4721,4857,5014", "endColumns": "106,100,94,92,120,111,96,97,125,124,103,119,146,122,121,107,88,120,89,104,107,104,100,117,109,112,111,101,105,111,114,107,110,86,82,103,135,156,88", "endOffsets": "924,1025,1120,1213,1334,1446,1543,1641,1767,1892,1996,2116,2263,2386,2508,2616,2705,2826,2916,3021,3129,3234,3335,3453,3563,3676,3788,3890,3996,4108,4223,4331,4442,4529,4612,4716,4852,5009,5098"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,307,404,504,607,711,5103", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "195,302,399,499,602,706,817,5199"}}]}]}