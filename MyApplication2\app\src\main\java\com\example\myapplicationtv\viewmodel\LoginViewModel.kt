package com.example.myapplicationtv.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplicationtv.model.UserResponse
import com.example.myapplicationtv.repository.UserRepository
import com.example.myapplicationtv.utils.UserManager
import kotlinx.coroutines.launch

/**
 * 登录ViewModel
 */
class LoginViewModel : ViewModel() {
    
    private val userRepository = UserRepository()
    
    // 登录结果
    private val _loginResult = MutableLiveData<Result<UserResponse>>()
    val loginResult: LiveData<Result<UserResponse>> = _loginResult
    
    // 注册结果
    private val _registerResult = MutableLiveData<Result<UserResponse>>()
    val registerResult: LiveData<Result<UserResponse>> = _registerResult
    
    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    // 错误信息
    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    
    /**
     * 用户登录
     */
    fun login(username: String, password: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = ""
                
                userRepository.login(username, password).fold(
                    onSuccess = { user ->
                        // 保存用户信息和token
                        UserManager.saveUser(user)
                        _loginResult.value = Result.success(user)
                    },
                    onFailure = { e ->
                        _error.value = e.message ?: "登录失败"
                        _loginResult.value = Result.failure(e)
                    }
                )
            } catch (e: Exception) {
                _error.value = "登录失败: ${e.message}"
                _loginResult.value = Result.failure(e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 用户注册
     */
    fun register(username: String, password: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = ""
                
                userRepository.register(username, password).fold(
                    onSuccess = { user ->
                        // 注册成功后自动登录
                        UserManager.saveUser(user)
                        _registerResult.value = Result.success(user)
                        _loginResult.value = Result.success(user)
                    },
                    onFailure = { e ->
                        _error.value = e.message ?: "注册失败"
                        _registerResult.value = Result.failure(e)
                    }
                )
            } catch (e: Exception) {
                _error.value = "注册失败: ${e.message}"
                _registerResult.value = Result.failure(e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 检查登录状态
     */
    fun checkLoginStatus(): Boolean {
        return UserManager.isLoggedIn()
    }
    
    /**
     * 退出登录
     */
    fun logout() {
        UserManager.logout()
    }
}
