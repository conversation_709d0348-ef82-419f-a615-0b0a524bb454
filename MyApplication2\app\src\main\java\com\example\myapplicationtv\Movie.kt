package com.example.myapplicationtv

import com.example.myapplicationtv.model.*
import java.io.Serializable
import java.math.BigDecimal

/**
 * Movie class represents video entity with title, description, image thumbs and video url.
 */
data class Movie(
    var id: String = "",
    var title: String? = null,
    var description: String? = null,
    var backgroundImageUrl: String? = null,
    var cardImageUrl: String? = null,
    var videoUrl: String? = null,
    var studio: String? = null,
    var category: String? = null,
    var rating: BigDecimal? = null,
    var year: Int? = null,
    var duration: Int? = null,
    var director: String? = null,
    var actors: String? = null
) : Serializable {

    override fun toString(): String {
        return "Movie{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", videoUrl='" + videoUrl + '\'' +
                ", backgroundImageUrl='" + backgroundImageUrl + '\'' +
                ", cardImageUrl='" + cardImageUrl + '\'' +
                '}'
    }

    companion object {
        internal const val serialVersionUID = 727566175075960653L

        /**
         * 从API响应转换为Movie对象
         */
        fun fromMovieResponse(response: MovieResponse): Movie {
            return Movie(
                id = response.id,
                title = response.title,
                description = response.description,
                backgroundImageUrl = response.poster ?: response.cover,
                cardImageUrl = response.cover,
                videoUrl = response.playUrl,
                studio = response.area,
                category = response.category,
                rating = response.rating,
                year = response.year,
                duration = response.duration,
                director = response.director,
                actors = response.actors
            )
        }
    }
}

/**
 * 通用内容项，用于统一显示不同类型的内容
 */
data class ContentItem(
    var id: String = "",
    var title: String? = null,
    var description: String? = null,
    var backgroundImageUrl: String? = null,
    var cardImageUrl: String? = null,
    var category: String? = null,
    var rating: BigDecimal? = null,
    var type: ContentType,
    var originalData: Any? = null
) : Serializable {

    companion object {
        fun fromMovieResponse(response: MovieResponse): ContentItem {
            return ContentItem(
                id = response.id,
                title = response.title,
                description = response.description,
                backgroundImageUrl = response.poster ?: response.cover,
                cardImageUrl = response.cover,
                category = response.category,
                rating = response.rating,
                type = ContentType.MOVIE,
                originalData = response
            )
        }

        fun fromAppResponse(response: AppResponse): ContentItem {
            return ContentItem(
                id = response.id,
                title = response.name,
                description = response.description,
                backgroundImageUrl = response.icon,
                cardImageUrl = response.icon,
                category = response.category,
                rating = response.rating,
                type = ContentType.APP,
                originalData = response
            )
        }

        fun fromGameResponse(response: GameResponse): ContentItem {
            return ContentItem(
                id = response.id,
                title = response.name,
                description = response.description,
                backgroundImageUrl = response.cover ?: response.icon,
                cardImageUrl = response.cover ?: response.icon,
                category = response.category,
                rating = response.rating,
                type = ContentType.GAME,
                originalData = response
            )
        }

        fun fromProductResponse(response: ProductResponse): ContentItem {
            return ContentItem(
                id = response.id,
                title = response.name,
                description = response.description,
                backgroundImageUrl = response.image,
                cardImageUrl = response.image,
                category = response.category,
                rating = response.rating,
                type = ContentType.PRODUCT,
                originalData = response
            )
        }
    }
}