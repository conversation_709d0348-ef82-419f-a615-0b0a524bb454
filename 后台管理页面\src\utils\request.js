import axios from 'axios'
import { ElMessage } from 'element-plus'
import { getToken, removeToken } from './auth'

// 创建axios实例
const request = axios.create({
  baseURL: 'http://localhost:8080/api',
  timeout: 5000  // 减少超时时间，快速切换到模拟数据
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bear<PERSON> ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const { data } = response
    if (data && data.success) {
      return data
    } else {
      // 对于模拟环境，直接返回响应数据
      return response.data || response
    }
  },
  error => {
    console.log('请求错误详情:', error)

    // 网络连接错误（后端未启动）
    if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK' || error.code === 'ERR_FAILED') {
      console.warn('后端服务未启动，使用模拟数据')

      // 根据请求URL返回相应的模拟数据
      const url = error.config?.url || ''
      if (url.includes('/games')) {
        return Promise.resolve({
          success: true,
          data: {
            list: [
              {
                id: 1,
                name: '王者荣耀',
                description: '5v5英雄公平对战手游',
                category: 'MOBA',
                downloadUrl: '#',
                imageUrl: '/images/game1.jpg',
                rating: 4.8,
                downloadCount: 50000000,
                createdAt: '2024-01-01T00:00:00Z',
                status: 'ACTIVE'
              },
              {
                id: 2,
                name: '和平精英',
                description: '战术竞技手游',
                category: 'SHOOTER',
                downloadUrl: '#',
                imageUrl: '/images/game2.jpg',
                rating: 4.6,
                downloadCount: 30000000,
                createdAt: '2024-01-02T00:00:00Z',
                status: 'ACTIVE'
              },
              {
                id: 3,
                name: '原神',
                description: '开放世界冒险游戏',
                category: 'RPG',
                downloadUrl: '#',
                imageUrl: '/images/game3.jpg',
                rating: 4.9,
                downloadCount: 20000000,
                createdAt: '2024-01-03T00:00:00Z',
                status: 'ACTIVE'
              }
            ],
            total: 3
          }
        })
      } else if (url.includes('/categories')) {
        return Promise.resolve({
          success: true,
          data: [
            { id: 1, name: 'MOBA游戏', type: 'GAME', status: 'ACTIVE' },
            { id: 2, name: '射击游戏', type: 'GAME', status: 'ACTIVE' },
            { id: 3, name: 'RPG游戏', type: 'GAME', status: 'ACTIVE' },
            { id: 4, name: '策略游戏', type: 'GAME', status: 'ACTIVE' },
            { id: 5, name: '电子产品', type: 'PRODUCT', status: 'ACTIVE' },
            { id: 6, name: '数码配件', type: 'PRODUCT', status: 'ACTIVE' }
          ]
        })
      }

      // 默认返回空数据
      return Promise.resolve({
        success: true,
        data: { content: [], totalElements: 0, totalPages: 0, number: 0, size: 20 }
      })
    }

    // 401 认证错误
    if (error.response?.status === 401) {
      console.warn('认证失败，使用模拟数据')

      // 对于401错误，也返回模拟数据而不是跳转登录页
      const url = error.config?.url || ''
      if (url.includes('/categories')) {
        return Promise.resolve({
          success: true,
          data: [
            { id: 1, name: 'MOBA游戏', type: 'GAME', status: 'ACTIVE' },
            { id: 2, name: '射击游戏', type: 'GAME', status: 'ACTIVE' },
            { id: 3, name: 'RPG游戏', type: 'GAME', status: 'ACTIVE' },
            { id: 4, name: '策略游戏', type: 'GAME', status: 'ACTIVE' },
            { id: 5, name: '电子产品', type: 'PRODUCT', status: 'ACTIVE' },
            { id: 6, name: '数码配件', type: 'PRODUCT', status: 'ACTIVE' }
          ]
        })
      } else if (url.includes('/games')) {
        return Promise.resolve({
          success: true,
          data: {
            list: [
              {
                id: 1,
                name: '王者荣耀',
                description: '5v5英雄公平对战手游',
                category: 'MOBA',
                downloadUrl: '#',
                imageUrl: '/images/game1.jpg',
                rating: 4.8,
                downloadCount: 50000000,
                createdAt: '2024-01-01T00:00:00Z',
                status: 'ACTIVE'
              },
              {
                id: 2,
                name: '和平精英',
                description: '战术竞技手游',
                category: 'SHOOTER',
                downloadUrl: '#',
                imageUrl: '/images/game2.jpg',
                rating: 4.6,
                downloadCount: 30000000,
                createdAt: '2024-01-02T00:00:00Z',
                status: 'ACTIVE'
              }
            ],
            total: 2
          }
        })
      }

      // 默认返回空数据
      return Promise.resolve({
        success: true,
        data: { content: [], totalElements: 0, totalPages: 0, number: 0, size: 20 }
      })
    }

    // 其他错误
    ElMessage.error(error.message || '网络错误')
    return Promise.reject(error)
  }
)

export default request
