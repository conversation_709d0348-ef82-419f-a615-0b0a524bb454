import request from '@/utils/request'

// 获取用户列表
export function getUsers(params) {
  return request({
    url: '/users',
    method: 'get',
    params
  })
}

// 获取单个用户
export function getUser(id) {
  return request({
    url: `/users/${id}`,
    method: 'get'
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: '/users',
    method: 'post',
    data
  })
}

// 更新用户
export function updateUser(data) {
  return request({
    url: `/users/${data.id}`,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/users/${id}`,
    method: 'delete'
  })
}

// 更新用户状态
export function updateUserStatus(id, status) {
  return request({
    url: `/users/${id}/status`,
    method: 'patch',
    data: { status }
  })
}

// 重置用户密码
export function resetUserPassword(id, password) {
  return request({
    url: `/users/${id}/password`,
    method: 'patch',
    data: { password }
  })
}

// 获取用户统计
export function getUserStatistics() {
  return request({
    url: '/users/statistics',
    method: 'get'
  })
}

// 获取用户收藏列表
export function getFavorites(params) {
  return request({
    url: '/users/favorites',
    method: 'get',
    params
  })
}

// 删除用户收藏
export function deleteFavorite(id) {
  return request({
    url: `/users/favorites/${id}`,
    method: 'delete'
  })
}

// 获取用户观看历史
export function getHistory(params) {
  return request({
    url: '/users/history',
    method: 'get',
    params
  })
}

// 删除观看历史
export function deleteHistory(id) {
  return request({
    url: `/users/history/${id}`,
    method: 'delete'
  })
}

// 批量删除收藏
export function batchDeleteFavorites(ids) {
  return request({
    url: '/users/favorites/batch',
    method: 'delete',
    data: { ids }
  })
}

// 批量删除历史
export function batchDeleteHistory(ids) {
  return request({
    url: '/users/history/batch',
    method: 'delete',
    data: { ids }
  })
}

// 批量操作用户
export function batchUpdateUsers(data) {
  return request({
    url: '/users/batch',
    method: 'patch',
    data
  })
}
