<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:background="@color/custom_tv_background"
    tools:context=".MainActivity"
    tools:deviceIds="tv">

    <!-- 主内容区域 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:padding="32dp">

        <!-- 顶部状态栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingBottom="24dp">

            <!-- 时间日期信息 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="22:53"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="星期六 5月8日"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:layout_marginStart="16dp" />

                <TextView
                    android:id="@+id/tv_weather"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="晴朗 28°C"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:layout_marginStart="16dp" />

            </LinearLayout>

            <!-- 搜索按钮 -->
            <ImageButton
                android:id="@+id/btn_search"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="@drawable/search_button_background"
                android:src="@drawable/ic_search"
                android:contentDescription="搜索"
                android:focusable="true"
                android:clickable="true" />

        </LinearLayout>

        <!-- 主内容区域 -->
        <FrameLayout
            android:id="@+id/main_content_fragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

    <!-- 右侧导航菜单 -->
    <LinearLayout
        android:layout_width="120dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/navigation_background"
        android:padding="16dp"
        android:gravity="center_horizontal">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_navigation"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="32dp" />

    </LinearLayout>

</LinearLayout>