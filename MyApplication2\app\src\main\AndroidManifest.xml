<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />

    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="true" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/Theme.MyApplicationTV"
        android:usesCleartextTraffic="true"
        android:name=".TVApplication">
        <activity
            android:name=".MainActivity"
            android:banner="@drawable/app_icon_your_company"
            android:exported="true"
            android:icon="@drawable/app_icon_your_company"
            android:label="@string/app_name"
            android:logo="@drawable/app_icon_your_company"
            android:screenOrientation="landscape">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".DetailsActivity"
            android:exported="false" />
        <activity
            android:name=".PlaybackActivity"
            android:exported="false" />
        <activity
            android:name=".BrowseErrorActivity"
            android:exported="false" />
        <activity
            android:name=".SearchActivity"
            android:exported="false" />
        <activity
            android:name=".LoginActivity"
            android:exported="false" />
        <activity
            android:name=".UserCenterActivity"
            android:exported="false" />
        <activity
            android:name=".TestActivity"
            android:exported="false" />
        <activity
            android:name=".GameActivity"
            android:exported="false" />
        <activity
            android:name=".ShopActivity"
            android:exported="false" />
    </application>

</manifest>