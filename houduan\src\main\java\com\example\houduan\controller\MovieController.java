package com.example.houduan.controller;

import com.example.houduan.common.PageResult;
import com.example.houduan.common.Result;
import com.example.houduan.dto.MovieQueryRequest;
import com.example.houduan.dto.MovieRequest;
import com.example.houduan.dto.MovieResponse;
import com.example.houduan.service.MovieService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 电影控制器
 */
@RestController
@RequestMapping("/movies")
@RequiredArgsConstructor
public class MovieController {

    private final MovieService movieService;

    /**
     * 分页查询电影列表
     */
    @GetMapping("/list")
    public Result<PageResult<MovieResponse>> getMovieList(MovieQueryRequest request) {
        try {
            PageResult<MovieResponse> result = movieService.getMovieList(request);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("查询电影列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取电影详情
     */
    @GetMapping("/{id}")
    public Result<MovieResponse> getMovieById(@PathVariable String id) {
        try {
            MovieResponse movie = movieService.getMovieById(id);
            return Result.success(movie);
        } catch (Exception e) {
            return Result.error("获取电影详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取推荐电影
     */
    @GetMapping("/recommended")
    public Result<List<MovieResponse>> getRecommendedMovies(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<MovieResponse> movies = movieService.getRecommendedMovies(limit);
            return Result.success(movies);
        } catch (Exception e) {
            return Result.error("获取推荐电影失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门电影
     */
    @GetMapping("/hot")
    public Result<List<MovieResponse>> getHotMovies(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<MovieResponse> movies = movieService.getHotMovies(limit);
            return Result.success(movies);
        } catch (Exception e) {
            return Result.error("获取热门电影失败: " + e.getMessage());
        }
    }

    /**
     * 获取最新电影
     */
    @GetMapping("/new")
    public Result<List<MovieResponse>> getNewMovies(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<MovieResponse> movies = movieService.getNewMovies(limit);
            return Result.success(movies);
        } catch (Exception e) {
            return Result.error("获取最新电影失败: " + e.getMessage());
        }
    }

    /**
     * 创建电影 (需要管理员权限)
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public Result<MovieResponse> createMovie(@Valid @RequestBody MovieRequest request) {
        try {
            MovieResponse movie = movieService.createMovie(request);
            return Result.success("创建电影成功", movie);
        } catch (Exception e) {
            return Result.error("创建电影失败: " + e.getMessage());
        }
    }

    /**
     * 更新电影 (需要管理员权限)
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<MovieResponse> updateMovie(@PathVariable String id, @Valid @RequestBody MovieRequest request) {
        try {
            MovieResponse movie = movieService.updateMovie(id, request);
            return Result.success("更新电影成功", movie);
        } catch (Exception e) {
            return Result.error("更新电影失败: " + e.getMessage());
        }
    }

    /**
     * 删除电影 (需要管理员权限)
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<String> deleteMovie(@PathVariable String id) {
        try {
            movieService.deleteMovie(id);
            return Result.success("删除电影成功");
        } catch (Exception e) {
            return Result.error("删除电影失败: " + e.getMessage());
        }
    }
}
