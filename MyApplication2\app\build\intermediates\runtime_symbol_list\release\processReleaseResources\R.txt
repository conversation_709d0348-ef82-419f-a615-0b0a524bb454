int anim fragment_fast_out_extra_slow_in 0x7f010000
int anim lb_decelerator_2 0x7f010001
int anim lb_decelerator_4 0x7f010002
int animator fragment_close_enter 0x7f020000
int animator fragment_close_exit 0x7f020001
int animator fragment_fade_enter 0x7f020002
int animator fragment_fade_exit 0x7f020003
int animator fragment_open_enter 0x7f020004
int animator fragment_open_exit 0x7f020005
int animator lb_guidedactions_item_pressed 0x7f020006
int animator lb_guidedactions_item_unpressed 0x7f020007
int animator lb_guidedstep_slide_down 0x7f020008
int animator lb_guidedstep_slide_up 0x7f020009
int animator lb_onboarding_description_enter 0x7f02000a
int animator lb_onboarding_logo_enter 0x7f02000b
int animator lb_onboarding_logo_exit 0x7f02000c
int animator lb_onboarding_page_indicator_enter 0x7f02000d
int animator lb_onboarding_page_indicator_fade_in 0x7f02000e
int animator lb_onboarding_page_indicator_fade_out 0x7f02000f
int animator lb_onboarding_start_button_fade_in 0x7f020010
int animator lb_onboarding_start_button_fade_out 0x7f020011
int animator lb_onboarding_title_enter 0x7f020012
int animator lb_playback_bg_fade_in 0x7f020013
int animator lb_playback_bg_fade_out 0x7f020014
int animator lb_playback_controls_fade_in 0x7f020015
int animator lb_playback_controls_fade_out 0x7f020016
int animator lb_playback_description_fade_in 0x7f020017
int animator lb_playback_description_fade_out 0x7f020018
int animator lb_playback_rows_fade_in 0x7f020019
int animator lb_playback_rows_fade_out 0x7f02001a
int attr activatedAnimationDuration 0x7f030000
int attr alpha 0x7f030001
int attr arrowBgColor 0x7f030002
int attr arrowColor 0x7f030003
int attr arrowRadius 0x7f030004
int attr baseCardViewStyle 0x7f030005
int attr browsePaddingBottom 0x7f030006
int attr browsePaddingEnd 0x7f030007
int attr browsePaddingStart 0x7f030008
int attr browsePaddingTop 0x7f030009
int attr browseRowsFadingEdgeLength 0x7f03000a
int attr browseRowsMarginStart 0x7f03000b
int attr browseRowsMarginTop 0x7f03000c
int attr browseTitleIconStyle 0x7f03000d
int attr browseTitleTextStyle 0x7f03000e
int attr browseTitleViewLayout 0x7f03000f
int attr browseTitleViewStyle 0x7f030010
int attr cardBackground 0x7f030011
int attr cardForeground 0x7f030012
int attr cardType 0x7f030013
int attr closed_captioning 0x7f030014
int attr columnWidth 0x7f030015
int attr coordinatorLayoutStyle 0x7f030016
int attr datePickerFormat 0x7f030017
int attr defaultBrandColor 0x7f030018
int attr defaultBrandColorDark 0x7f030019
int attr defaultSearchBrightColor 0x7f03001a
int attr defaultSearchColor 0x7f03001b
int attr defaultSearchIcon 0x7f03001c
int attr defaultSearchIconColor 0x7f03001d
int attr defaultSectionHeaderColor 0x7f03001e
int attr detailsActionButtonStyle 0x7f03001f
int attr detailsDescriptionBodyStyle 0x7f030020
int attr detailsDescriptionSubtitleStyle 0x7f030021
int attr detailsDescriptionTitleStyle 0x7f030022
int attr dotBgColor 0x7f030023
int attr dotToArrowGap 0x7f030024
int attr dotToDotGap 0x7f030025
int attr errorMessageStyle 0x7f030026
int attr extraVisibility 0x7f030027
int attr fastScrollEnabled 0x7f030028
int attr fastScrollHorizontalThumbDrawable 0x7f030029
int attr fastScrollHorizontalTrackDrawable 0x7f03002a
int attr fastScrollVerticalThumbDrawable 0x7f03002b
int attr fastScrollVerticalTrackDrawable 0x7f03002c
int attr fast_forward 0x7f03002d
int attr focusOutEnd 0x7f03002e
int attr focusOutFront 0x7f03002f
int attr focusOutSideEnd 0x7f030030
int attr focusOutSideStart 0x7f030031
int attr font 0x7f030032
int attr fontProviderAuthority 0x7f030033
int attr fontProviderCerts 0x7f030034
int attr fontProviderFetchStrategy 0x7f030035
int attr fontProviderFetchTimeout 0x7f030036
int attr fontProviderPackage 0x7f030037
int attr fontProviderQuery 0x7f030038
int attr fontProviderSystemFontFamily 0x7f030039
int attr fontStyle 0x7f03003a
int attr fontVariationSettings 0x7f03003b
int attr fontWeight 0x7f03003c
int attr guidanceBreadcrumbStyle 0x7f03003d
int attr guidanceContainerStyle 0x7f03003e
int attr guidanceDescriptionStyle 0x7f03003f
int attr guidanceEntryAnimation 0x7f030040
int attr guidanceIconStyle 0x7f030041
int attr guidanceTitleStyle 0x7f030042
int attr guidedActionCheckedAnimation 0x7f030043
int attr guidedActionContentWidth 0x7f030044
int attr guidedActionContentWidthNoIcon 0x7f030045
int attr guidedActionContentWidthWeight 0x7f030046
int attr guidedActionContentWidthWeightTwoPanels 0x7f030047
int attr guidedActionDescriptionMinLines 0x7f030048
int attr guidedActionDisabledChevronAlpha 0x7f030049
int attr guidedActionEnabledChevronAlpha 0x7f03004a
int attr guidedActionItemCheckmarkStyle 0x7f03004b
int attr guidedActionItemChevronStyle 0x7f03004c
int attr guidedActionItemContainerStyle 0x7f03004d
int attr guidedActionItemContentStyle 0x7f03004e
int attr guidedActionItemDescriptionStyle 0x7f03004f
int attr guidedActionItemIconStyle 0x7f030050
int attr guidedActionItemTitleStyle 0x7f030051
int attr guidedActionPressedAnimation 0x7f030052
int attr guidedActionTitleMaxLines 0x7f030053
int attr guidedActionTitleMinLines 0x7f030054
int attr guidedActionUncheckedAnimation 0x7f030055
int attr guidedActionUnpressedAnimation 0x7f030056
int attr guidedActionVerticalPadding 0x7f030057
int attr guidedActionsBackground 0x7f030058
int attr guidedActionsBackgroundDark 0x7f030059
int attr guidedActionsContainerStyle 0x7f03005a
int attr guidedActionsElevation 0x7f03005b
int attr guidedActionsEntryAnimation 0x7f03005c
int attr guidedActionsListStyle 0x7f03005d
int attr guidedActionsSelectorDrawable 0x7f03005e
int attr guidedActionsSelectorHideAnimation 0x7f03005f
int attr guidedActionsSelectorShowAnimation 0x7f030060
int attr guidedActionsSelectorStyle 0x7f030061
int attr guidedButtonActionsListStyle 0x7f030062
int attr guidedButtonActionsWidthWeight 0x7f030063
int attr guidedStepBackground 0x7f030064
int attr guidedStepEntryAnimation 0x7f030065
int attr guidedStepExitAnimation 0x7f030066
int attr guidedStepHeightWeight 0x7f030067
int attr guidedStepImeAppearingAnimation 0x7f030068
int attr guidedStepImeDisappearingAnimation 0x7f030069
int attr guidedStepKeyline 0x7f03006a
int attr guidedStepReentryAnimation 0x7f03006b
int attr guidedStepReturnAnimation 0x7f03006c
int attr guidedStepTheme 0x7f03006d
int attr guidedStepThemeFlag 0x7f03006e
int attr guidedSubActionsListStyle 0x7f03006f
int attr headerStyle 0x7f030070
int attr headersVerticalGridStyle 0x7f030071
int attr high_quality 0x7f030072
int attr horizontalMargin 0x7f030073
int attr imageCardViewBadgeStyle 0x7f030074
int attr imageCardViewContentStyle 0x7f030075
int attr imageCardViewImageStyle 0x7f030076
int attr imageCardViewInfoAreaStyle 0x7f030077
int attr imageCardViewStyle 0x7f030078
int attr imageCardViewTitleStyle 0x7f030079
int attr infoAreaBackground 0x7f03007a
int attr infoVisibility 0x7f03007b
int attr is24HourFormat 0x7f03007c
int attr itemsVerticalGridStyle 0x7f03007d
int attr keylines 0x7f03007e
int attr lStar 0x7f03007f
int attr layoutManager 0x7f030080
int attr layout_anchor 0x7f030081
int attr layout_anchorGravity 0x7f030082
int attr layout_behavior 0x7f030083
int attr layout_dodgeInsetEdges 0x7f030084
int attr layout_insetEdge 0x7f030085
int attr layout_keyline 0x7f030086
int attr layout_viewType 0x7f030087
int attr lbDotRadius 0x7f030088
int attr lbImageCardViewType 0x7f030089
int attr lb_slideEdge 0x7f03008a
int attr maintainLineSpacing 0x7f03008b
int attr nestedScrollViewStyle 0x7f03008c
int attr numberOfColumns 0x7f03008d
int attr numberOfRows 0x7f03008e
int attr onboardingDescriptionStyle 0x7f03008f
int attr onboardingHeaderStyle 0x7f030090
int attr onboardingLogoStyle 0x7f030091
int attr onboardingMainIconStyle 0x7f030092
int attr onboardingNavigatorContainerStyle 0x7f030093
int attr onboardingPageIndicatorStyle 0x7f030094
int attr onboardingStartButtonStyle 0x7f030095
int attr onboardingTheme 0x7f030096
int attr onboardingTitleStyle 0x7f030097
int attr overlayDimActiveLevel 0x7f030098
int attr overlayDimDimmedLevel 0x7f030099
int attr overlayDimMaskColor 0x7f03009a
int attr pause 0x7f03009b
int attr picture_in_picture 0x7f03009c
int attr play 0x7f03009d
int attr playbackControlButtonLabelStyle 0x7f03009e
int attr playbackControlsActionIcons 0x7f03009f
int attr playbackControlsAutoHideTickleTimeout 0x7f0300a0
int attr playbackControlsAutoHideTimeout 0x7f0300a1
int attr playbackControlsButtonStyle 0x7f0300a2
int attr playbackControlsIconHighlightColor 0x7f0300a3
int attr playbackControlsTimeStyle 0x7f0300a4
int attr playbackMediaItemDetailsStyle 0x7f0300a5
int attr playbackMediaItemDurationStyle 0x7f0300a6
int attr playbackMediaItemNameStyle 0x7f0300a7
int attr playbackMediaItemNumberStyle 0x7f0300a8
int attr playbackMediaItemNumberViewFlipperLayout 0x7f0300a9
int attr playbackMediaItemNumberViewFlipperStyle 0x7f0300aa
int attr playbackMediaItemPaddingStart 0x7f0300ab
int attr playbackMediaItemRowStyle 0x7f0300ac
int attr playbackMediaItemSeparatorStyle 0x7f0300ad
int attr playbackMediaListHeaderStyle 0x7f0300ae
int attr playbackMediaListHeaderTitleStyle 0x7f0300af
int attr playbackPaddingEnd 0x7f0300b0
int attr playbackPaddingStart 0x7f0300b1
int attr playbackProgressPrimaryColor 0x7f0300b2
int attr playbackProgressSecondaryColor 0x7f0300b3
int attr queryPatterns 0x7f0300b4
int attr recyclerViewStyle 0x7f0300b5
int attr repeat 0x7f0300b6
int attr repeat_one 0x7f0300b7
int attr resizeTrigger 0x7f0300b8
int attr resizedPaddingAdjustmentBottom 0x7f0300b9
int attr resizedPaddingAdjustmentTop 0x7f0300ba
int attr resizedTextSize 0x7f0300bb
int attr reverseLayout 0x7f0300bc
int attr rewind 0x7f0300bd
int attr rowHeaderDescriptionStyle 0x7f0300be
int attr rowHeaderDockStyle 0x7f0300bf
int attr rowHeaderStyle 0x7f0300c0
int attr rowHeight 0x7f0300c1
int attr rowHorizontalGridStyle 0x7f0300c2
int attr rowHoverCardDescriptionStyle 0x7f0300c3
int attr rowHoverCardTitleStyle 0x7f0300c4
int attr rowsVerticalGridStyle 0x7f0300c5
int attr searchOrbBrightColor 0x7f0300c6
int attr searchOrbColor 0x7f0300c7
int attr searchOrbIcon 0x7f0300c8
int attr searchOrbIconColor 0x7f0300c9
int attr searchOrbViewStyle 0x7f0300ca
int attr sectionHeaderStyle 0x7f0300cb
int attr selectedAnimationDelay 0x7f0300cc
int attr selectedAnimationDuration 0x7f0300cd
int attr shortcutMatchRequired 0x7f0300ce
int attr shuffle 0x7f0300cf
int attr skip_next 0x7f0300d0
int attr skip_previous 0x7f0300d1
int attr spanCount 0x7f0300d2
int attr stackFromEnd 0x7f0300d3
int attr statusBarBackground 0x7f0300d4
int attr thumb_down 0x7f0300d5
int attr thumb_down_outline 0x7f0300d6
int attr thumb_up 0x7f0300d7
int attr thumb_up_outline 0x7f0300d8
int attr ttcIndex 0x7f0300d9
int attr useCurrentTime 0x7f0300da
int attr verticalMargin 0x7f0300db
int color androidx_core_ripple_material_light 0x7f040000
int color androidx_core_secondary_text_default_material_light 0x7f040001
int color background_gradient_end 0x7f040002
int color background_gradient_start 0x7f040003
int color call_notification_answer_color 0x7f040004
int color call_notification_decline_color 0x7f040005
int color default_background 0x7f040006
int color fastlane_background 0x7f040007
int color lb_action_text_color 0x7f040008
int color lb_background_protection 0x7f040009
int color lb_basic_card_bg_color 0x7f04000a
int color lb_basic_card_content_text_color 0x7f04000b
int color lb_basic_card_info_bg_color 0x7f04000c
int color lb_basic_card_title_text_color 0x7f04000d
int color lb_browse_header_color 0x7f04000e
int color lb_browse_header_description_color 0x7f04000f
int color lb_browse_title_color 0x7f040010
int color lb_control_button_color 0x7f040011
int color lb_control_button_text 0x7f040012
int color lb_default_brand_color 0x7f040013
int color lb_default_brand_color_dark 0x7f040014
int color lb_default_search_color 0x7f040015
int color lb_default_search_icon_color 0x7f040016
int color lb_details_description_body_color 0x7f040017
int color lb_details_description_color 0x7f040018
int color lb_details_overview_bg_color 0x7f040019
int color lb_error_background_color_opaque 0x7f04001a
int color lb_error_background_color_translucent 0x7f04001b
int color lb_error_message 0x7f04001c
int color lb_grey 0x7f04001d
int color lb_guidedactions_background 0x7f04001e
int color lb_guidedactions_background_dark 0x7f04001f
int color lb_guidedactions_item_unselected_text_color 0x7f040020
int color lb_list_item_unselected_text_color 0x7f040021
int color lb_media_background_color 0x7f040022
int color lb_page_indicator_arrow_background 0x7f040023
int color lb_page_indicator_arrow_shadow 0x7f040024
int color lb_page_indicator_dot 0x7f040025
int color lb_playback_background_progress_color 0x7f040026
int color lb_playback_controls_background_dark 0x7f040027
int color lb_playback_controls_background_light 0x7f040028
int color lb_playback_controls_time_text_color 0x7f040029
int color lb_playback_icon_highlight_no_theme 0x7f04002a
int color lb_playback_media_row_highlight_color 0x7f04002b
int color lb_playback_media_row_separator_highlight_color 0x7f04002c
int color lb_playback_now_playing_bar_color 0x7f04002d
int color lb_playback_progress_color_no_theme 0x7f04002e
int color lb_playback_progress_secondary_color_no_theme 0x7f04002f
int color lb_playback_secondary_progress_color 0x7f040030
int color lb_preference_item_category_text_color 0x7f040031
int color lb_search_bar_hint 0x7f040032
int color lb_search_bar_hint_speech_mode 0x7f040033
int color lb_search_bar_text 0x7f040034
int color lb_search_bar_text_speech_mode 0x7f040035
int color lb_search_plate_hint_text_color 0x7f040036
int color lb_speech_orb_not_recording 0x7f040037
int color lb_speech_orb_not_recording_icon 0x7f040038
int color lb_speech_orb_not_recording_pulsed 0x7f040039
int color lb_speech_orb_recording 0x7f04003a
int color lb_tv_white 0x7f04003b
int color lb_view_dim_mask_color 0x7f04003c
int color notification_action_color_filter 0x7f04003d
int color notification_icon_bg_color 0x7f04003e
int color notification_material_background_media_default_color 0x7f04003f
int color primary_text_default_material_dark 0x7f040040
int color search_opaque 0x7f040041
int color secondary_text_default_material_dark 0x7f040042
int color selected_background 0x7f040043
int dimen compat_button_inset_horizontal_material 0x7f050000
int dimen compat_button_inset_vertical_material 0x7f050001
int dimen compat_button_padding_horizontal_material 0x7f050002
int dimen compat_button_padding_vertical_material 0x7f050003
int dimen compat_control_corner_material 0x7f050004
int dimen compat_notification_large_icon_max_height 0x7f050005
int dimen compat_notification_large_icon_max_width 0x7f050006
int dimen fastscroll_default_thickness 0x7f050007
int dimen fastscroll_margin 0x7f050008
int dimen fastscroll_minimum_range 0x7f050009
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f05000a
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f05000b
int dimen item_touch_helper_swipe_escape_velocity 0x7f05000c
int dimen lb_action_1_line_height 0x7f05000d
int dimen lb_action_2_lines_height 0x7f05000e
int dimen lb_action_button_corner_radius 0x7f05000f
int dimen lb_action_icon_margin 0x7f050010
int dimen lb_action_padding_horizontal 0x7f050011
int dimen lb_action_text_size 0x7f050012
int dimen lb_action_with_icon_padding_end 0x7f050013
int dimen lb_action_with_icon_padding_start 0x7f050014
int dimen lb_basic_card_content_text_size 0x7f050015
int dimen lb_basic_card_info_badge_margin 0x7f050016
int dimen lb_basic_card_info_badge_size 0x7f050017
int dimen lb_basic_card_info_height 0x7f050018
int dimen lb_basic_card_info_height_no_content 0x7f050019
int dimen lb_basic_card_info_padding_bottom 0x7f05001a
int dimen lb_basic_card_info_padding_horizontal 0x7f05001b
int dimen lb_basic_card_info_padding_top 0x7f05001c
int dimen lb_basic_card_info_text_margin 0x7f05001d
int dimen lb_basic_card_main_height 0x7f05001e
int dimen lb_basic_card_main_width 0x7f05001f
int dimen lb_basic_card_title_text_size 0x7f050020
int dimen lb_browse_expanded_row_no_hovercard_bottom_padding 0x7f050021
int dimen lb_browse_expanded_selected_row_top_padding 0x7f050022
int dimen lb_browse_header_description_text_size 0x7f050023
int dimen lb_browse_header_fading_length 0x7f050024
int dimen lb_browse_header_height 0x7f050025
int dimen lb_browse_header_padding_end 0x7f050026
int dimen lb_browse_header_select_duration 0x7f050027
int dimen lb_browse_header_select_scale 0x7f050028
int dimen lb_browse_header_text_size 0x7f050029
int dimen lb_browse_headers_vertical_spacing 0x7f05002a
int dimen lb_browse_headers_width 0x7f05002b
int dimen lb_browse_headers_z 0x7f05002c
int dimen lb_browse_item_horizontal_spacing 0x7f05002d
int dimen lb_browse_item_vertical_spacing 0x7f05002e
int dimen lb_browse_padding_bottom 0x7f05002f
int dimen lb_browse_padding_end 0x7f050030
int dimen lb_browse_padding_start 0x7f050031
int dimen lb_browse_padding_top 0x7f050032
int dimen lb_browse_row_hovercard_description_font_size 0x7f050033
int dimen lb_browse_row_hovercard_max_width 0x7f050034
int dimen lb_browse_row_hovercard_title_font_size 0x7f050035
int dimen lb_browse_rows_fading_edge 0x7f050036
int dimen lb_browse_rows_margin_start 0x7f050037
int dimen lb_browse_rows_margin_top 0x7f050038
int dimen lb_browse_section_header_text_size 0x7f050039
int dimen lb_browse_selected_row_top_padding 0x7f05003a
int dimen lb_browse_title_height 0x7f05003b
int dimen lb_browse_title_icon_height 0x7f05003c
int dimen lb_browse_title_icon_max_width 0x7f05003d
int dimen lb_browse_title_text_size 0x7f05003e
int dimen lb_control_button_diameter 0x7f05003f
int dimen lb_control_button_height 0x7f050040
int dimen lb_control_button_secondary_diameter 0x7f050041
int dimen lb_control_button_secondary_height 0x7f050042
int dimen lb_control_button_text_size 0x7f050043
int dimen lb_control_icon_height 0x7f050044
int dimen lb_control_icon_width 0x7f050045
int dimen lb_details_cover_drawable_parallax_movement 0x7f050046
int dimen lb_details_description_body_line_spacing 0x7f050047
int dimen lb_details_description_body_text_size 0x7f050048
int dimen lb_details_description_subtitle_text_size 0x7f050049
int dimen lb_details_description_title_baseline 0x7f05004a
int dimen lb_details_description_title_line_spacing 0x7f05004b
int dimen lb_details_description_title_padding_adjust_bottom 0x7f05004c
int dimen lb_details_description_title_padding_adjust_top 0x7f05004d
int dimen lb_details_description_title_resized_text_size 0x7f05004e
int dimen lb_details_description_title_text_size 0x7f05004f
int dimen lb_details_description_under_subtitle_baseline_margin 0x7f050050
int dimen lb_details_description_under_title_baseline_margin 0x7f050051
int dimen lb_details_overview_action_items_spacing 0x7f050052
int dimen lb_details_overview_action_select_duration 0x7f050053
int dimen lb_details_overview_actions_fade_size 0x7f050054
int dimen lb_details_overview_actions_height 0x7f050055
int dimen lb_details_overview_actions_padding_end 0x7f050056
int dimen lb_details_overview_actions_padding_start 0x7f050057
int dimen lb_details_overview_description_margin_bottom 0x7f050058
int dimen lb_details_overview_description_margin_end 0x7f050059
int dimen lb_details_overview_description_margin_start 0x7f05005a
int dimen lb_details_overview_description_margin_top 0x7f05005b
int dimen lb_details_overview_height_large 0x7f05005c
int dimen lb_details_overview_height_small 0x7f05005d
int dimen lb_details_overview_image_margin_horizontal 0x7f05005e
int dimen lb_details_overview_image_margin_vertical 0x7f05005f
int dimen lb_details_overview_margin_bottom 0x7f050060
int dimen lb_details_overview_margin_end 0x7f050061
int dimen lb_details_overview_margin_start 0x7f050062
int dimen lb_details_overview_z 0x7f050063
int dimen lb_details_rows_align_top 0x7f050064
int dimen lb_details_v2_actions_height 0x7f050065
int dimen lb_details_v2_align_pos_for_actions 0x7f050066
int dimen lb_details_v2_align_pos_for_description 0x7f050067
int dimen lb_details_v2_blank_height 0x7f050068
int dimen lb_details_v2_card_height 0x7f050069
int dimen lb_details_v2_description_margin_end 0x7f05006a
int dimen lb_details_v2_description_margin_start 0x7f05006b
int dimen lb_details_v2_description_margin_top 0x7f05006c
int dimen lb_details_v2_left 0x7f05006d
int dimen lb_details_v2_logo_margin_start 0x7f05006e
int dimen lb_details_v2_logo_max_height 0x7f05006f
int dimen lb_details_v2_logo_max_width 0x7f050070
int dimen lb_error_image_max_height 0x7f050071
int dimen lb_error_message_max_width 0x7f050072
int dimen lb_error_message_text_size 0x7f050073
int dimen lb_error_under_image_baseline_margin 0x7f050074
int dimen lb_error_under_message_baseline_margin 0x7f050075
int dimen lb_guidedactions_elevation 0x7f050076
int dimen lb_guidedactions_item_bottom_padding 0x7f050077
int dimen lb_guidedactions_item_checkmark_diameter 0x7f050078
int dimen lb_guidedactions_item_delimiter_padding 0x7f050079
int dimen lb_guidedactions_item_description_font_size 0x7f05007a
int dimen lb_guidedactions_item_disabled_chevron_alpha 0x7f05007b
int dimen lb_guidedactions_item_disabled_description_text_alpha 0x7f05007c
int dimen lb_guidedactions_item_disabled_text_alpha 0x7f05007d
int dimen lb_guidedactions_item_enabled_chevron_alpha 0x7f05007e
int dimen lb_guidedactions_item_end_padding 0x7f05007f
int dimen lb_guidedactions_item_icon_height 0x7f050080
int dimen lb_guidedactions_item_icon_width 0x7f050081
int dimen lb_guidedactions_item_space_between_title_and_description 0x7f050082
int dimen lb_guidedactions_item_start_padding 0x7f050083
int dimen lb_guidedactions_item_text_width 0x7f050084
int dimen lb_guidedactions_item_text_width_no_icon 0x7f050085
int dimen lb_guidedactions_item_title_font_size 0x7f050086
int dimen lb_guidedactions_item_top_padding 0x7f050087
int dimen lb_guidedactions_item_unselected_description_text_alpha 0x7f050088
int dimen lb_guidedactions_item_unselected_text_alpha 0x7f050089
int dimen lb_guidedactions_list_padding_end 0x7f05008a
int dimen lb_guidedactions_list_padding_start 0x7f05008b
int dimen lb_guidedactions_list_vertical_spacing 0x7f05008c
int dimen lb_guidedactions_section_shadow_width 0x7f05008d
int dimen lb_guidedactions_sublist_bottom_margin 0x7f05008e
int dimen lb_guidedactions_sublist_padding_bottom 0x7f05008f
int dimen lb_guidedactions_sublist_padding_top 0x7f050090
int dimen lb_guidedactions_vertical_padding 0x7f050091
int dimen lb_guidedactions_width_weight 0x7f050092
int dimen lb_guidedactions_width_weight_two_panels 0x7f050093
int dimen lb_guidedbuttonactions_width_weight 0x7f050094
int dimen lb_guidedstep_height_weight 0x7f050095
int dimen lb_guidedstep_height_weight_translucent 0x7f050096
int dimen lb_guidedstep_keyline 0x7f050097
int dimen lb_guidedstep_slide_ime_distance 0x7f050098
int dimen lb_list_row_height 0x7f050099
int dimen lb_material_shadow_details_z 0x7f05009a
int dimen lb_material_shadow_focused_z 0x7f05009b
int dimen lb_material_shadow_normal_z 0x7f05009c
int dimen lb_onboarding_content_margin_bottom 0x7f05009d
int dimen lb_onboarding_content_margin_top 0x7f05009e
int dimen lb_onboarding_content_width 0x7f05009f
int dimen lb_onboarding_header_height 0x7f0500a0
int dimen lb_onboarding_header_margin_top 0x7f0500a1
int dimen lb_onboarding_navigation_height 0x7f0500a2
int dimen lb_onboarding_start_button_height 0x7f0500a3
int dimen lb_onboarding_start_button_margin_bottom 0x7f0500a4
int dimen lb_onboarding_start_button_translation_offset 0x7f0500a5
int dimen lb_page_indicator_arrow_gap 0x7f0500a6
int dimen lb_page_indicator_arrow_radius 0x7f0500a7
int dimen lb_page_indicator_arrow_shadow_offset 0x7f0500a8
int dimen lb_page_indicator_arrow_shadow_radius 0x7f0500a9
int dimen lb_page_indicator_dot_gap 0x7f0500aa
int dimen lb_page_indicator_dot_radius 0x7f0500ab
int dimen lb_playback_controls_card_height 0x7f0500ac
int dimen lb_playback_controls_child_margin_bigger 0x7f0500ad
int dimen lb_playback_controls_child_margin_biggest 0x7f0500ae
int dimen lb_playback_controls_child_margin_default 0x7f0500af
int dimen lb_playback_controls_margin_bottom 0x7f0500b0
int dimen lb_playback_controls_margin_end 0x7f0500b1
int dimen lb_playback_controls_margin_start 0x7f0500b2
int dimen lb_playback_controls_padding_bottom 0x7f0500b3
int dimen lb_playback_controls_time_text_size 0x7f0500b4
int dimen lb_playback_controls_z 0x7f0500b5
int dimen lb_playback_current_time_margin_start 0x7f0500b6
int dimen lb_playback_description_margin_end 0x7f0500b7
int dimen lb_playback_description_margin_start 0x7f0500b8
int dimen lb_playback_description_margin_top 0x7f0500b9
int dimen lb_playback_major_fade_translate_y 0x7f0500ba
int dimen lb_playback_media_item_radio_icon_size 0x7f0500bb
int dimen lb_playback_media_radio_width_with_padding 0x7f0500bc
int dimen lb_playback_media_row_details_selector_width 0x7f0500bd
int dimen lb_playback_media_row_horizontal_padding 0x7f0500be
int dimen lb_playback_media_row_radio_selector_width 0x7f0500bf
int dimen lb_playback_media_row_selector_round_rect_radius 0x7f0500c0
int dimen lb_playback_media_row_separator_height 0x7f0500c1
int dimen lb_playback_minor_fade_translate_y 0x7f0500c2
int dimen lb_playback_now_playing_bar_height 0x7f0500c3
int dimen lb_playback_now_playing_bar_left_margin 0x7f0500c4
int dimen lb_playback_now_playing_bar_margin 0x7f0500c5
int dimen lb_playback_now_playing_bar_top_margin 0x7f0500c6
int dimen lb_playback_now_playing_bar_width 0x7f0500c7
int dimen lb_playback_now_playing_view_size 0x7f0500c8
int dimen lb_playback_other_rows_center_to_bottom 0x7f0500c9
int dimen lb_playback_play_icon_size 0x7f0500ca
int dimen lb_playback_time_padding_top 0x7f0500cb
int dimen lb_playback_total_time_margin_end 0x7f0500cc
int dimen lb_playback_transport_control_info_margin_bottom 0x7f0500cd
int dimen lb_playback_transport_control_row_padding_bottom 0x7f0500ce
int dimen lb_playback_transport_controlbar_margin_start 0x7f0500cf
int dimen lb_playback_transport_hero_thumbs_height 0x7f0500d0
int dimen lb_playback_transport_hero_thumbs_width 0x7f0500d1
int dimen lb_playback_transport_image_height 0x7f0500d2
int dimen lb_playback_transport_image_margin_end 0x7f0500d3
int dimen lb_playback_transport_progressbar_active_bar_height 0x7f0500d4
int dimen lb_playback_transport_progressbar_active_radius 0x7f0500d5
int dimen lb_playback_transport_progressbar_bar_height 0x7f0500d6
int dimen lb_playback_transport_progressbar_height 0x7f0500d7
int dimen lb_playback_transport_thumbs_bottom_margin 0x7f0500d8
int dimen lb_playback_transport_thumbs_height 0x7f0500d9
int dimen lb_playback_transport_thumbs_margin 0x7f0500da
int dimen lb_playback_transport_thumbs_width 0x7f0500db
int dimen lb_playback_transport_time_margin 0x7f0500dc
int dimen lb_playback_transport_time_margin_top 0x7f0500dd
int dimen lb_rounded_rect_corner_radius 0x7f0500de
int dimen lb_search_bar_edit_text_margin_start 0x7f0500df
int dimen lb_search_bar_height 0x7f0500e0
int dimen lb_search_bar_hint_margin_start 0x7f0500e1
int dimen lb_search_bar_icon_height 0x7f0500e2
int dimen lb_search_bar_icon_margin_start 0x7f0500e3
int dimen lb_search_bar_icon_width 0x7f0500e4
int dimen lb_search_bar_inner_margin_bottom 0x7f0500e5
int dimen lb_search_bar_inner_margin_top 0x7f0500e6
int dimen lb_search_bar_items_height 0x7f0500e7
int dimen lb_search_bar_items_layout_margin_top 0x7f0500e8
int dimen lb_search_bar_items_margin_start 0x7f0500e9
int dimen lb_search_bar_items_width 0x7f0500ea
int dimen lb_search_bar_padding_start 0x7f0500eb
int dimen lb_search_bar_padding_top 0x7f0500ec
int dimen lb_search_bar_speech_orb_margin_start 0x7f0500ed
int dimen lb_search_bar_speech_orb_size 0x7f0500ee
int dimen lb_search_bar_text_size 0x7f0500ef
int dimen lb_search_bar_unfocused_text_size 0x7f0500f0
int dimen lb_search_browse_row_padding_start 0x7f0500f1
int dimen lb_search_browse_rows_align_top 0x7f0500f2
int dimen lb_search_orb_focused_z 0x7f0500f3
int dimen lb_search_orb_margin_bottom 0x7f0500f4
int dimen lb_search_orb_margin_end 0x7f0500f5
int dimen lb_search_orb_margin_start 0x7f0500f6
int dimen lb_search_orb_margin_top 0x7f0500f7
int dimen lb_search_orb_size 0x7f0500f8
int dimen lb_search_orb_unfocused_z 0x7f0500f9
int dimen lb_vertical_grid_padding_bottom 0x7f0500fa
int dimen notification_action_icon_size 0x7f0500fb
int dimen notification_action_text_size 0x7f0500fc
int dimen notification_big_circle_margin 0x7f0500fd
int dimen notification_content_margin_start 0x7f0500fe
int dimen notification_large_icon_height 0x7f0500ff
int dimen notification_large_icon_width 0x7f050100
int dimen notification_main_column_padding_top 0x7f050101
int dimen notification_media_narrow_margin 0x7f050102
int dimen notification_right_icon_size 0x7f050103
int dimen notification_right_side_padding_top 0x7f050104
int dimen notification_small_icon_background_padding 0x7f050105
int dimen notification_small_icon_size_as_large 0x7f050106
int dimen notification_subtext_size 0x7f050107
int dimen notification_top_pad 0x7f050108
int dimen notification_top_pad_large_text 0x7f050109
int dimen picker_column_horizontal_padding 0x7f05010a
int dimen picker_item_height 0x7f05010b
int dimen picker_item_spacing 0x7f05010c
int dimen picker_separator_horizontal_padding 0x7f05010d
int dimen subtitle_corner_radius 0x7f05010e
int dimen subtitle_outline_width 0x7f05010f
int dimen subtitle_shadow_offset 0x7f050110
int dimen subtitle_shadow_radius 0x7f050111
int drawable app_icon_your_company 0x7f060000
int drawable button_background 0x7f060001
int drawable default_background 0x7f060002
int drawable edit_text_background 0x7f060003
int drawable ic_call_answer 0x7f060004
int drawable ic_call_answer_low 0x7f060005
int drawable ic_call_answer_video 0x7f060006
int drawable ic_call_answer_video_low 0x7f060007
int drawable ic_call_decline 0x7f060008
int drawable ic_call_decline_low 0x7f060009
int drawable lb_action_bg 0x7f06000a
int drawable lb_action_bg_focused 0x7f06000b
int drawable lb_background 0x7f06000c
int drawable lb_card_foreground 0x7f06000d
int drawable lb_card_shadow_focused 0x7f06000e
int drawable lb_card_shadow_normal 0x7f06000f
int drawable lb_control_button_primary 0x7f060010
int drawable lb_control_button_secondary 0x7f060011
int drawable lb_headers_right_fading 0x7f060012
int drawable lb_ic_actions_right_arrow 0x7f060013
int drawable lb_ic_cc 0x7f060014
int drawable lb_ic_fast_forward 0x7f060015
int drawable lb_ic_fast_rewind 0x7f060016
int drawable lb_ic_guidedactions_item_chevron 0x7f060017
int drawable lb_ic_hq 0x7f060018
int drawable lb_ic_in_app_search 0x7f060019
int drawable lb_ic_loop 0x7f06001a
int drawable lb_ic_loop_one 0x7f06001b
int drawable lb_ic_more 0x7f06001c
int drawable lb_ic_nav_arrow 0x7f06001d
int drawable lb_ic_pause 0x7f06001e
int drawable lb_ic_pip 0x7f06001f
int drawable lb_ic_play 0x7f060020
int drawable lb_ic_play_fit 0x7f060021
int drawable lb_ic_playback_loop 0x7f060022
int drawable lb_ic_replay 0x7f060023
int drawable lb_ic_sad_cloud 0x7f060024
int drawable lb_ic_search_mic 0x7f060025
int drawable lb_ic_search_mic_out 0x7f060026
int drawable lb_ic_shuffle 0x7f060027
int drawable lb_ic_skip_next 0x7f060028
int drawable lb_ic_skip_previous 0x7f060029
int drawable lb_ic_stop 0x7f06002a
int drawable lb_ic_thumb_down 0x7f06002b
int drawable lb_ic_thumb_down_outline 0x7f06002c
int drawable lb_ic_thumb_up 0x7f06002d
int drawable lb_ic_thumb_up_outline 0x7f06002e
int drawable lb_in_app_search_bg 0x7f06002f
int drawable lb_in_app_search_shadow_focused 0x7f060030
int drawable lb_in_app_search_shadow_normal 0x7f060031
int drawable lb_onboarding_start_button_background 0x7f060032
int drawable lb_playback_now_playing_bar 0x7f060033
int drawable lb_playback_progress_bar 0x7f060034
int drawable lb_search_orb 0x7f060035
int drawable lb_selectable_item_rounded_rect 0x7f060036
int drawable lb_speech_orb 0x7f060037
int drawable lb_text_dot_one 0x7f060038
int drawable lb_text_dot_one_small 0x7f060039
int drawable lb_text_dot_two 0x7f06003a
int drawable lb_text_dot_two_small 0x7f06003b
int drawable movie 0x7f06003c
int drawable notification_action_background 0x7f06003d
int drawable notification_bg 0x7f06003e
int drawable notification_bg_low 0x7f06003f
int drawable notification_bg_low_normal 0x7f060040
int drawable notification_bg_low_pressed 0x7f060041
int drawable notification_bg_normal 0x7f060042
int drawable notification_bg_normal_pressed 0x7f060043
int drawable notification_icon_background 0x7f060044
int drawable notification_template_icon_bg 0x7f060045
int drawable notification_template_icon_low_bg 0x7f060046
int drawable notification_tile_bg 0x7f060047
int drawable notify_panel_notification_icon_bg 0x7f060048
int fraction lb_browse_header_unselect_alpha 0x7f070000
int fraction lb_browse_rows_scale 0x7f070001
int fraction lb_focus_zoom_factor_large 0x7f070002
int fraction lb_focus_zoom_factor_medium 0x7f070003
int fraction lb_focus_zoom_factor_small 0x7f070004
int fraction lb_focus_zoom_factor_xsmall 0x7f070005
int fraction lb_search_bar_speech_orb_max_level_zoom 0x7f070006
int fraction lb_search_orb_focused_zoom 0x7f070007
int fraction lb_view_active_level 0x7f070008
int fraction lb_view_dimmed_level 0x7f070009
int id Content 0x7f080000
int id IconOnLeft 0x7f080001
int id IconOnRight 0x7f080002
int id ImageOnly 0x7f080003
int id Title 0x7f080004
int id accessibility_action_clickable_span 0x7f080005
int id accessibility_custom_action_0 0x7f080006
int id accessibility_custom_action_1 0x7f080007
int id accessibility_custom_action_10 0x7f080008
int id accessibility_custom_action_11 0x7f080009
int id accessibility_custom_action_12 0x7f08000a
int id accessibility_custom_action_13 0x7f08000b
int id accessibility_custom_action_14 0x7f08000c
int id accessibility_custom_action_15 0x7f08000d
int id accessibility_custom_action_16 0x7f08000e
int id accessibility_custom_action_17 0x7f08000f
int id accessibility_custom_action_18 0x7f080010
int id accessibility_custom_action_19 0x7f080011
int id accessibility_custom_action_2 0x7f080012
int id accessibility_custom_action_20 0x7f080013
int id accessibility_custom_action_21 0x7f080014
int id accessibility_custom_action_22 0x7f080015
int id accessibility_custom_action_23 0x7f080016
int id accessibility_custom_action_24 0x7f080017
int id accessibility_custom_action_25 0x7f080018
int id accessibility_custom_action_26 0x7f080019
int id accessibility_custom_action_27 0x7f08001a
int id accessibility_custom_action_28 0x7f08001b
int id accessibility_custom_action_29 0x7f08001c
int id accessibility_custom_action_3 0x7f08001d
int id accessibility_custom_action_30 0x7f08001e
int id accessibility_custom_action_31 0x7f08001f
int id accessibility_custom_action_4 0x7f080020
int id accessibility_custom_action_5 0x7f080021
int id accessibility_custom_action_6 0x7f080022
int id accessibility_custom_action_7 0x7f080023
int id accessibility_custom_action_8 0x7f080024
int id accessibility_custom_action_9 0x7f080025
int id action0 0x7f080026
int id actionIcon 0x7f080027
int id action_container 0x7f080028
int id action_divider 0x7f080029
int id action_fragment 0x7f08002a
int id action_fragment_background 0x7f08002b
int id action_fragment_root 0x7f08002c
int id action_image 0x7f08002d
int id action_text 0x7f08002e
int id actions 0x7f08002f
int id activated 0x7f080030
int id all 0x7f080031
int id always 0x7f080032
int id async 0x7f080033
int id background 0x7f080034
int id background_container 0x7f080035
int id background_imagein 0x7f080036
int id background_imageout 0x7f080037
int id bar1 0x7f080038
int id bar2 0x7f080039
int id bar3 0x7f08003a
int id blocking 0x7f08003b
int id bottom 0x7f08003c
int id bottom_spacer 0x7f08003d
int id browse_container_dock 0x7f08003e
int id browse_dummy 0x7f08003f
int id browse_frame 0x7f080040
int id browse_grid 0x7f080041
int id browse_grid_dock 0x7f080042
int id browse_headers 0x7f080043
int id browse_headers_dock 0x7f080044
int id browse_headers_root 0x7f080045
int id browse_title_group 0x7f080046
int id button 0x7f080047
int id button_start 0x7f080048
int id cancel_action 0x7f080049
int id center 0x7f08004a
int id center_horizontal 0x7f08004b
int id center_vertical 0x7f08004c
int id chronometer 0x7f08004d
int id clip_horizontal 0x7f08004e
int id clip_vertical 0x7f08004f
int id column 0x7f080050
int id container_list 0x7f080051
int id content_container 0x7f080052
int id content_fragment 0x7f080053
int id content_frame 0x7f080054
int id content_text 0x7f080055
int id control_bar 0x7f080056
int id controls_card 0x7f080057
int id controls_card_right_panel 0x7f080058
int id controls_container 0x7f080059
int id controls_dock 0x7f08005a
int id current_time 0x7f08005b
int id description 0x7f08005c
int id description_dock 0x7f08005d
int id details_background_view 0x7f08005e
int id details_fragment 0x7f08005f
int id details_fragment_root 0x7f080060
int id details_frame 0x7f080061
int id details_overview 0x7f080062
int id details_overview_actions 0x7f080063
int id details_overview_actions_background 0x7f080064
int id details_overview_description 0x7f080065
int id details_overview_image 0x7f080066
int id details_overview_right_panel 0x7f080067
int id details_root 0x7f080068
int id details_rows_dock 0x7f080069
int id dialog_button 0x7f08006a
int id dummy 0x7f08006b
int id end 0x7f08006c
int id end_padder 0x7f08006d
int id error_frame 0x7f08006e
int id extra 0x7f08006f
int id extra_badge 0x7f080070
int id fade_out_edge 0x7f080071
int id fill 0x7f080072
int id fill_horizontal 0x7f080073
int id fill_vertical 0x7f080074
int id foreground_container 0x7f080075
int id forever 0x7f080076
int id fragment_container_view_tag 0x7f080077
int id glide_custom_view_target_tag 0x7f080078
int id grid_frame 0x7f080079
int id guidance_breadcrumb 0x7f08007a
int id guidance_container 0x7f08007b
int id guidance_description 0x7f08007c
int id guidance_icon 0x7f08007d
int id guidance_title 0x7f08007e
int id guidedactions_activator_item 0x7f08007f
int id guidedactions_content 0x7f080080
int id guidedactions_content2 0x7f080081
int id guidedactions_item_checkmark 0x7f080082
int id guidedactions_item_chevron 0x7f080083
int id guidedactions_item_content 0x7f080084
int id guidedactions_item_description 0x7f080085
int id guidedactions_item_icon 0x7f080086
int id guidedactions_item_title 0x7f080087
int id guidedactions_list 0x7f080088
int id guidedactions_list2 0x7f080089
int id guidedactions_list_background 0x7f08008a
int id guidedactions_list_background2 0x7f08008b
int id guidedactions_root 0x7f08008c
int id guidedactions_root2 0x7f08008d
int id guidedactions_sub_list 0x7f08008e
int id guidedactions_sub_list_background 0x7f08008f
int id guidedstep_background 0x7f080090
int id guidedstep_background_view_root 0x7f080091
int id guidedstep_root 0x7f080092
int id hovercard_panel 0x7f080093
int id icon 0x7f080094
int id icon_group 0x7f080095
int id image 0x7f080096
int id info 0x7f080097
int id infoOver 0x7f080098
int id infoUnder 0x7f080099
int id infoUnderWithExtra 0x7f08009a
int id info_field 0x7f08009b
int id initial 0x7f08009c
int id is_pooling_container_tag 0x7f08009d
int id italic 0x7f08009e
int id item_touch_helper_previous_elevation 0x7f08009f
int id label 0x7f0800a0
int id lb_action_button 0x7f0800a1
int id lb_control_closed_captioning 0x7f0800a2
int id lb_control_fast_forward 0x7f0800a3
int id lb_control_fast_rewind 0x7f0800a4
int id lb_control_high_quality 0x7f0800a5
int id lb_control_more_actions 0x7f0800a6
int id lb_control_picture_in_picture 0x7f0800a7
int id lb_control_play_pause 0x7f0800a8
int id lb_control_repeat 0x7f0800a9
int id lb_control_shuffle 0x7f0800aa
int id lb_control_skip_next 0x7f0800ab
int id lb_control_skip_previous 0x7f0800ac
int id lb_control_thumbs_down 0x7f0800ad
int id lb_control_thumbs_up 0x7f0800ae
int id lb_details_description_body 0x7f0800af
int id lb_details_description_subtitle 0x7f0800b0
int id lb_details_description_title 0x7f0800b1
int id lb_focus_animator 0x7f0800b2
int id lb_guidedstep_background 0x7f0800b3
int id lb_parallax_source 0x7f0800b4
int id lb_results_frame 0x7f0800b5
int id lb_row_container_header_dock 0x7f0800b6
int id lb_search_bar 0x7f0800b7
int id lb_search_bar_badge 0x7f0800b8
int id lb_search_bar_items 0x7f0800b9
int id lb_search_bar_speech_orb 0x7f0800ba
int id lb_search_frame 0x7f0800bb
int id lb_search_text_editor 0x7f0800bc
int id lb_shadow_focused 0x7f0800bd
int id lb_shadow_impl 0x7f0800be
int id lb_shadow_normal 0x7f0800bf
int id lb_slide_transition_value 0x7f0800c0
int id left 0x7f0800c1
int id line1 0x7f0800c2
int id line3 0x7f0800c3
int id list_item 0x7f0800c4
int id login_button 0x7f0800c5
int id login_fragment 0x7f0800c6
int id logo 0x7f0800c7
int id main 0x7f0800c8
int id mainOnly 0x7f0800c9
int id main_browse_fragment 0x7f0800ca
int id main_icon 0x7f0800cb
int id main_image 0x7f0800cc
int id maxLines 0x7f0800cd
int id mediaItemActionsContainer 0x7f0800ce
int id mediaItemDetails 0x7f0800cf
int id mediaItemDuration 0x7f0800d0
int id mediaItemName 0x7f0800d1
int id mediaItemNumberViewFlipper 0x7f0800d2
int id mediaItemRow 0x7f0800d3
int id mediaListHeader 0x7f0800d4
int id mediaRowSelector 0x7f0800d5
int id mediaRowSeparator 0x7f0800d6
int id media_actions 0x7f0800d7
int id message 0x7f0800d8
int id more_actions_dock 0x7f0800d9
int id navigator_container 0x7f0800da
int id none 0x7f0800db
int id normal 0x7f0800dc
int id notification_background 0x7f0800dd
int id notification_main_column 0x7f0800de
int id notification_main_column_container 0x7f0800df
int id onboarding_fragment_root 0x7f0800e0
int id page_container 0x7f0800e1
int id page_indicator 0x7f0800e2
int id password_edit_text 0x7f0800e3
int id paused 0x7f0800e4
int id picker 0x7f0800e5
int id playback_controls_dock 0x7f0800e6
int id playback_fragment_background 0x7f0800e7
int id playback_fragment_root 0x7f0800e8
int id playback_progress 0x7f0800e9
int id playing 0x7f0800ea
int id pooling_container_listener_holder_tag 0x7f0800eb
int id progress_bar 0x7f0800ec
int id register_button 0x7f0800ed
int id report_drawn 0x7f0800ee
int id right 0x7f0800ef
int id right_icon 0x7f0800f0
int id right_side 0x7f0800f1
int id row_content 0x7f0800f2
int id row_header 0x7f0800f3
int id row_header_description 0x7f0800f4
int id scale_frame 0x7f0800f5
int id search_fragment 0x7f0800f6
int id search_orb 0x7f0800f7
int id secondary_controls_dock 0x7f0800f8
int id selected 0x7f0800f9
int id separate_time 0x7f0800fa
int id separator 0x7f0800fb
int id spacer 0x7f0800fc
int id special_effects_controller_view_tag 0x7f0800fd
int id start 0x7f0800fe
int id status_bar_latest_event_content 0x7f0800ff
int id tag_accessibility_actions 0x7f080100
int id tag_accessibility_clickable_spans 0x7f080101
int id tag_accessibility_heading 0x7f080102
int id tag_accessibility_pane_title 0x7f080103
int id tag_on_apply_window_listener 0x7f080104
int id tag_on_receive_content_listener 0x7f080105
int id tag_on_receive_content_mime_types 0x7f080106
int id tag_screen_reader_focusable 0x7f080107
int id tag_state_description 0x7f080108
int id tag_transition_group 0x7f080109
int id tag_unhandled_key_event_manager 0x7f08010a
int id tag_unhandled_key_listeners 0x7f08010b
int id tag_window_insets_animation_callback 0x7f08010c
int id text 0x7f08010d
int id text2 0x7f08010e
int id thumbs_row 0x7f08010f
int id time 0x7f080110
int id title 0x7f080111
int id title_badge 0x7f080112
int id title_orb 0x7f080113
int id title_text 0x7f080114
int id top 0x7f080115
int id total_time 0x7f080116
int id transitionPosition 0x7f080117
int id transport_row 0x7f080118
int id user_center_fragment 0x7f080119
int id username_edit_text 0x7f08011a
int id video_surface 0x7f08011b
int id video_surface_container 0x7f08011c
int id view_tree_lifecycle_owner 0x7f08011d
int id view_tree_on_back_pressed_dispatcher_owner 0x7f08011e
int id view_tree_saved_state_registry_owner 0x7f08011f
int id view_tree_view_model_store_owner 0x7f080120
int id visible_removing_fragment_view_tag 0x7f080121
int id wrap_content 0x7f080122
int integer cancel_button_image_alpha 0x7f090000
int integer lb_browse_headers_transition_delay 0x7f090001
int integer lb_browse_headers_transition_duration 0x7f090002
int integer lb_browse_rows_anim_duration 0x7f090003
int integer lb_card_activated_animation_duration 0x7f090004
int integer lb_card_selected_animation_delay 0x7f090005
int integer lb_card_selected_animation_duration 0x7f090006
int integer lb_details_description_body_max_lines 0x7f090007
int integer lb_details_description_body_min_lines 0x7f090008
int integer lb_details_description_subtitle_max_lines 0x7f090009
int integer lb_details_description_title_max_lines 0x7f09000a
int integer lb_error_message_max_lines 0x7f09000b
int integer lb_guidedactions_item_animation_duration 0x7f09000c
int integer lb_guidedactions_item_description_min_lines 0x7f09000d
int integer lb_guidedactions_item_title_max_lines 0x7f09000e
int integer lb_guidedactions_item_title_min_lines 0x7f09000f
int integer lb_guidedstep_activity_background_fade_duration_ms 0x7f090010
int integer lb_onboarding_header_description_delay 0x7f090011
int integer lb_onboarding_header_title_delay 0x7f090012
int integer lb_playback_bg_fade_in_ms 0x7f090013
int integer lb_playback_bg_fade_out_ms 0x7f090014
int integer lb_playback_controls_fade_in_ms 0x7f090015
int integer lb_playback_controls_fade_out_ms 0x7f090016
int integer lb_playback_controls_show_time_ms 0x7f090017
int integer lb_playback_controls_tickle_timeout_ms 0x7f090018
int integer lb_playback_description_fade_in_ms 0x7f090019
int integer lb_playback_description_fade_out_ms 0x7f09001a
int integer lb_playback_rows_fade_delay_ms 0x7f09001b
int integer lb_playback_rows_fade_in_ms 0x7f09001c
int integer lb_playback_rows_fade_out_ms 0x7f09001d
int integer lb_search_bar_speech_mode_background_alpha 0x7f09001e
int integer lb_search_bar_text_mode_background_alpha 0x7f09001f
int integer lb_search_orb_pulse_duration_ms 0x7f090020
int integer lb_search_orb_scale_duration_ms 0x7f090021
int integer slideEdgeEnd 0x7f090022
int integer slideEdgeStart 0x7f090023
int integer status_bar_notification_info_maxnum 0x7f090024
int layout activity_details 0x7f0a0000
int layout activity_login 0x7f0a0001
int layout activity_main 0x7f0a0002
int layout activity_search 0x7f0a0003
int layout activity_test 0x7f0a0004
int layout activity_user_center 0x7f0a0005
int layout custom_dialog 0x7f0a0006
int layout fragment_login 0x7f0a0007
int layout lb_action_1_line 0x7f0a0008
int layout lb_action_2_lines 0x7f0a0009
int layout lb_background_window 0x7f0a000a
int layout lb_browse_fragment 0x7f0a000b
int layout lb_browse_title 0x7f0a000c
int layout lb_control_bar 0x7f0a000d
int layout lb_control_button_primary 0x7f0a000e
int layout lb_control_button_secondary 0x7f0a000f
int layout lb_details_description 0x7f0a0010
int layout lb_details_fragment 0x7f0a0011
int layout lb_details_overview 0x7f0a0012
int layout lb_divider 0x7f0a0013
int layout lb_error_fragment 0x7f0a0014
int layout lb_fullwidth_details_overview 0x7f0a0015
int layout lb_fullwidth_details_overview_logo 0x7f0a0016
int layout lb_guidance 0x7f0a0017
int layout lb_guidedactions 0x7f0a0018
int layout lb_guidedactions_datepicker_item 0x7f0a0019
int layout lb_guidedactions_item 0x7f0a001a
int layout lb_guidedbuttonactions 0x7f0a001b
int layout lb_guidedstep_background 0x7f0a001c
int layout lb_guidedstep_fragment 0x7f0a001d
int layout lb_header 0x7f0a001e
int layout lb_headers_fragment 0x7f0a001f
int layout lb_image_card_view 0x7f0a0020
int layout lb_image_card_view_themed_badge_left 0x7f0a0021
int layout lb_image_card_view_themed_badge_right 0x7f0a0022
int layout lb_image_card_view_themed_content 0x7f0a0023
int layout lb_image_card_view_themed_title 0x7f0a0024
int layout lb_list_row 0x7f0a0025
int layout lb_list_row_hovercard 0x7f0a0026
int layout lb_media_item_number_view_flipper 0x7f0a0027
int layout lb_media_list_header 0x7f0a0028
int layout lb_onboarding_fragment 0x7f0a0029
int layout lb_picker 0x7f0a002a
int layout lb_picker_column 0x7f0a002b
int layout lb_picker_item 0x7f0a002c
int layout lb_picker_separator 0x7f0a002d
int layout lb_playback_controls 0x7f0a002e
int layout lb_playback_controls_row 0x7f0a002f
int layout lb_playback_fragment 0x7f0a0030
int layout lb_playback_now_playing_bars 0x7f0a0031
int layout lb_playback_transport_controls 0x7f0a0032
int layout lb_playback_transport_controls_row 0x7f0a0033
int layout lb_row_container 0x7f0a0034
int layout lb_row_header 0x7f0a0035
int layout lb_row_media_item 0x7f0a0036
int layout lb_row_media_item_action 0x7f0a0037
int layout lb_rows_fragment 0x7f0a0038
int layout lb_search_bar 0x7f0a0039
int layout lb_search_fragment 0x7f0a003a
int layout lb_search_orb 0x7f0a003b
int layout lb_section_header 0x7f0a003c
int layout lb_shadow 0x7f0a003d
int layout lb_speech_orb 0x7f0a003e
int layout lb_title_view 0x7f0a003f
int layout lb_vertical_grid 0x7f0a0040
int layout lb_vertical_grid_fragment 0x7f0a0041
int layout lb_video_surface 0x7f0a0042
int layout notification_action 0x7f0a0043
int layout notification_action_tombstone 0x7f0a0044
int layout notification_media_action 0x7f0a0045
int layout notification_media_cancel_action 0x7f0a0046
int layout notification_template_big_media 0x7f0a0047
int layout notification_template_big_media_custom 0x7f0a0048
int layout notification_template_big_media_narrow 0x7f0a0049
int layout notification_template_big_media_narrow_custom 0x7f0a004a
int layout notification_template_custom_big 0x7f0a004b
int layout notification_template_icon_group 0x7f0a004c
int layout notification_template_lines_media 0x7f0a004d
int layout notification_template_media 0x7f0a004e
int layout notification_template_media_custom 0x7f0a004f
int layout notification_template_part_chronometer 0x7f0a0050
int layout notification_template_part_time 0x7f0a0051
int layout video_surface_fragment 0x7f0a0052
int mipmap ic_launcher 0x7f0b0000
int raw lb_voice_failure 0x7f0c0000
int raw lb_voice_no_input 0x7f0c0001
int raw lb_voice_open 0x7f0c0002
int raw lb_voice_success 0x7f0c0003
int string androidx_startup 0x7f0d0000
int string app_name 0x7f0d0001
int string browse_title 0x7f0d0002
int string buy_1 0x7f0d0003
int string buy_2 0x7f0d0004
int string call_notification_answer_action 0x7f0d0005
int string call_notification_answer_video_action 0x7f0d0006
int string call_notification_decline_action 0x7f0d0007
int string call_notification_hang_up_action 0x7f0d0008
int string call_notification_incoming_text 0x7f0d0009
int string call_notification_ongoing_text 0x7f0d000a
int string call_notification_screening_text 0x7f0d000b
int string dismiss_error 0x7f0d000c
int string error_fragment 0x7f0d000d
int string error_fragment_message 0x7f0d000e
int string grid_view 0x7f0d000f
int string lb_control_display_fast_forward_multiplier 0x7f0d0010
int string lb_control_display_rewind_multiplier 0x7f0d0011
int string lb_guidedaction_continue_title 0x7f0d0012
int string lb_guidedaction_finish_title 0x7f0d0013
int string lb_media_player_error 0x7f0d0014
int string lb_navigation_menu_contentDescription 0x7f0d0015
int string lb_onboarding_accessibility_next 0x7f0d0016
int string lb_onboarding_get_started 0x7f0d0017
int string lb_playback_controls_closed_captioning_disable 0x7f0d0018
int string lb_playback_controls_closed_captioning_enable 0x7f0d0019
int string lb_playback_controls_fast_forward 0x7f0d001a
int string lb_playback_controls_fast_forward_multiplier 0x7f0d001b
int string lb_playback_controls_hidden 0x7f0d001c
int string lb_playback_controls_high_quality_disable 0x7f0d001d
int string lb_playback_controls_high_quality_enable 0x7f0d001e
int string lb_playback_controls_more_actions 0x7f0d001f
int string lb_playback_controls_pause 0x7f0d0020
int string lb_playback_controls_picture_in_picture 0x7f0d0021
int string lb_playback_controls_play 0x7f0d0022
int string lb_playback_controls_repeat_all 0x7f0d0023
int string lb_playback_controls_repeat_none 0x7f0d0024
int string lb_playback_controls_repeat_one 0x7f0d0025
int string lb_playback_controls_rewind 0x7f0d0026
int string lb_playback_controls_rewind_multiplier 0x7f0d0027
int string lb_playback_controls_shown 0x7f0d0028
int string lb_playback_controls_shuffle_disable 0x7f0d0029
int string lb_playback_controls_shuffle_enable 0x7f0d002a
int string lb_playback_controls_skip_next 0x7f0d002b
int string lb_playback_controls_skip_previous 0x7f0d002c
int string lb_playback_controls_thumb_down 0x7f0d002d
int string lb_playback_controls_thumb_down_outline 0x7f0d002e
int string lb_playback_controls_thumb_up 0x7f0d002f
int string lb_playback_controls_thumb_up_outline 0x7f0d0030
int string lb_playback_time_separator 0x7f0d0031
int string lb_search_bar_hint 0x7f0d0032
int string lb_search_bar_hint_speech 0x7f0d0033
int string lb_search_bar_hint_with_title 0x7f0d0034
int string lb_search_bar_hint_with_title_speech 0x7f0d0035
int string movie 0x7f0d0036
int string orb_search_action 0x7f0d0037
int string personal_settings 0x7f0d0038
int string related_movies 0x7f0d0039
int string rent_1 0x7f0d003a
int string rent_2 0x7f0d003b
int string status_bar_notification_info_overflow 0x7f0d003c
int string watch_trailer_1 0x7f0d003d
int string watch_trailer_2 0x7f0d003e
int style TextAppearance_Compat_Notification 0x7f0e0000
int style TextAppearance_Compat_Notification_Info 0x7f0e0001
int style TextAppearance_Compat_Notification_Info_Media 0x7f0e0002
int style TextAppearance_Compat_Notification_Line2 0x7f0e0003
int style TextAppearance_Compat_Notification_Line2_Media 0x7f0e0004
int style TextAppearance_Compat_Notification_Media 0x7f0e0005
int style TextAppearance_Compat_Notification_Time 0x7f0e0006
int style TextAppearance_Compat_Notification_Time_Media 0x7f0e0007
int style TextAppearance_Compat_Notification_Title 0x7f0e0008
int style TextAppearance_Compat_Notification_Title_Media 0x7f0e0009
int style TextAppearance_Leanback 0x7f0e000a
int style TextAppearance_Leanback_DetailsActionButton 0x7f0e000b
int style TextAppearance_Leanback_DetailsDescriptionBody 0x7f0e000c
int style TextAppearance_Leanback_DetailsDescriptionSubtitle 0x7f0e000d
int style TextAppearance_Leanback_DetailsDescriptionTitle 0x7f0e000e
int style TextAppearance_Leanback_ErrorMessage 0x7f0e000f
int style TextAppearance_Leanback_Header 0x7f0e0010
int style TextAppearance_Leanback_Header_Section 0x7f0e0011
int style TextAppearance_Leanback_ImageCardView 0x7f0e0012
int style TextAppearance_Leanback_ImageCardView_Content 0x7f0e0013
int style TextAppearance_Leanback_ImageCardView_Title 0x7f0e0014
int style TextAppearance_Leanback_PlaybackControlLabel 0x7f0e0015
int style TextAppearance_Leanback_PlaybackControlsTime 0x7f0e0016
int style TextAppearance_Leanback_PlaybackMediaItemDuration 0x7f0e0017
int style TextAppearance_Leanback_PlaybackMediaItemName 0x7f0e0018
int style TextAppearance_Leanback_PlaybackMediaItemNumber 0x7f0e0019
int style TextAppearance_Leanback_PlaybackMediaListHeaderTitle 0x7f0e001a
int style TextAppearance_Leanback_Row_Header 0x7f0e001b
int style TextAppearance_Leanback_Row_Header_Description 0x7f0e001c
int style TextAppearance_Leanback_Row_HoverCardDescription 0x7f0e001d
int style TextAppearance_Leanback_Row_HoverCardTitle 0x7f0e001e
int style TextAppearance_Leanback_SearchTextEdit 0x7f0e001f
int style TextAppearance_Leanback_Title 0x7f0e0020
int style TextAppearance_LeanbackBase 0x7f0e0021
int style Theme_Leanback 0x7f0e0022
int style Theme_Leanback_Browse 0x7f0e0023
int style Theme_Leanback_Details 0x7f0e0024
int style Theme_Leanback_Details_NoSharedElementTransition 0x7f0e0025
int style Theme_Leanback_GuidedStep 0x7f0e0026
int style Theme_Leanback_GuidedStep_Half 0x7f0e0027
int style Theme_Leanback_GuidedStep_HalfBase 0x7f0e0028
int style Theme_Leanback_GuidedStepBase 0x7f0e0029
int style Theme_Leanback_Onboarding 0x7f0e002a
int style Theme_Leanback_VerticalGrid 0x7f0e002b
int style Theme_LeanbackBase 0x7f0e002c
int style Theme_MyApplicationTV 0x7f0e002d
int style Widget_Compat_NotificationActionContainer 0x7f0e002e
int style Widget_Compat_NotificationActionText 0x7f0e002f
int style Widget_Leanback 0x7f0e0030
int style Widget_Leanback_BaseCardViewStyle 0x7f0e0031
int style Widget_Leanback_DetailsActionButtonStyle 0x7f0e0032
int style Widget_Leanback_DetailsActionButtonStyleBase 0x7f0e0033
int style Widget_Leanback_DetailsDescriptionBodyStyle 0x7f0e0034
int style Widget_Leanback_DetailsDescriptionSubtitleStyle 0x7f0e0035
int style Widget_Leanback_DetailsDescriptionTitleStyle 0x7f0e0036
int style Widget_Leanback_ErrorMessageStyle 0x7f0e0037
int style Widget_Leanback_GridItems 0x7f0e0038
int style Widget_Leanback_GridItems_VerticalGridView 0x7f0e0039
int style Widget_Leanback_GuidanceBreadcrumbStyle 0x7f0e003a
int style Widget_Leanback_GuidanceContainerStyle 0x7f0e003b
int style Widget_Leanback_GuidanceDescriptionStyle 0x7f0e003c
int style Widget_Leanback_GuidanceIconStyle 0x7f0e003d
int style Widget_Leanback_GuidanceTitleStyle 0x7f0e003e
int style Widget_Leanback_GuidedActionItemCheckmarkStyle 0x7f0e003f
int style Widget_Leanback_GuidedActionItemChevronStyle 0x7f0e0040
int style Widget_Leanback_GuidedActionItemContainerStyle 0x7f0e0041
int style Widget_Leanback_GuidedActionItemContentStyle 0x7f0e0042
int style Widget_Leanback_GuidedActionItemDescriptionStyle 0x7f0e0043
int style Widget_Leanback_GuidedActionItemIconStyle 0x7f0e0044
int style Widget_Leanback_GuidedActionItemTitleStyle 0x7f0e0045
int style Widget_Leanback_GuidedActionsContainerStyle 0x7f0e0046
int style Widget_Leanback_GuidedActionsListStyle 0x7f0e0047
int style Widget_Leanback_GuidedActionsSelectorStyle 0x7f0e0048
int style Widget_Leanback_GuidedButtonActionsListStyle 0x7f0e0049
int style Widget_Leanback_GuidedSubActionsListStyle 0x7f0e004a
int style Widget_Leanback_Header 0x7f0e004b
int style Widget_Leanback_Header_Section 0x7f0e004c
int style Widget_Leanback_Headers 0x7f0e004d
int style Widget_Leanback_Headers_VerticalGridView 0x7f0e004e
int style Widget_Leanback_ImageCardView 0x7f0e004f
int style Widget_Leanback_ImageCardView_BadgeStyle 0x7f0e0050
int style Widget_Leanback_ImageCardView_ContentStyle 0x7f0e0051
int style Widget_Leanback_ImageCardView_ImageStyle 0x7f0e0052
int style Widget_Leanback_ImageCardView_InfoAreaStyle 0x7f0e0053
int style Widget_Leanback_ImageCardView_TitleStyle 0x7f0e0054
int style Widget_Leanback_ImageCardViewStyle 0x7f0e0055
int style Widget_Leanback_OnboardingDescriptionStyle 0x7f0e0056
int style Widget_Leanback_OnboardingHeaderStyle 0x7f0e0057
int style Widget_Leanback_OnboardingLogoStyle 0x7f0e0058
int style Widget_Leanback_OnboardingMainIconStyle 0x7f0e0059
int style Widget_Leanback_OnboardingNavigatorContainerStyle 0x7f0e005a
int style Widget_Leanback_OnboardingPageIndicatorStyle 0x7f0e005b
int style Widget_Leanback_OnboardingStartButtonStyle 0x7f0e005c
int style Widget_Leanback_OnboardingStartButtonStyleBase 0x7f0e005d
int style Widget_Leanback_OnboardingTitleStyle 0x7f0e005e
int style Widget_Leanback_PlaybackControlLabelStyle 0x7f0e005f
int style Widget_Leanback_PlaybackControlsActionIconsStyle 0x7f0e0060
int style Widget_Leanback_PlaybackControlsButtonStyle 0x7f0e0061
int style Widget_Leanback_PlaybackControlsTimeStyle 0x7f0e0062
int style Widget_Leanback_PlaybackMediaItemDetailsStyle 0x7f0e0063
int style Widget_Leanback_PlaybackMediaItemDurationStyle 0x7f0e0064
int style Widget_Leanback_PlaybackMediaItemNameStyle 0x7f0e0065
int style Widget_Leanback_PlaybackMediaItemNumberStyle 0x7f0e0066
int style Widget_Leanback_PlaybackMediaItemNumberViewFlipperStyle 0x7f0e0067
int style Widget_Leanback_PlaybackMediaItemRowStyle 0x7f0e0068
int style Widget_Leanback_PlaybackMediaItemSeparatorStyle 0x7f0e0069
int style Widget_Leanback_PlaybackMediaListHeaderStyle 0x7f0e006a
int style Widget_Leanback_PlaybackMediaListHeaderTitleStyle 0x7f0e006b
int style Widget_Leanback_PlaybackRow 0x7f0e006c
int style Widget_Leanback_Row 0x7f0e006d
int style Widget_Leanback_Row_Header 0x7f0e006e
int style Widget_Leanback_Row_Header_Description 0x7f0e006f
int style Widget_Leanback_Row_HeaderDock 0x7f0e0070
int style Widget_Leanback_Row_HorizontalGridView 0x7f0e0071
int style Widget_Leanback_Row_HoverCardDescription 0x7f0e0072
int style Widget_Leanback_Row_HoverCardTitle 0x7f0e0073
int style Widget_Leanback_Rows 0x7f0e0074
int style Widget_Leanback_Rows_VerticalGridView 0x7f0e0075
int style Widget_Leanback_SearchOrbViewStyle 0x7f0e0076
int style Widget_Leanback_Title 0x7f0e0077
int style Widget_Leanback_Title_Icon 0x7f0e0078
int style Widget_Leanback_Title_Text 0x7f0e0079
int style Widget_Leanback_TitleView 0x7f0e007a
int style Widget_LeanbackBase 0x7f0e007b
int style Widget_Support_CoordinatorLayout 0x7f0e007c
int[] styleable Capability { 0x7f0300b4, 0x7f0300ce }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f030001, 0x7f03007f }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CoordinatorLayout { 0x7f03007e, 0x7f0300d4 }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f030081, 0x7f030082, 0x7f030083, 0x7f030084, 0x7f030085, 0x7f030086 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable FontFamily { 0x7f030033, 0x7f030034, 0x7f030035, 0x7f030036, 0x7f030037, 0x7f030038, 0x7f030039 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f030032, 0x7f03003a, 0x7f03003b, 0x7f03003c, 0x7f0300d9 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LeanbackGuidedStepTheme { 0x7f03003d, 0x7f03003e, 0x7f03003f, 0x7f030040, 0x7f030041, 0x7f030042, 0x7f030043, 0x7f030044, 0x7f030045, 0x7f030046, 0x7f030047, 0x7f030048, 0x7f030049, 0x7f03004a, 0x7f03004b, 0x7f03004c, 0x7f03004d, 0x7f03004e, 0x7f03004f, 0x7f030050, 0x7f030051, 0x7f030052, 0x7f030053, 0x7f030054, 0x7f030055, 0x7f030056, 0x7f030057, 0x7f030058, 0x7f030059, 0x7f03005a, 0x7f03005b, 0x7f03005c, 0x7f03005d, 0x7f03005e, 0x7f03005f, 0x7f030060, 0x7f030061, 0x7f030062, 0x7f030063, 0x7f030064, 0x7f030065, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f030069, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f03006d, 0x7f03006e, 0x7f03006f }
int styleable LeanbackGuidedStepTheme_guidanceBreadcrumbStyle 0
int styleable LeanbackGuidedStepTheme_guidanceContainerStyle 1
int styleable LeanbackGuidedStepTheme_guidanceDescriptionStyle 2
int styleable LeanbackGuidedStepTheme_guidanceEntryAnimation 3
int styleable LeanbackGuidedStepTheme_guidanceIconStyle 4
int styleable LeanbackGuidedStepTheme_guidanceTitleStyle 5
int styleable LeanbackGuidedStepTheme_guidedActionCheckedAnimation 6
int styleable LeanbackGuidedStepTheme_guidedActionContentWidth 7
int styleable LeanbackGuidedStepTheme_guidedActionContentWidthNoIcon 8
int styleable LeanbackGuidedStepTheme_guidedActionContentWidthWeight 9
int styleable LeanbackGuidedStepTheme_guidedActionContentWidthWeightTwoPanels 10
int styleable LeanbackGuidedStepTheme_guidedActionDescriptionMinLines 11
int styleable LeanbackGuidedStepTheme_guidedActionDisabledChevronAlpha 12
int styleable LeanbackGuidedStepTheme_guidedActionEnabledChevronAlpha 13
int styleable LeanbackGuidedStepTheme_guidedActionItemCheckmarkStyle 14
int styleable LeanbackGuidedStepTheme_guidedActionItemChevronStyle 15
int styleable LeanbackGuidedStepTheme_guidedActionItemContainerStyle 16
int styleable LeanbackGuidedStepTheme_guidedActionItemContentStyle 17
int styleable LeanbackGuidedStepTheme_guidedActionItemDescriptionStyle 18
int styleable LeanbackGuidedStepTheme_guidedActionItemIconStyle 19
int styleable LeanbackGuidedStepTheme_guidedActionItemTitleStyle 20
int styleable LeanbackGuidedStepTheme_guidedActionPressedAnimation 21
int styleable LeanbackGuidedStepTheme_guidedActionTitleMaxLines 22
int styleable LeanbackGuidedStepTheme_guidedActionTitleMinLines 23
int styleable LeanbackGuidedStepTheme_guidedActionUncheckedAnimation 24
int styleable LeanbackGuidedStepTheme_guidedActionUnpressedAnimation 25
int styleable LeanbackGuidedStepTheme_guidedActionVerticalPadding 26
int styleable LeanbackGuidedStepTheme_guidedActionsBackground 27
int styleable LeanbackGuidedStepTheme_guidedActionsBackgroundDark 28
int styleable LeanbackGuidedStepTheme_guidedActionsContainerStyle 29
int styleable LeanbackGuidedStepTheme_guidedActionsElevation 30
int styleable LeanbackGuidedStepTheme_guidedActionsEntryAnimation 31
int styleable LeanbackGuidedStepTheme_guidedActionsListStyle 32
int styleable LeanbackGuidedStepTheme_guidedActionsSelectorDrawable 33
int styleable LeanbackGuidedStepTheme_guidedActionsSelectorHideAnimation 34
int styleable LeanbackGuidedStepTheme_guidedActionsSelectorShowAnimation 35
int styleable LeanbackGuidedStepTheme_guidedActionsSelectorStyle 36
int styleable LeanbackGuidedStepTheme_guidedButtonActionsListStyle 37
int styleable LeanbackGuidedStepTheme_guidedButtonActionsWidthWeight 38
int styleable LeanbackGuidedStepTheme_guidedStepBackground 39
int styleable LeanbackGuidedStepTheme_guidedStepEntryAnimation 40
int styleable LeanbackGuidedStepTheme_guidedStepExitAnimation 41
int styleable LeanbackGuidedStepTheme_guidedStepHeightWeight 42
int styleable LeanbackGuidedStepTheme_guidedStepImeAppearingAnimation 43
int styleable LeanbackGuidedStepTheme_guidedStepImeDisappearingAnimation 44
int styleable LeanbackGuidedStepTheme_guidedStepKeyline 45
int styleable LeanbackGuidedStepTheme_guidedStepReentryAnimation 46
int styleable LeanbackGuidedStepTheme_guidedStepReturnAnimation 47
int styleable LeanbackGuidedStepTheme_guidedStepTheme 48
int styleable LeanbackGuidedStepTheme_guidedStepThemeFlag 49
int styleable LeanbackGuidedStepTheme_guidedSubActionsListStyle 50
int[] styleable LeanbackOnboardingTheme { 0x7f03008f, 0x7f030090, 0x7f030091, 0x7f030092, 0x7f030093, 0x7f030094, 0x7f030095, 0x7f030096, 0x7f030097 }
int styleable LeanbackOnboardingTheme_onboardingDescriptionStyle 0
int styleable LeanbackOnboardingTheme_onboardingHeaderStyle 1
int styleable LeanbackOnboardingTheme_onboardingLogoStyle 2
int styleable LeanbackOnboardingTheme_onboardingMainIconStyle 3
int styleable LeanbackOnboardingTheme_onboardingNavigatorContainerStyle 4
int styleable LeanbackOnboardingTheme_onboardingPageIndicatorStyle 5
int styleable LeanbackOnboardingTheme_onboardingStartButtonStyle 6
int styleable LeanbackOnboardingTheme_onboardingTheme 7
int styleable LeanbackOnboardingTheme_onboardingTitleStyle 8
int[] styleable LeanbackTheme { 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000e, 0x7f03000f, 0x7f030010, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030020, 0x7f030021, 0x7f030022, 0x7f030026, 0x7f030070, 0x7f030071, 0x7f030074, 0x7f030075, 0x7f030076, 0x7f030077, 0x7f030078, 0x7f030079, 0x7f03007d, 0x7f030098, 0x7f030099, 0x7f03009a, 0x7f03009e, 0x7f03009f, 0x7f0300a0, 0x7f0300a1, 0x7f0300a2, 0x7f0300a3, 0x7f0300a4, 0x7f0300a5, 0x7f0300a6, 0x7f0300a7, 0x7f0300a8, 0x7f0300a9, 0x7f0300aa, 0x7f0300ab, 0x7f0300ac, 0x7f0300ad, 0x7f0300ae, 0x7f0300af, 0x7f0300b0, 0x7f0300b1, 0x7f0300b2, 0x7f0300b3, 0x7f0300be, 0x7f0300bf, 0x7f0300c0, 0x7f0300c2, 0x7f0300c3, 0x7f0300c4, 0x7f0300c5, 0x7f0300ca, 0x7f0300cb }
int styleable LeanbackTheme_baseCardViewStyle 0
int styleable LeanbackTheme_browsePaddingBottom 1
int styleable LeanbackTheme_browsePaddingEnd 2
int styleable LeanbackTheme_browsePaddingStart 3
int styleable LeanbackTheme_browsePaddingTop 4
int styleable LeanbackTheme_browseRowsFadingEdgeLength 5
int styleable LeanbackTheme_browseRowsMarginStart 6
int styleable LeanbackTheme_browseRowsMarginTop 7
int styleable LeanbackTheme_browseTitleIconStyle 8
int styleable LeanbackTheme_browseTitleTextStyle 9
int styleable LeanbackTheme_browseTitleViewLayout 10
int styleable LeanbackTheme_browseTitleViewStyle 11
int styleable LeanbackTheme_defaultBrandColor 12
int styleable LeanbackTheme_defaultBrandColorDark 13
int styleable LeanbackTheme_defaultSearchBrightColor 14
int styleable LeanbackTheme_defaultSearchColor 15
int styleable LeanbackTheme_defaultSearchIcon 16
int styleable LeanbackTheme_defaultSearchIconColor 17
int styleable LeanbackTheme_defaultSectionHeaderColor 18
int styleable LeanbackTheme_detailsActionButtonStyle 19
int styleable LeanbackTheme_detailsDescriptionBodyStyle 20
int styleable LeanbackTheme_detailsDescriptionSubtitleStyle 21
int styleable LeanbackTheme_detailsDescriptionTitleStyle 22
int styleable LeanbackTheme_errorMessageStyle 23
int styleable LeanbackTheme_headerStyle 24
int styleable LeanbackTheme_headersVerticalGridStyle 25
int styleable LeanbackTheme_imageCardViewBadgeStyle 26
int styleable LeanbackTheme_imageCardViewContentStyle 27
int styleable LeanbackTheme_imageCardViewImageStyle 28
int styleable LeanbackTheme_imageCardViewInfoAreaStyle 29
int styleable LeanbackTheme_imageCardViewStyle 30
int styleable LeanbackTheme_imageCardViewTitleStyle 31
int styleable LeanbackTheme_itemsVerticalGridStyle 32
int styleable LeanbackTheme_overlayDimActiveLevel 33
int styleable LeanbackTheme_overlayDimDimmedLevel 34
int styleable LeanbackTheme_overlayDimMaskColor 35
int styleable LeanbackTheme_playbackControlButtonLabelStyle 36
int styleable LeanbackTheme_playbackControlsActionIcons 37
int styleable LeanbackTheme_playbackControlsAutoHideTickleTimeout 38
int styleable LeanbackTheme_playbackControlsAutoHideTimeout 39
int styleable LeanbackTheme_playbackControlsButtonStyle 40
int styleable LeanbackTheme_playbackControlsIconHighlightColor 41
int styleable LeanbackTheme_playbackControlsTimeStyle 42
int styleable LeanbackTheme_playbackMediaItemDetailsStyle 43
int styleable LeanbackTheme_playbackMediaItemDurationStyle 44
int styleable LeanbackTheme_playbackMediaItemNameStyle 45
int styleable LeanbackTheme_playbackMediaItemNumberStyle 46
int styleable LeanbackTheme_playbackMediaItemNumberViewFlipperLayout 47
int styleable LeanbackTheme_playbackMediaItemNumberViewFlipperStyle 48
int styleable LeanbackTheme_playbackMediaItemPaddingStart 49
int styleable LeanbackTheme_playbackMediaItemRowStyle 50
int styleable LeanbackTheme_playbackMediaItemSeparatorStyle 51
int styleable LeanbackTheme_playbackMediaListHeaderStyle 52
int styleable LeanbackTheme_playbackMediaListHeaderTitleStyle 53
int styleable LeanbackTheme_playbackPaddingEnd 54
int styleable LeanbackTheme_playbackPaddingStart 55
int styleable LeanbackTheme_playbackProgressPrimaryColor 56
int styleable LeanbackTheme_playbackProgressSecondaryColor 57
int styleable LeanbackTheme_rowHeaderDescriptionStyle 58
int styleable LeanbackTheme_rowHeaderDockStyle 59
int styleable LeanbackTheme_rowHeaderStyle 60
int styleable LeanbackTheme_rowHorizontalGridStyle 61
int styleable LeanbackTheme_rowHoverCardDescriptionStyle 62
int styleable LeanbackTheme_rowHoverCardTitleStyle 63
int styleable LeanbackTheme_rowsVerticalGridStyle 64
int styleable LeanbackTheme_searchOrbViewStyle 65
int styleable LeanbackTheme_sectionHeaderStyle 66
int[] styleable PagingIndicator { 0x7f030002, 0x7f030003, 0x7f030004, 0x7f030023, 0x7f030024, 0x7f030025, 0x7f030088 }
int styleable PagingIndicator_arrowBgColor 0
int styleable PagingIndicator_arrowColor 1
int styleable PagingIndicator_arrowRadius 2
int styleable PagingIndicator_dotBgColor 3
int styleable PagingIndicator_dotToArrowGap 4
int styleable PagingIndicator_dotToDotGap 5
int styleable PagingIndicator_lbDotRadius 6
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f030028, 0x7f030029, 0x7f03002a, 0x7f03002b, 0x7f03002c, 0x7f030080, 0x7f0300bc, 0x7f0300d2, 0x7f0300d3 }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable lbBaseCardView { 0x7f030000, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030027, 0x7f03007b, 0x7f0300cc, 0x7f0300cd }
int styleable lbBaseCardView_activatedAnimationDuration 0
int styleable lbBaseCardView_cardBackground 1
int styleable lbBaseCardView_cardForeground 2
int styleable lbBaseCardView_cardType 3
int styleable lbBaseCardView_extraVisibility 4
int styleable lbBaseCardView_infoVisibility 5
int styleable lbBaseCardView_selectedAnimationDelay 6
int styleable lbBaseCardView_selectedAnimationDuration 7
int[] styleable lbBaseCardView_Layout { 0x7f030087 }
int styleable lbBaseCardView_Layout_layout_viewType 0
int[] styleable lbBaseGridView { 0x010100af, 0x01010114, 0x01010115, 0x7f03002e, 0x7f03002f, 0x7f030030, 0x7f030031, 0x7f030073, 0x7f0300db }
int styleable lbBaseGridView_android_gravity 0
int styleable lbBaseGridView_android_horizontalSpacing 1
int styleable lbBaseGridView_android_verticalSpacing 2
int styleable lbBaseGridView_focusOutEnd 3
int styleable lbBaseGridView_focusOutFront 4
int styleable lbBaseGridView_focusOutSideEnd 5
int styleable lbBaseGridView_focusOutSideStart 6
int styleable lbBaseGridView_horizontalMargin 7
int styleable lbBaseGridView_verticalMargin 8
int[] styleable lbDatePicker { 0x0101033f, 0x01010340, 0x7f030017 }
int styleable lbDatePicker_android_minDate 0
int styleable lbDatePicker_android_maxDate 1
int styleable lbDatePicker_datePickerFormat 2
int[] styleable lbHorizontalGridView { 0x7f03008e, 0x7f0300c1 }
int styleable lbHorizontalGridView_numberOfRows 0
int styleable lbHorizontalGridView_rowHeight 1
int[] styleable lbImageCardView { 0x7f03007a, 0x7f030089 }
int styleable lbImageCardView_infoAreaBackground 0
int styleable lbImageCardView_lbImageCardViewType 1
int[] styleable lbPlaybackControlsActionIcons { 0x7f030014, 0x7f03002d, 0x7f030072, 0x7f03009b, 0x7f03009c, 0x7f03009d, 0x7f0300b6, 0x7f0300b7, 0x7f0300bd, 0x7f0300cf, 0x7f0300d0, 0x7f0300d1, 0x7f0300d5, 0x7f0300d6, 0x7f0300d7, 0x7f0300d8 }
int styleable lbPlaybackControlsActionIcons_closed_captioning 0
int styleable lbPlaybackControlsActionIcons_fast_forward 1
int styleable lbPlaybackControlsActionIcons_high_quality 2
int styleable lbPlaybackControlsActionIcons_pause 3
int styleable lbPlaybackControlsActionIcons_picture_in_picture 4
int styleable lbPlaybackControlsActionIcons_play 5
int styleable lbPlaybackControlsActionIcons_repeat 6
int styleable lbPlaybackControlsActionIcons_repeat_one 7
int styleable lbPlaybackControlsActionIcons_rewind 8
int styleable lbPlaybackControlsActionIcons_shuffle 9
int styleable lbPlaybackControlsActionIcons_skip_next 10
int styleable lbPlaybackControlsActionIcons_skip_previous 11
int styleable lbPlaybackControlsActionIcons_thumb_down 12
int styleable lbPlaybackControlsActionIcons_thumb_down_outline 13
int styleable lbPlaybackControlsActionIcons_thumb_up 14
int styleable lbPlaybackControlsActionIcons_thumb_up_outline 15
int[] styleable lbResizingTextView { 0x7f03008b, 0x7f0300b8, 0x7f0300b9, 0x7f0300ba, 0x7f0300bb }
int styleable lbResizingTextView_maintainLineSpacing 0
int styleable lbResizingTextView_resizeTrigger 1
int styleable lbResizingTextView_resizedPaddingAdjustmentBottom 2
int styleable lbResizingTextView_resizedPaddingAdjustmentTop 3
int styleable lbResizingTextView_resizedTextSize 4
int[] styleable lbSearchOrbView { 0x7f0300c6, 0x7f0300c7, 0x7f0300c8, 0x7f0300c9 }
int styleable lbSearchOrbView_searchOrbBrightColor 0
int styleable lbSearchOrbView_searchOrbColor 1
int styleable lbSearchOrbView_searchOrbIcon 2
int styleable lbSearchOrbView_searchOrbIconColor 3
int[] styleable lbSlide { 0x01010141, 0x01010198, 0x010103e2, 0x7f03008a }
int styleable lbSlide_android_interpolator 0
int styleable lbSlide_android_duration 1
int styleable lbSlide_android_startDelay 2
int styleable lbSlide_lb_slideEdge 3
int[] styleable lbTimePicker { 0x7f03007c, 0x7f0300da }
int styleable lbTimePicker_is24HourFormat 0
int styleable lbTimePicker_useCurrentTime 1
int[] styleable lbVerticalGridView { 0x7f030015, 0x7f03008d }
int styleable lbVerticalGridView_columnWidth 0
int styleable lbVerticalGridView_numberOfColumns 1
int transition lb_browse_enter_transition 0x7f100000
int transition lb_browse_entrance_transition 0x7f100001
int transition lb_browse_headers_in 0x7f100002
int transition lb_browse_headers_out 0x7f100003
int transition lb_browse_return_transition 0x7f100004
int transition lb_details_enter_transition 0x7f100005
int transition lb_details_return_transition 0x7f100006
int transition lb_enter_transition 0x7f100007
int transition lb_guidedstep_activity_enter 0x7f100008
int transition lb_guidedstep_activity_enter_bottom 0x7f100009
int transition lb_return_transition 0x7f10000a
int transition lb_shared_element_enter_transition 0x7f10000b
int transition lb_shared_element_return_transition 0x7f10000c
int transition lb_title_in 0x7f10000d
int transition lb_title_out 0x7f10000e
int transition lb_vertical_grid_enter_transition 0x7f10000f
int transition lb_vertical_grid_entrance_transition 0x7f100010
int transition lb_vertical_grid_return_transition 0x7f100011
