import request from '@/utils/request'

// 获取游戏列表
export function getGames(params) {
  return request({
    url: '/games',
    method: 'get',
    params
  })
}

// 获取单个游戏
export function getGame(id) {
  return request({
    url: `/games/${id}`,
    method: 'get'
  })
}

// 创建游戏
export function createGame(data) {
  return request({
    url: '/games',
    method: 'post',
    data
  })
}

// 更新游戏
export function updateGame(data) {
  return request({
    url: `/games/${data.id}`,
    method: 'put',
    data
  })
}

// 删除游戏
export function deleteGame(id) {
  return request({
    url: `/games/${id}`,
    method: 'delete'
  })
}

// 更新游戏状态
export function updateGameStatus(id, status) {
  return request({
    url: `/games/${id}/status`,
    method: 'patch',
    data: { status }
  })
}

// 批量操作游戏
export function batchUpdateGames(data) {
  return request({
    url: '/games/batch',
    method: 'patch',
    data
  })
}

// 获取游戏统计
export function getGameStatistics() {
  return request({
    url: '/games/statistics',
    method: 'get'
  })
}

// 获取推荐游戏
export function getRecommendedGames(params) {
  return request({
    url: '/games/recommended',
    method: 'get',
    params
  })
}

// 获取精选游戏
export function getFeaturedGames(params) {
  return request({
    url: '/games/featured',
    method: 'get',
    params
  })
}
