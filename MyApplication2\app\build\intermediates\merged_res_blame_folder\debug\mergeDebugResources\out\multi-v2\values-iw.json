{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeDebugResources-30:/values-iw/values-iw.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,495,596,696,5085", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "194,296,393,490,591,691,797,5181"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,319,413,505,627,735,830,928,1047,1165,1268,1391,1532,1650,1767,1873,1964,2089,2179,2282,2390,2497,2594,2712,2815,2922,3028,3136,3250,3372,3496,3613,3732,3819,3902,4008,4145,4300", "endColumns": "109,103,93,91,121,107,94,97,118,117,102,122,140,117,116,105,90,124,89,102,107,106,96,117,102,106,105,107,113,121,123,116,118,86,82,105,136,154,87", "endOffsets": "210,314,408,500,622,730,825,923,1042,1160,1263,1386,1527,1645,1762,1868,1959,2084,2174,2277,2385,2492,2589,2707,2810,2917,3023,3131,3245,3367,3491,3608,3727,3814,3897,4003,4140,4295,4383"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "802,912,1016,1110,1202,1324,1432,1527,1625,1744,1862,1965,2088,2229,2347,2464,2570,2661,2786,2876,2979,3087,3194,3291,3409,3512,3619,3725,3833,3947,4069,4193,4310,4429,4516,4599,4705,4842,4997", "endColumns": "109,103,93,91,121,107,94,97,118,117,102,122,140,117,116,105,90,124,89,102,107,106,96,117,102,106,105,107,113,121,123,116,118,86,82,105,136,154,87", "endOffsets": "907,1011,1105,1197,1319,1427,1522,1620,1739,1857,1960,2083,2224,2342,2459,2565,2656,2781,2871,2974,3082,3189,3286,3404,3507,3614,3720,3828,3942,4064,4188,4305,4424,4511,4594,4700,4837,4992,5080"}}]}]}