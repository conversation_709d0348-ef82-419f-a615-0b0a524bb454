package com.example.myapplicationtv.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplicationtv.ContentItem
import com.example.myapplicationtv.model.ProductCategory
import kotlinx.coroutines.launch

/**
 * 商城页面ViewModel
 */
class ShopViewModel : ViewModel() {

    private val _productCategories = MutableLiveData<List<ProductCategory>>()
    val productCategories: LiveData<List<ProductCategory>> = _productCategories

    private val _hotProducts = MutableLiveData<List<ContentItem>>()
    val hotProducts: LiveData<List<ContentItem>> = _hotProducts

    private val _featuredProducts = MutableLiveData<List<ContentItem>>()
    val featuredProducts: LiveData<List<ContentItem>> = _featuredProducts

    fun loadShopData() {
        viewModelScope.launch {
            loadProductCategories()
            loadHotProducts()
            loadFeaturedProducts()
        }
    }

    private fun loadProductCategories() {
        // 模拟商品分类数据
        val categories = listOf(
            ProductCategory(
                id = 1,
                name = "服饰",
                products = listOf(
                    ContentItem(
                        id = "product_1",
                        title = "智能手表",
                        description = "多功能智能穿戴设备",
                        imageUrl = "https://example.com/product1.jpg",
                        type = com.example.myapplicationtv.model.ContentType.PRODUCT,
                        rating = java.math.BigDecimal.valueOf(4.8),
                        price = "¥1,299"
                    ),
                    ContentItem(
                        id = "product_2",
                        title = "无线耳机",
                        description = "高品质音质体验",
                        imageUrl = "https://example.com/product2.jpg",
                        type = com.example.myapplicationtv.model.ContentType.PRODUCT,
                        rating = java.math.BigDecimal.valueOf(4.6),
                        price = "¥599"
                    )
                )
            ),
            ProductCategory(
                id = 2,
                name = "智能家居",
                products = listOf(
                    ContentItem(
                        id = "product_3",
                        title = "智能音箱",
                        description = "语音控制，智能家居中心",
                        imageUrl = "https://example.com/product3.jpg",
                        type = com.example.myapplicationtv.model.ContentType.PRODUCT,
                        rating = java.math.BigDecimal.valueOf(4.9),
                        price = "¥399"
                    )
                )
            ),
            ProductCategory(
                id = 3,
                name = "电脑办公",
                products = listOf(
                    ContentItem(
                        id = "product_4",
                        title = "机械键盘",
                        description = "游戏办公两用，手感出色",
                        imageUrl = "https://example.com/product4.jpg",
                        type = com.example.myapplicationtv.model.ContentType.PRODUCT,
                        rating = java.math.BigDecimal.valueOf(4.7),
                        price = "¥299"
                    )
                )
            ),
            ProductCategory(
                id = 4,
                name = "手机平板",
                products = listOf(
                    ContentItem(
                        id = "product_5",
                        title = "智能手机",
                        description = "高性能处理器，拍照出色",
                        imageUrl = "https://example.com/product5.jpg",
                        type = com.example.myapplicationtv.model.ContentType.PRODUCT,
                        rating = java.math.BigDecimal.valueOf(4.5),
                        price = "¥2,999"
                    )
                )
            ),
            ProductCategory(
                id = 5,
                name = "数码配件",
                products = listOf(
                    ContentItem(
                        id = "product_6",
                        title = "充电宝",
                        description = "大容量，快充技术",
                        imageUrl = "https://example.com/product6.jpg",
                        type = com.example.myapplicationtv.model.ContentType.PRODUCT,
                        rating = java.math.BigDecimal.valueOf(4.4),
                        price = "¥99"
                    )
                )
            ),
            ProductCategory(
                id = 6,
                name = "热门茶叶",
                products = listOf(
                    ContentItem(
                        id = "product_7",
                        title = "龙井茶",
                        description = "正宗西湖龙井，清香甘甜",
                        imageUrl = "https://example.com/product7.jpg",
                        type = com.example.myapplicationtv.model.ContentType.PRODUCT,
                        rating = java.math.BigDecimal.valueOf(4.8),
                        price = "¥168"
                    )
                )
            )
        )
        _productCategories.value = categories
    }

    private fun loadHotProducts() {
        // 模拟热门商品数据
        val hotProducts = listOf(
            ContentItem(
                id = "hot_product_1",
                title = "4K超清电视机",
                description = "55英寸大屏，画质清晰",
                imageUrl = "https://example.com/hot_product1.jpg",
                type = com.example.myapplicationtv.model.ContentType.PRODUCT,
                rating = java.math.BigDecimal.valueOf(4.9),
                price = "¥3,999"
            ),
            ContentItem(
                id = "hot_product_2",
                title = "家庭影院音响",
                description = "环绕立体声，影院级体验",
                imageUrl = "https://example.com/hot_product2.jpg",
                type = com.example.myapplicationtv.model.ContentType.PRODUCT,
                rating = java.math.BigDecimal.valueOf(4.8),
                price = "¥2,499"
            ),
            ContentItem(
                id = "hot_product_3",
                title = "智能电饭煲",
                description = "AI烹饪，营养美味",
                imageUrl = "https://example.com/hot_product3.jpg",
                type = com.example.myapplicationtv.model.ContentType.PRODUCT,
                rating = java.math.BigDecimal.valueOf(4.7),
                price = "¥599"
            ),
            ContentItem(
                id = "hot_product_4",
                title = "4K超清电视机",
                description = "65英寸超大屏幕",
                imageUrl = "https://example.com/hot_product4.jpg",
                type = com.example.myapplicationtv.model.ContentType.PRODUCT,
                rating = java.math.BigDecimal.valueOf(4.8),
                price = "¥3,999"
            )
        )
        _hotProducts.value = hotProducts
    }

    private fun loadFeaturedProducts() {
        // 模拟精选商品数据
        val featuredProducts = listOf(
            ContentItem(
                id = "featured_product_1",
                title = "精选商品1",
                description = "编辑推荐的优质商品",
                imageUrl = "https://example.com/featured_product1.jpg",
                type = com.example.myapplicationtv.model.ContentType.PRODUCT,
                rating = java.math.BigDecimal.valueOf(4.9),
                price = "¥999"
            )
        )
        _featuredProducts.value = featuredProducts
    }
}
