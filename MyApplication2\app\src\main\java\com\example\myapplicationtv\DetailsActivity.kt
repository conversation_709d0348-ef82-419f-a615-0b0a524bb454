package com.example.myapplicationtv

import android.os.Bundle
import com.example.myapplicationtv.base.BaseTVActivity

/**
 * Details activity class that loads [VideoDetailsFragment] class.
 */
class DetailsActivity : BaseTVActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_details)
        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .replace(R.id.details_fragment, VideoDetailsFragment())
                .commitNow()
        }
    }

    companion object {
        const val SHARED_ELEMENT_NAME = "hero"
        const val MOVIE = "Movie"
        const val CONTENT_ITEM = "ContentItem"
    }
}