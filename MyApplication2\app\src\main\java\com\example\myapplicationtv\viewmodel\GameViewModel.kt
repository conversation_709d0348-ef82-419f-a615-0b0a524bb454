package com.example.myapplicationtv.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplicationtv.ContentItem
import com.example.myapplicationtv.model.GameCategory
import kotlinx.coroutines.launch

/**
 * 游戏页面ViewModel
 */
class GameViewModel : ViewModel() {

    private val _gameCategories = MutableLiveData<List<GameCategory>>()
    val gameCategories: LiveData<List<GameCategory>> = _gameCategories

    private val _hotGames = MutableLiveData<List<ContentItem>>()
    val hotGames: LiveData<List<ContentItem>> = _hotGames

    private val _featuredGames = MutableLiveData<List<ContentItem>>()
    val featuredGames: LiveData<List<ContentItem>> = _featuredGames

    fun loadGameData() {
        viewModelScope.launch {
            loadGameCategories()
            loadHotGames()
            loadFeaturedGames()
        }
    }

    private fun loadGameCategories() {
        // 模拟游戏分类数据
        val categories = listOf(
            GameCategory(
                id = 1,
                name = "体育游戏",
                games = listOf(
                    ContentItem(
                        id = "game_1",
                        title = "王者战争",
                        description = "热血竞技，王者对决",
                        imageUrl = "https://example.com/game1.jpg",
                        type = com.example.myapplicationtv.model.ContentType.GAME,
                        rating = java.math.BigDecimal.valueOf(4.8)
                    ),
                    ContentItem(
                        id = "game_2",
                        title = "荒野生存",
                        description = "生存挑战，极限求生",
                        imageUrl = "https://example.com/game2.jpg",
                        type = com.example.myapplicationtv.model.ContentType.GAME,
                        rating = java.math.BigDecimal.valueOf(4.6)
                    )
                )
            ),
            GameCategory(
                id = 2,
                name = "儿童游戏",
                games = listOf(
                    ContentItem(
                        id = "game_3",
                        title = "超级玛丽",
                        description = "经典冒险，童年回忆",
                        imageUrl = "https://example.com/game3.jpg",
                        type = com.example.myapplicationtv.model.ContentType.GAME,
                        rating = java.math.BigDecimal.valueOf(4.9)
                    )
                )
            ),
            GameCategory(
                id = 3,
                name = "汽车游戏",
                games = listOf(
                    ContentItem(
                        id = "game_4",
                        title = "极速赛车",
                        description = "速度与激情的完美结合",
                        imageUrl = "https://example.com/game4.jpg",
                        type = com.example.myapplicationtv.model.ContentType.GAME,
                        rating = java.math.BigDecimal.valueOf(4.7)
                    )
                )
            ),
            GameCategory(
                id = 4,
                name = "射击游戏",
                games = listOf(
                    ContentItem(
                        id = "game_5",
                        title = "战地风云",
                        description = "真实战场，激烈对战",
                        imageUrl = "https://example.com/game5.jpg",
                        type = com.example.myapplicationtv.model.ContentType.GAME,
                        rating = java.math.BigDecimal.valueOf(4.5)
                    )
                )
            ),
            GameCategory(
                id = 5,
                name = "动作游戏",
                games = listOf(
                    ContentItem(
                        id = "game_6",
                        title = "忍者传说",
                        description = "忍者世界，暗影传奇",
                        imageUrl = "https://example.com/game6.jpg",
                        type = com.example.myapplicationtv.model.ContentType.GAME,
                        rating = java.math.BigDecimal.valueOf(4.4)
                    )
                )
            ),
            GameCategory(
                id = 6,
                name = "热门游戏",
                games = listOf(
                    ContentItem(
                        id = "game_7",
                        title = "王者荣耀",
                        description = "5V5王者峡谷，公平对战",
                        imageUrl = "https://example.com/game7.jpg",
                        type = com.example.myapplicationtv.model.ContentType.GAME,
                        rating = java.math.BigDecimal.valueOf(4.8)
                    )
                )
            )
        )
        _gameCategories.value = categories
    }

    private fun loadHotGames() {
        // 模拟热门游戏数据
        val hotGames = listOf(
            ContentItem(
                id = "hot_game_1",
                title = "明日方舟",
                description = "策略塔防，二次元世界",
                imageUrl = "https://example.com/hot_game1.jpg",
                type = com.example.myapplicationtv.model.ContentType.GAME,
                rating = java.math.BigDecimal.valueOf(4.9)
            ),
            ContentItem(
                id = "hot_game_2",
                title = "原神",
                description = "开放世界，冒险之旅",
                imageUrl = "https://example.com/hot_game2.jpg",
                type = com.example.myapplicationtv.model.ContentType.GAME,
                rating = java.math.BigDecimal.valueOf(4.8)
            ),
            ContentItem(
                id = "hot_game_3",
                title = "和平精英",
                description = "大逃杀，战术竞技",
                imageUrl = "https://example.com/hot_game3.jpg",
                type = com.example.myapplicationtv.model.ContentType.GAME,
                rating = java.math.BigDecimal.valueOf(4.7)
            ),
            ContentItem(
                id = "hot_game_4",
                title = "王者荣耀",
                description = "MOBA经典，团队对战",
                imageUrl = "https://example.com/hot_game4.jpg",
                type = com.example.myapplicationtv.model.ContentType.GAME,
                rating = java.math.BigDecimal.valueOf(4.8)
            )
        )
        _hotGames.value = hotGames
    }

    private fun loadFeaturedGames() {
        // 模拟精选游戏数据
        val featuredGames = listOf(
            ContentItem(
                id = "featured_game_1",
                title = "精选游戏1",
                description = "编辑推荐的优质游戏",
                imageUrl = "https://example.com/featured_game1.jpg",
                type = com.example.myapplicationtv.model.ContentType.GAME,
                rating = java.math.BigDecimal.valueOf(4.9)
            )
        )
        _featuredGames.value = featuredGames
    }
}
