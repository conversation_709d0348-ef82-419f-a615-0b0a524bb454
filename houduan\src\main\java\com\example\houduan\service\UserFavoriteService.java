package com.example.houduan.service;

import com.example.houduan.common.PageResult;
import com.example.houduan.dto.MovieResponse;
import com.example.houduan.entity.Movie;
import com.example.houduan.entity.UserFavorite;
import com.example.houduan.mapper.UserFavoriteMapper;
import com.example.houduan.mapper.MovieMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户收藏服务类
 */
@Service
@RequiredArgsConstructor
public class UserFavoriteService {

    private final UserFavoriteMapper userFavoriteMapper;
    private final MovieMapper movieMapper;

    /**
     * 添加收藏
     */
    public void addFavorite(String userId, String movieId) {
        // 检查是否已收藏
        if (isFavorited(userId, movieId)) {
            throw new RuntimeException("已经收藏过该影片");
        }

        // 检查电影是否存在
        Movie movie = movieMapper.findById(movieId).orElse(null);
        if (movie == null) {
            throw new RuntimeException("影片不存在");
        }

        // 添加收藏
        UserFavorite favorite = new UserFavorite();
        favorite.setUserId(userId);
        favorite.setMovieId(movieId);
        favorite.setCreateTime(LocalDateTime.now());
        userFavoriteMapper.save(favorite);

        // 更新电影收藏数
        movie.setFavoriteCount(movie.getFavoriteCount() + 1);
        movieMapper.save(movie);
    }

    /**
     * 取消收藏
     */
    public void removeFavorite(String userId, String movieId) {
        UserFavorite favorite = userFavoriteMapper.findByUserIdAndMovieId(userId, movieId).orElse(null);
        if (favorite == null) {
            throw new RuntimeException("未收藏该影片");
        }

        // 删除收藏
        userFavoriteMapper.deleteById(favorite.getId());

        // 更新电影收藏数
        Movie movie = movieMapper.findById(movieId).orElse(null);
        if (movie != null && movie.getFavoriteCount() > 0) {
            movie.setFavoriteCount(movie.getFavoriteCount() - 1);
            movieMapper.save(movie);
        }
    }

    /**
     * 检查是否已收藏
     */
    public boolean isFavorited(String userId, String movieId) {
        return userFavoriteMapper.findByUserIdAndMovieId(userId, movieId).isPresent();
    }

    /**
     * 获取用户收藏列表
     */
    public PageResult<MovieResponse> getUserFavorites(String userId, int page, int size) {
        // 分页查询用户收藏
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<UserFavorite> favoriteResult = userFavoriteMapper.findByUserIdOrderByCreateTimeDesc(userId, pageable);

        // 获取电影信息
        List<String> movieIds = favoriteResult.getContent().stream()
                .map(UserFavorite::getMovieId)
                .collect(Collectors.toList());

        if (movieIds.isEmpty()) {
            return PageResult.empty(page, size);
        }

        List<Movie> movies = movieMapper.findAllById(movieIds);
        List<MovieResponse> movieResponses = movies.stream()
                .map(movie -> {
                    MovieResponse response = new MovieResponse();
                    BeanUtils.copyProperties(movie, response);
                    response.setIsFavorited(true);
                    return response;
                })
                .collect(Collectors.toList());

        return PageResult.of(movieResponses, favoriteResult.getTotalElements(), page, size);
    }

    /**
     * 获取用户收藏数量
     */
    public long getUserFavoriteCount(String userId) {
        return userFavoriteMapper.countByUserId(userId);
    }
}
