package com.example.houduan.mapper;

import com.example.houduan.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 用户Repository接口
 */
@Repository
public interface UserMapper extends JpaRepository<User, String> {

    Optional<User> findByUsername(String username);
    Optional<User> findByPhone(String phone);
    Optional<User> findByEmail(String email);

    Optional<User> findByUsernameOrPhoneOrEmail(String username, String phone, String email);
}
