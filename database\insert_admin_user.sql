-- 插入管理员用户到 user 表
-- 用户名: admin
-- 密码: admin123 (加密后)

USE movietv;

-- 插入管理员用户
INSERT INTO `user` (
    `id`, 
    `username`, 
    `password`, 
    `phone`, 
    `email`, 
    `nickname`, 
    `avatar`, 
    `member_level`, 
    `member_expire_time`, 
    `status`, 
    `create_time`, 
    `update_time`
) VALUES (
    'admin001', 
    'admin', 
    '$2a$10$DOh0MEYhhbT1gYap9IbGeOUuF3dsUvKQq2HJmmT/b8mhN2nGIcgoC', 
    NULL, 
    '<EMAIL>', 
    '系统管理员', 
    NULL, 
    'ADMIN', 
    NULL, 
    1, 
    NOW(), 
    NOW()
) ON DUPLICATE KEY UPDATE
    `password` = '$2a$10$DOh0MEYhhbT1gYap9IbGeOUuF3dsUvKQq2HJmmT/b8mhN2nGIcgoC',
    `update_time` = NOW();

-- 验证插入结果
SELECT id, username, nickname, member_level, status, create_time FROM `user` WHERE username = 'admin';
