<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res"><file name="app_category_background" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\app_category_background.xml" qualifiers="" type="drawable"/><file name="app_category_dynamic_background" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\app_category_dynamic_background.xml" qualifiers="" type="drawable"/><file name="app_icon_your_company" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\app_icon_your_company.png" qualifiers="" type="drawable"/><file name="button_background" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\button_background.xml" qualifiers="" type="drawable"/><file name="default_background" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\default_background.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="ic_apps" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\ic_apps.xml" qualifiers="" type="drawable"/><file name="ic_car" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\ic_car.xml" qualifiers="" type="drawable"/><file name="ic_fitness" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\ic_fitness.xml" qualifiers="" type="drawable"/><file name="ic_games" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\ic_games.xml" qualifiers="" type="drawable"/><file name="ic_live_tv" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\ic_live_tv.xml" qualifiers="" type="drawable"/><file name="ic_movie" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\ic_movie.xml" qualifiers="" type="drawable"/><file name="ic_music" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\ic_music.xml" qualifiers="" type="drawable"/><file name="ic_person" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_search" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_shopping" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\ic_shopping.xml" qualifiers="" type="drawable"/><file name="ic_tv_shows" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\ic_tv_shows.xml" qualifiers="" type="drawable"/><file name="ic_video_library" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\ic_video_library.xml" qualifiers="" type="drawable"/><file name="movie" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\movie.png" qualifiers="" type="drawable"/><file name="movie_card_background" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\movie_card_background.xml" qualifiers="" type="drawable"/><file name="movie_card_focus_overlay" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\movie_card_focus_overlay.xml" qualifiers="" type="drawable"/><file name="navigation_item_background" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\navigation_item_background.xml" qualifiers="" type="drawable"/><file name="search_button_background" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\search_button_background.xml" qualifiers="" type="drawable"/><file name="activity_details" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_details.xml" qualifiers="" type="layout"/><file name="activity_login" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_search" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_search.xml" qualifiers="" type="layout"/><file name="activity_test" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml" qualifiers="" type="layout"/><file name="activity_user_center" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_user_center.xml" qualifiers="" type="layout"/><file name="fragment_login" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml" qualifiers="" type="layout"/><file name="fragment_main_content" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_main_content.xml" qualifiers="" type="layout"/><file name="item_app_category" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\item_app_category.xml" qualifiers="" type="layout"/><file name="item_featured_movie" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\item_featured_movie.xml" qualifiers="" type="layout"/><file name="item_navigation" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\item_navigation.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\values\colors.xml" qualifiers=""><color name="background_gradient_start">#000000</color><color name="background_gradient_end">#DDDDDD</color><color name="fastlane_background">#0096a6</color><color name="search_opaque">#ffaa3f</color><color name="selected_background">#ffaa3f</color><color name="default_background">#3d3d3d</color><color name="custom_tv_background">#1a1a2e</color><color name="navigation_background">#16213e</color><color name="card_background">#0f3460</color><color name="accent_blue">#0066cc</color><color name="accent_orange">#ff6b35</color><color name="accent_purple">#8b5cf6</color><color name="accent_green">#10b981</color><color name="accent_yellow">#f59e0b</color><color name="accent_cyan">#06b6d4</color></file><file path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">My Application TV</string><string name="browse_title">Videos by Your Company</string><string name="related_movies">Related Videos</string><string name="grid_view">Grid View</string><string name="error_fragment">Error Fragment</string><string name="personal_settings">Personal Settings</string><string name="watch_trailer_1">Watch trailer</string><string name="watch_trailer_2">FREE</string><string name="rent_1">Rent By Day</string><string name="rent_2">From $1.99</string><string name="buy_1">Buy and Own</string><string name="buy_2">AT $9.99</string><string name="movie">Movie</string><string name="error_fragment_message">An error occurred</string><string name="dismiss_error">Dismiss</string></file><file path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.MyApplicationTV" parent="@style/Theme.Leanback"/></file><file name="activity_game" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_game.xml" qualifiers="" type="layout"/><file name="activity_shop" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_shop.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aikaifa\MovieTV\MyApplication2\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aikaifa\MovieTV\MyApplication2\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aikaifa\MovieTV\MyApplication2\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aikaifa\MovieTV\MyApplication2\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>