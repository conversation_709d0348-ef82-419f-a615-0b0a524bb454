<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res"><file name="app_icon_your_company" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\app_icon_your_company.png" qualifiers="" type="drawable"/><file name="button_background" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\button_background.xml" qualifiers="" type="drawable"/><file name="default_background" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\default_background.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="movie" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\drawable\movie.png" qualifiers="" type="drawable"/><file name="activity_details" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_details.xml" qualifiers="" type="layout"/><file name="activity_login" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_search" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_search.xml" qualifiers="" type="layout"/><file name="activity_test" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_test.xml" qualifiers="" type="layout"/><file name="activity_user_center" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\activity_user_center.xml" qualifiers="" type="layout"/><file name="fragment_login" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\layout\fragment_login.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\values\colors.xml" qualifiers=""><color name="background_gradient_start">#000000</color><color name="background_gradient_end">#DDDDDD</color><color name="fastlane_background">#0096a6</color><color name="search_opaque">#ffaa3f</color><color name="selected_background">#ffaa3f</color><color name="default_background">#3d3d3d</color></file><file path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">My Application TV</string><string name="browse_title">Videos by Your Company</string><string name="related_movies">Related Videos</string><string name="grid_view">Grid View</string><string name="error_fragment">Error Fragment</string><string name="personal_settings">Personal Settings</string><string name="watch_trailer_1">Watch trailer</string><string name="watch_trailer_2">FREE</string><string name="rent_1">Rent By Day</string><string name="rent_2">From $1.99</string><string name="buy_1">Buy and Own</string><string name="buy_2">AT $9.99</string><string name="movie">Movie</string><string name="error_fragment_message">An error occurred</string><string name="dismiss_error">Dismiss</string></file><file path="E:\aikaifa\MovieTV\MyApplication2\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.MyApplicationTV" parent="@style/Theme.Leanback"/></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aikaifa\MovieTV\MyApplication2\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aikaifa\MovieTV\MyApplication2\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aikaifa\MovieTV\MyApplication2\app\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\aikaifa\MovieTV\MyApplication2\app\build\generated\res\resValues\release"/></dataSet><mergedItems/></merger>