import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8080/api',
  timeout: 10000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { data } = response
    if (data && data.success) {
      return data
    } else {
      // 对于模拟环境，直接返回响应数据
      return response.data || response
    }
  },
  error => {
    // 只有在真正的401认证错误时才跳转登录页
    if (error.response?.status === 401 && error.response?.data?.message === 'Unauthorized') {
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      window.location.href = '/login'
    } else if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
      // 网络错误，可能是后端服务未启动，不跳转登录页
      console.warn('后端服务未启动，使用模拟数据')
    } else {
      ElMessage.error(error.message || '网络错误')
    }
    return Promise.reject(error)
  }
)

// 认证相关API
export const authAPI = {
  login: async (data) => {
    try {
      // 首先尝试调用真实API
      const response = await api.post('/auth/login', data)
      return response
    } catch (error) {
      console.warn('API调用失败，使用模拟登录:', error.message)
      // 使用模拟登录逻辑
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (data.username === 'admin' && data.password === 'admin123') {
            resolve({
              success: true,
              data: {
                token: 'mock-jwt-token-' + Date.now(),
                user: {
                  id: 1,
                  username: 'admin',
                  name: '管理员',
                  role: 'admin'
                }
              },
              message: '登录成功'
            })
          } else {
            reject(new Error('用户名或密码错误'))
          }
        }, 500)
      })
    }
  },
  register: (data) => api.post('/auth/register', data),
  logout: () => api.post('/auth/logout'),
  getUserInfo: () => api.get('/auth/user')
}

// 电影相关API
export const movieAPI = {
  getList: (params) => api.get('/movies/list', { params }),
  getById: (id) => api.get(`/movies/${id}`),
  create: (data) => api.post('/movies', data),
  update: (id, data) => api.put(`/movies/${id}`, data),
  delete: (id) => api.delete(`/movies/${id}`)
}

// 分类相关API
export const categoryAPI = {
  getList: (params) => api.get('/categories', { params }),
  create: (data) => api.post('/categories', data),
  update: (id, data) => api.put(`/categories/${id}`, data),
  delete: (id) => api.delete(`/categories/${id}`)
}

// 用户相关API
export const userAPI = {
  getList: (params) => api.get('/users', { params }),
  getById: (id) => api.get(`/users/${id}`),
  update: (id, data) => api.put(`/users/${id}`, data),
  delete: (id) => api.delete(`/users/${id}`)
}

// 系统相关API
export const systemAPI = {
  getConfig: () => api.get('/system/config'),
  updateConfig: (data) => api.put('/system/config', data),
  getStats: () => api.get('/system/stats')
}

export default api
