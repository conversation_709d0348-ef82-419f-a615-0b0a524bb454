package com.example.houduan.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 电影响应DTO
 */
@Data
public class MovieResponse {

    private String id;
    private String title;
    private String originalTitle;
    private String cover;
    private String poster;
    private BigDecimal rating;
    private Integer ratingCount;
    private String type;
    private String category;
    private String area;
    private String language;
    private Integer year;
    private Integer duration;
    private String director;
    private String actors;
    private String description;
    private String playUrl;
    private String trailerUrl;
    private Long viewCount;
    private Integer favoriteCount;
    private Integer isRecommended;
    private Integer isHot;
    private Integer isNew;
    private Integer status;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    // 扩展字段
    private Boolean isFavorited; // 当前用户是否收藏
    private BigDecimal userRating; // 当前用户评分
}
