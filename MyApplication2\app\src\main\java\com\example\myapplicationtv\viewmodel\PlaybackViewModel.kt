package com.example.myapplicationtv.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplicationtv.repository.UserRepository
import kotlinx.coroutines.launch

/**
 * 播放ViewModel
 */
class PlaybackViewModel : ViewModel() {
    
    private val userRepository = UserRepository()
    
    // 播放位置
    private val _playbackPosition = MutableLiveData<Long>()
    val playbackPosition: LiveData<Long> = _playbackPosition
    
    // 播放状态
    private val _isPlaying = MutableLiveData<Boolean>()
    val isPlaying: LiveData<Boolean> = _isPlaying
    
    // 错误信息
    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    
    // 当前播放的内容信息
    private var currentContentId: String? = null
    private var currentContentType: String? = null
    
    /**
     * 保存播放进度
     */
    fun savePlaybackPosition(position: Long) {
        _playbackPosition.value = position
        
        // 如果有当前播放内容，保存到服务器
        if (currentContentId != null && currentContentType != null) {
            viewModelScope.launch {
                try {
                    userRepository.addHistory(
                        currentContentId!!,
                        currentContentType!!,
                        (position / 1000).toInt() // 转换为秒
                    )
                } catch (e: Exception) {
                    _error.value = "保存播放进度失败: ${e.message}"
                }
            }
        }
    }
    
    /**
     * 设置播放状态
     */
    fun setPlayingState(isPlaying: Boolean) {
        _isPlaying.value = isPlaying
    }
    
    /**
     * 添加观看历史
     */
    fun addHistory(contentId: String, contentType: String) {
        this.currentContentId = contentId
        this.currentContentType = contentType
        
        viewModelScope.launch {
            try {
                userRepository.addHistory(contentId, contentType).fold(
                    onSuccess = {
                        // 历史记录添加成功
                    },
                    onFailure = { e ->
                        _error.value = "添加观看历史失败: ${e.message}"
                    }
                )
            } catch (e: Exception) {
                _error.value = "添加观看历史失败: ${e.message}"
            }
        }
    }
    
    /**
     * 获取播放进度
     */
    fun getPlaybackPosition(): Long {
        return _playbackPosition.value ?: 0L
    }
    
    /**
     * 重置播放状态
     */
    fun reset() {
        _playbackPosition.value = 0L
        _isPlaying.value = false
        _error.value = ""
        currentContentId = null
        currentContentType = null
    }
}
