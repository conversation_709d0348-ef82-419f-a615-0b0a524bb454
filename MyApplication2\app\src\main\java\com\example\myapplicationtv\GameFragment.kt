package com.example.myapplicationtv

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.adapter.GameCategoryAdapter
import com.example.myapplicationtv.adapter.FeaturedGameAdapter
import com.example.myapplicationtv.adapter.HotGameAdapter
import com.example.myapplicationtv.viewmodel.GameViewModel

/**
 * 游戏页面Fragment
 */
class GameFragment : Fragment() {

    private val TAG = "GameFragment"
    private lateinit var viewModel: GameViewModel

    private lateinit var rvGameCategories: RecyclerView
    private lateinit var rvFeaturedGames: RecyclerView
    private lateinit var rvHotGames: RecyclerView

    private lateinit var categoryAdapter: GameCategoryAdapter
    private lateinit var featuredGameAdapter: FeaturedGameAdapter
    private lateinit var hotGameAdapter: HotGameAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_game, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.i(TAG, "onViewCreated")

        viewModel = ViewModelProvider(this)[GameViewModel::class.java]

        initViews(view)
        setupRecyclerViews()
        observeData()
        loadData()
    }

    private fun initViews(view: View) {
        rvGameCategories = view.findViewById(R.id.rv_game_categories)
        rvFeaturedGames = view.findViewById(R.id.rv_featured_games)
        rvHotGames = view.findViewById(R.id.rv_hot_games)
    }

    private fun setupRecyclerViews() {
        // 设置游戏分类RecyclerView
        rvGameCategories.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        categoryAdapter = GameCategoryAdapter { category ->
            Log.d(TAG, "Category clicked: ${category.name}")
            // 处理分类点击事件
        }
        rvGameCategories.adapter = categoryAdapter

        // 设置精选游戏RecyclerView
        rvFeaturedGames.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        featuredGameAdapter = FeaturedGameAdapter { game ->
            Log.d(TAG, "Featured game clicked: ${game.title}")
            // 处理精选游戏点击事件
        }
        rvFeaturedGames.adapter = featuredGameAdapter

        // 设置热门游戏RecyclerView
        rvHotGames.layoutManager = GridLayoutManager(context, 4)
        hotGameAdapter = HotGameAdapter { game ->
            Log.d(TAG, "Hot game clicked: ${game.title}")
            // 处理热门游戏点击事件
        }
        rvHotGames.adapter = hotGameAdapter
    }

    private fun observeData() {
        // 观察游戏分类数据
        viewModel.gameCategories.observe(viewLifecycleOwner, Observer { categories ->
            categoryAdapter.updateData(categories)
        })

        // 观察精选游戏数据
        viewModel.featuredGames.observe(viewLifecycleOwner, Observer { games ->
            featuredGameAdapter.updateData(games)
        })

        // 观察热门游戏数据
        viewModel.hotGames.observe(viewLifecycleOwner, Observer { games ->
            hotGameAdapter.updateData(games)
        })
    }

    private fun loadData() {
        viewModel.loadGameData()
    }

}
