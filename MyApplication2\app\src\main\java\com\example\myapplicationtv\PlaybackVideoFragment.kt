package com.example.myapplicationtv

import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.leanback.app.VideoSupportFragment
import androidx.leanback.app.VideoSupportFragmentGlueHost
import androidx.leanback.media.MediaPlayerAdapter
import androidx.leanback.media.PlaybackTransportControlGlue
import androidx.leanback.widget.PlaybackControlsRow
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.Observer
import com.example.myapplicationtv.viewmodel.PlaybackViewModel
import com.example.myapplicationtv.utils.UserManager
import com.example.myapplicationtv.model.ContentType

/** Handles video playback with media controls. */
class PlaybackVideoFragment : VideoSupportFragment() {

    private lateinit var mTransportControlGlue: PlaybackTransportControlGlue<MediaPlayerAdapter>
    private lateinit var viewModel: PlaybackViewModel
    private var currentMovie: Movie? = null
    private var currentContentItem: ContentItem? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        initViewModel()
        setupPlayback()
    }

    private fun initViewModel() {
        viewModel = ViewModelProvider(this)[PlaybackViewModel::class.java]

        // 观察播放状态
        viewModel.playbackPosition.observe(this, Observer { position ->
            // 可以在这里保存播放进度
            Log.d(TAG, "Playback position: $position")
        })

        // 观察错误信息
        viewModel.error.observe(this, Observer { error ->
            if (error.isNotEmpty()) {
                Toast.makeText(context, error, Toast.LENGTH_LONG).show()
                Log.e(TAG, "Playback error: $error")
            }
        })
    }

    private fun setupPlayback() {
        // 尝试获取ContentItem
        currentContentItem = activity?.intent?.getSerializableExtra(DetailsActivity.CONTENT_ITEM) as? ContentItem

        // 如果没有ContentItem，尝试获取Movie（向后兼容）
        if (currentContentItem == null) {
            currentMovie = activity?.intent?.getSerializableExtra(DetailsActivity.MOVIE) as? Movie
        }

        val title: String?
        val description: String?
        val videoUrl: String?

        when {
            currentContentItem != null -> {
                title = currentContentItem!!.title
                description = currentContentItem!!.description
                // 对于ContentItem，需要根据类型获取播放URL
                videoUrl = getPlaybackUrl(currentContentItem!!)
            }
            currentMovie != null -> {
                title = currentMovie!!.title
                description = currentMovie!!.description
                videoUrl = currentMovie!!.videoUrl
            }
            else -> {
                Toast.makeText(activity, "无法获取播放内容", Toast.LENGTH_LONG).show()
                activity?.finish()
                return
            }
        }

        if (videoUrl.isNullOrEmpty()) {
            Toast.makeText(activity, "无效的播放地址", Toast.LENGTH_LONG).show()
            activity?.finish()
            return
        }

        val glueHost = VideoSupportFragmentGlueHost(this@PlaybackVideoFragment)
        val playerAdapter = MediaPlayerAdapter(activity)
        playerAdapter.setRepeatAction(PlaybackControlsRow.RepeatAction.INDEX_NONE)

        mTransportControlGlue = PlaybackTransportControlGlue(getActivity(), playerAdapter)
        mTransportControlGlue.host = glueHost
        mTransportControlGlue.title = title
        mTransportControlGlue.subtitle = description
        mTransportControlGlue.playWhenPrepared()

        try {
            playerAdapter.setDataSource(Uri.parse(videoUrl))

            // 记录观看历史
            recordWatchHistory()

        } catch (e: Exception) {
            Log.e(TAG, "Error setting data source", e)
            Toast.makeText(activity, "播放失败: ${e.message}", Toast.LENGTH_LONG).show()
            activity?.finish()
        }
    }

    private fun getPlaybackUrl(contentItem: ContentItem): String? {
        return when (contentItem.type) {
            ContentType.MOVIE -> {
                // 对于电影，从原始数据中获取播放URL
                val movieResponse = contentItem.originalData as? com.example.myapplicationtv.model.MovieResponse
                movieResponse?.playUrl
            }
            ContentType.APP -> {
                // 应用不支持播放
                null
            }
            ContentType.GAME -> {
                // 游戏不支持播放
                null
            }
            ContentType.PRODUCT -> {
                // 商品不支持播放
                null
            }
        }
    }

    private fun recordWatchHistory() {
        if (UserManager.isLoggedIn()) {
            when {
                currentContentItem != null -> {
                    viewModel.addHistory(
                        currentContentItem!!.id,
                        currentContentItem!!.type.value
                    )
                }
                currentMovie != null -> {
                    viewModel.addHistory(
                        currentMovie!!.id.toString(),
                        "MOVIE"
                    )
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        if (::mTransportControlGlue.isInitialized) {
            mTransportControlGlue.pause()

            // 保存播放进度
            val currentPosition = mTransportControlGlue.currentPosition
            viewModel.savePlaybackPosition(currentPosition)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::mTransportControlGlue.isInitialized) {
            // 保存最终播放进度
            val currentPosition = mTransportControlGlue.currentPosition
            viewModel.savePlaybackPosition(currentPosition)
        }
    }

    companion object {
        private const val TAG = "PlaybackVideoFragment"
    }
}