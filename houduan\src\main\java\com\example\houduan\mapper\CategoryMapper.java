package com.example.houduan.mapper;

import com.example.houduan.entity.Category;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 分类Repository接口
 */
@Repository
public interface CategoryMapper extends JpaRepository<Category, String> {

    List<Category> findByTypeAndStatusOrderBySortOrder(String type, Integer status);
    List<Category> findByStatusOrderByTypeAscSortOrderAsc(Integer status);
}
