<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <layer-list>
            <!-- 外层白色发光边框 -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@android:color/white" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
            <!-- 内层彩色背景 -->
            <item android:left="4dp" android:top="4dp" android:right="4dp" android:bottom="4dp">
                <shape android:shape="rectangle">
                    <solid android:color="@color/accent_blue" />
                    <corners android:radius="12dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/accent_blue" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>
