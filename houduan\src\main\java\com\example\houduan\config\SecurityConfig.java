package com.example.houduan.config;

import com.example.houduan.security.JwtAuthenticationEntryPoint;
import com.example.houduan.security.JwtAuthenticationFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * Spring Security配置类
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 认证管理器
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    /**
     * 安全过滤器链
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
                // 禁用CSRF
                .csrf(AbstractHttpConfigurer::disable)
                // 启用CORS
                .cors(cors -> cors.configurationSource(request -> {
                    var corsConfiguration = new org.springframework.web.cors.CorsConfiguration();
                    corsConfiguration.setAllowedOriginPatterns(java.util.List.of("http://localhost:3000", "http://localhost:5173"));
                    corsConfiguration.setAllowedMethods(java.util.List.of("GET", "POST", "PUT", "DELETE", "OPTIONS"));
                    corsConfiguration.setAllowedHeaders(java.util.List.of("*"));
                    corsConfiguration.setAllowCredentials(true);
                    corsConfiguration.setMaxAge(3600L);
                    return corsConfiguration;
                }))
                // 配置异常处理
                .exceptionHandling(exception -> exception
                        .authenticationEntryPoint(jwtAuthenticationEntryPoint))
                // 配置会话管理
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                // 配置请求授权
                .authorizeHttpRequests(auth -> auth
                        // 公开接口
                        .requestMatchers("/auth/**", "/api/auth/**").permitAll()
                        .requestMatchers("/public/**", "/api/public/**").permitAll()
                        .requestMatchers("/files/**", "/api/files/**").permitAll()
                        .requestMatchers("/movies/list", "/api/movies/list").permitAll()
                        .requestMatchers("/movies/{id}", "/api/movies/{id}").permitAll()
                        .requestMatchers("/apps/list", "/api/apps/list").permitAll()
                        .requestMatchers("/apps/{id}", "/api/apps/{id}").permitAll()
                        .requestMatchers("/apps/recommended", "/api/apps/recommended").permitAll()
                        .requestMatchers("/apps/hot", "/api/apps/hot").permitAll()
                        .requestMatchers("/games/**", "/api/games/**").permitAll()
                        .requestMatchers("/products/list", "/api/products/list").permitAll()
                        .requestMatchers("/products/{id}", "/api/products/{id}").permitAll()
                        .requestMatchers("/products/recommended", "/api/products/recommended").permitAll()
                        .requestMatchers("/products/hot", "/api/products/hot").permitAll()
                        .requestMatchers("/categories/**", "/api/categories/**").permitAll()
                        .requestMatchers("/banners/**", "/api/banners/**").permitAll()
                        .requestMatchers("/test/public", "/api/test/public").permitAll()
                        .requestMatchers("/test/health", "/api/test/health").permitAll()
                        // 管理员接口
                        .requestMatchers("/admin/**", "/api/admin/**").hasRole("ADMIN")
                        // 其他接口需要认证
                        .anyRequest().authenticated())
                // 添加JWT过滤器
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
