# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.20"
  }
  digests {
    sha256: "\343\230\266ywb\'\030\277\030\377\231\2679\307\331\332\006\0173\373E\212.% 2!\301j\360\020"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.20"
  }
  digests {
    sha256: "\257\036\304\f;\225\032\375\314\f*\001s\307\270\027c\305(\034-[\257\277\n\205D\242L]\314\f"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.10.1"
  }
  digests {
    sha256: "\241\363\266\034\254\277\275i\265]\311V\356\305\365\243/\345~\316D\234\232S\266\b9\242}/\313#"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.6.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "`\261\v^\365v\233yW\001r\340\025\270\025\224\005\311/\003K\250\213\223\221\251wX\234\235\353N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.10.1"
  }
  digests {
    sha256: "!G\2075\307\235\350\353\213FJk\325\035\206l\2319\027\037\227H\207G\034\235\031~7\375k\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.3.0"
  }
  digests {
    sha256: "\253\375)\310Un[\3202Z\237v\232\271\351\321T\377JU\025\304v\315\325\242\250([\033\031\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.7.0"
  }
  digests {
    sha256: "\232\377\242La`\334\214\255\252\311B-OqNP\tS}\002C\257\304\274t\265\267\317\r\344\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\222\375\017\002\273YO\a\264\017\202p\3533**\341\345\030\313\246\376\307\320b\363\366\004<\231\315\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.0"
  }
  digests {
    sha256: "4\350\262\277\307N#\301R^=\251\003\256D\233\177\033D\n\357E\341\201Y\356G\016\221\231\177H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.leanback"
    artifactId: "leanback"
    version: "1.0.0"
  }
  digests {
    sha256: "\b,H\377|8e\216\255\227\212\325#\bc\020\354\356\212Q(\344U\367Q\250@/\223\216\373\267"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\345\b\306\225H\224\2237M\224+\367\264\356\002\253\367W\035%\252\304\306\"\345}l\325\315)\353s"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.0.0"
  }
  digests {
    sha256: "v\277\373|\357\277x\a\224\330\201p\002\332\321V/>\'\300\251\367F\326$\001\310\355\263\n\356\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\227a\263\250\t\311\260\223\375\006\243\304\273\306Eum\354\016\225\265\311\332A\233\311\362\243\363\002n\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.0.0"
  }
  digests {
    sha256: "\262;R{+\254\207\fJtQ\346\230-q2\344\023\350\215\177\'\333\353\037\307d\nr\f\331\356"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.2"
  }
  digests {
    sha256: "\337{\247?7}\275|\315(\261\022\257\320\215(\266\016\302\306\274\221\354e\206\t\310ED*\316\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.7.2"
  }
  digests {
    sha256: "*\032\277\216\nY\215$k\036\334\267\036#\225\343\23723!\225\353\345\352@\243\335!5[\243\256"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.7.2"
  }
  digests {
    sha256: "\260\264 n\316\222\221\231%\006\037\337W\204\335!\360\021\2054`\236\217m\224\004\275\320\365\313Z="
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.6.2"
  }
  digests {
    sha256: "F\f\2206\266M\247\316z\006j\f\261\204\340\024Q\317\304I)\252\033b\376\006\211Y\177#C\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "+\374TG\\\004q1\2213a\365m\017\177\001\234n[\356S\356\260\353}\224\247\304\231\240R\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.2"
  }
  digests {
    sha256: "\000\\\365\025\020I:$\372H\272\256\032EZRXQS\2215\032qq}\323<\272\225!\031f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.11.0"
  }
  digests {
    sha256: "\\)Nj_\017\201,\357\207k\204\022\225L\030\"\332\030J\363\216\b*[vn<OO\315\225"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.11.0"
  }
  digests {
    sha256: "\031z\034\325\267hU\252\002\2620\3019t\342\223\"\233\220\035\302\271o\253C\025 \036x\272\250\004"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.11.0"
  }
  digests {
    sha256: "\320gu\245\027\033wz\243\333\003\036\260\335J\035\276?\000\335\243[Ut\337\331S\366\260\325\357\030"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.11.0"
  }
  digests {
    sha256: "\322\031\3228\000m\202Ib\027b)\324p\212\274\335\334\3764,j\030\245\320\372H\326\360G\233>"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.0.0"
  }
  digests {
    sha256: "&\303\240\317\n\232\232}#Z\v\000\362\363~C\035R\331\225\'Q\343\353|\220\264\265,#l\361"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.0.0"
  }
  digests {
    sha256: "Pu\221\222e\203\207^:\016lF\'\"5\363#\264WGY\265\300/\n\267\245\035*\3472\r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.0.0"
  }
  digests {
    sha256: "\356H\276\020\252\270\365N\377\364\301Kw\321\036\020\271\356\356Cy\325\357k\362\227\242\222<U\314\021"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.9.0"
  }
  digests {
    sha256: "\346\352\031)\304hR\365\276\306j\2635}\243\203Gl\357N\215\035\356\375\277\031[y\314Me\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.11.0"
  }
  digests {
    sha256: "\356\217k\326\315\022W\001=t\2030\364\312\024v8\251\373\313R\3738\215Z\311<\3654\bt]"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.2.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.2.0"
  }
  digests {
    sha256: "\266B\272\357LW\000U\336L\263\321f{+\026\334\355\220\037\370\006cE\240ci\032\240`%\244"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.9.0"
  }
  digests {
    sha256: "2\252 k\232)\311\337^\332\223\240\222\317\263\260\271\023>#,\006+\252\210/\003\031\360\347\237\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.11.0"
  }
  digests {
    sha256: "\271\237-H\217\316\232\305\256\277ux:xH\370?\247\247\252\301C\202\fN\275I\333\204\231\333\214"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 35
  library_dep_index: 5
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 6
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 12
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
}
library_dependencies {
  library_index: 14
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 32
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 15
  library_dep_index: 6
}
library_dependencies {
  library_index: 16
  library_dep_index: 6
  library_dep_index: 15
}
library_dependencies {
  library_index: 17
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 2
}
library_dependencies {
  library_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 20
  library_dep_index: 1
  library_dep_index: 21
  library_dep_index: 4
  library_dep_index: 2
}
library_dependencies {
  library_index: 21
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 19
}
library_dependencies {
  library_index: 22
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 29
}
library_dependencies {
  library_index: 23
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 24
  library_dep_index: 23
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 29
}
library_dependencies {
  library_index: 25
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 14
  library_dep_index: 23
  library_dep_index: 17
  library_dep_index: 29
}
library_dependencies {
  library_index: 26
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 23
  library_dep_index: 17
  library_dep_index: 29
}
library_dependencies {
  library_index: 27
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 23
  library_dep_index: 17
  library_dep_index: 29
}
library_dependencies {
  library_index: 28
  library_dep_index: 27
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 14
  library_dep_index: 23
  library_dep_index: 17
  library_dep_index: 29
}
library_dependencies {
  library_index: 29
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 23
  library_dep_index: 27
  library_dep_index: 30
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 30
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 31
}
library_dependencies {
  library_index: 31
  library_dep_index: 30
  library_dep_index: 0
  library_dep_index: 30
}
library_dependencies {
  library_index: 32
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 33
  library_dep_index: 12
}
library_dependencies {
  library_index: 33
  library_dep_index: 6
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 6
}
library_dependencies {
  library_index: 35
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 36
  library_dep_index: 8
  library_dep_index: 37
  library_dep_index: 51
  library_dep_index: 52
  library_dep_index: 57
}
library_dependencies {
  library_index: 37
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 38
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 13
  library_dep_index: 48
  library_dep_index: 49
  library_dep_index: 50
}
library_dependencies {
  library_index: 38
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 42
}
library_dependencies {
  library_index: 39
  library_dep_index: 6
}
library_dependencies {
  library_index: 40
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 22
  library_dep_index: 27
}
library_dependencies {
  library_index: 41
  library_dep_index: 6
}
library_dependencies {
  library_index: 42
  library_dep_index: 6
}
library_dependencies {
  library_index: 43
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 44
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 43
}
library_dependencies {
  library_index: 45
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 43
}
library_dependencies {
  library_index: 46
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 43
}
library_dependencies {
  library_index: 47
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 43
}
library_dependencies {
  library_index: 48
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 13
}
library_dependencies {
  library_index: 49
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 50
  library_dep_index: 6
}
library_dependencies {
  library_index: 51
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 35
}
library_dependencies {
  library_index: 52
  library_dep_index: 53
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 5
  library_dep_index: 23
  library_dep_index: 14
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 40
  library_dep_index: 32
  library_dep_index: 30
  library_dep_index: 44
  library_dep_index: 0
  library_dep_index: 55
}
library_dependencies {
  library_index: 53
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 30
  library_dep_index: 34
  library_dep_index: 0
  library_dep_index: 54
}
library_dependencies {
  library_index: 54
  library_dep_index: 53
  library_dep_index: 5
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 0
  library_dep_index: 53
}
library_dependencies {
  library_index: 55
  library_dep_index: 54
  library_dep_index: 56
  library_dep_index: 5
  library_dep_index: 52
  library_dep_index: 24
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 0
  library_dep_index: 52
}
library_dependencies {
  library_index: 56
  library_dep_index: 0
  library_dep_index: 10
}
library_dependencies {
  library_index: 57
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 43
  library_dep_index: 58
}
library_dependencies {
  library_index: 58
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 59
  library_dep_index: 60
  library_dep_index: 61
  library_dep_index: 62
  library_dep_index: 52
  library_dep_index: 63
  library_dep_index: 65
}
library_dependencies {
  library_index: 60
  library_dep_index: 6
}
library_dependencies {
  library_index: 63
  library_dep_index: 64
  library_dep_index: 37
}
library_dependencies {
  library_index: 64
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 65
  library_dep_index: 6
}
library_dependencies {
  library_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 68
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 69
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 70
  library_dep_index: 66
  library_dep_index: 71
}
library_dependencies {
  library_index: 72
  library_dep_index: 67
  library_dep_index: 2
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 36
  dependency_index: 59
  dependency_index: 66
  dependency_index: 70
  dependency_index: 72
  dependency_index: 71
  dependency_index: 18
  dependency_index: 19
  dependency_index: 28
  dependency_index: 25
  dependency_index: 26
  dependency_index: 55
  dependency_index: 57
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
