package com.example.houduan.mapper;

import com.example.houduan.entity.UserFavorite;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 用户收藏Repository接口
 */
@Repository
public interface UserFavoriteMapper extends JpaRepository<UserFavorite, String> {

    Optional<UserFavorite> findByUserIdAndMovieId(String userId, String movieId);
    Page<UserFavorite> findByUserIdOrderByCreateTimeDesc(String userId, Pageable pageable);
    long countByUserId(String userId);
    void deleteByUserIdAndMovieId(String userId, String movieId);
}
