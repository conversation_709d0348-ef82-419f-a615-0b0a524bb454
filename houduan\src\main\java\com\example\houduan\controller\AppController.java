package com.example.houduan.controller;

import com.example.houduan.dto.AppQueryRequest;
import com.example.houduan.dto.AppResponse;
import com.example.houduan.common.PageResult;
import com.example.houduan.common.Result;
import com.example.houduan.entity.App;
import com.example.houduan.service.AppService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应用控制器
 */
@RestController
@RequestMapping("/apps")
@RequiredArgsConstructor
public class AppController {

    private final AppService appService;

    /**
     * 分页查询应用列表
     */
    @GetMapping("/list")
    public Result<PageResult<AppResponse>> getAppList(AppQueryRequest request) {
        try {
            PageResult<AppResponse> result = appService.getAppList(request);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("查询应用列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取应用详情
     */
    @GetMapping("/{id}")
    public Result<AppResponse> getAppById(@PathVariable String id) {
        try {
            AppResponse app = appService.getAppById(id);
            return Result.success(app);
        } catch (Exception e) {
            return Result.error("获取应用详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取推荐应用
     */
    @GetMapping("/recommended")
    public Result<List<AppResponse>> getRecommendedApps(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<AppResponse> apps = appService.getRecommendedApps(limit);
            return Result.success(apps);
        } catch (Exception e) {
            return Result.error("获取推荐应用失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门应用
     */
    @GetMapping("/hot")
    public Result<List<AppResponse>> getHotApps(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<AppResponse> apps = appService.getHotApps(limit);
            return Result.success(apps);
        } catch (Exception e) {
            return Result.error("获取热门应用失败: " + e.getMessage());
        }
    }

    /**
     * 创建应用
     */
    @PostMapping
    public Result<AppResponse> createApp(@RequestBody App app) {
        try {
            AppResponse response = appService.createApp(app);
            return Result.success("创建成功", response);
        } catch (Exception e) {
            return Result.error("创建应用失败: " + e.getMessage());
        }
    }

    /**
     * 更新应用
     */
    @PutMapping("/{id}")
    public Result<AppResponse> updateApp(@PathVariable String id, @RequestBody App app) {
        try {
            AppResponse response = appService.updateApp(id, app);
            return Result.success("更新成功", response);
        } catch (Exception e) {
            return Result.error("更新应用失败: " + e.getMessage());
        }
    }

    /**
     * 删除应用
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteApp(@PathVariable String id) {
        try {
            appService.deleteApp(id);
            return Result.success("删除成功");
        } catch (Exception e) {
            return Result.error("删除应用失败: " + e.getMessage());
        }
    }
}
