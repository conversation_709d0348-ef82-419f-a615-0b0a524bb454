package com.example.myapplicationtv

import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.Observer
import com.example.myapplicationtv.base.BaseTVActivity
import com.example.myapplicationtv.utils.TestHelper
import com.example.myapplicationtv.viewmodel.MainViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 测试Activity - 用于运行各种系统测试
 */
class TestActivity : BaseTVActivity() {

    private lateinit var viewModel: MainViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_test)
        
        initTests()
    }

    private fun initTests() {
        Log.i(TAG, "开始系统集成测试")
        
        // 生成测试报告
        val testReport = TestHelper.generateTestReport(this)
        Log.i(TAG, testReport)
        
        // 检查设备兼容性
        checkDeviceCompatibility()
        
        // 测试API连接
        testApiConnections()
        
        // 测试内存性能
        testMemoryPerformance()
        
        // 测试遥控器功能
        testRemoteControlFunctions()
    }

    private fun checkDeviceCompatibility() {
        Log.i(TAG, "=== 设备兼容性测试 ===")
        
        val isTVDevice = TestHelper.isTVDevice(this)
        val hasTouchScreen = TestHelper.hasTouchScreen(this)
        
        Log.i(TAG, "是否为TV设备: $isTVDevice")
        Log.i(TAG, "是否支持触摸屏: $hasTouchScreen")
        
        if (!isTVDevice) {
            Log.w(TAG, "警告: 当前设备不是标准TV设备")
            Toast.makeText(this, "警告: 当前设备不是标准TV设备", Toast.LENGTH_LONG).show()
        }
        
        if (hasTouchScreen) {
            Log.i(TAG, "设备支持触摸屏，可能影响遥控器体验")
        }
    }

    private fun testApiConnections() {
        Log.i(TAG, "=== API连接测试 ===")
        
        viewModel = ViewModelProvider(this)[MainViewModel::class.java]
        
        // 观察API响应
        viewModel.recommendedMovies.observe(this, Observer { movies ->
            if (movies.isNotEmpty()) {
                Log.i(TAG, "✓ 推荐电影API测试通过，返回${movies.size}条数据")
            } else {
                Log.w(TAG, "✗ 推荐电影API测试失败，返回数据为空")
            }
        })
        
        viewModel.recommendedApps.observe(this, Observer { apps ->
            if (apps.isNotEmpty()) {
                Log.i(TAG, "✓ 推荐应用API测试通过，返回${apps.size}条数据")
            } else {
                Log.w(TAG, "✗ 推荐应用API测试失败，返回数据为空")
            }
        })
        
        viewModel.error.observe(this, Observer { error ->
            if (error.isNotEmpty()) {
                Log.e(TAG, "✗ API错误: $error")
            }
        })
        
        // 开始测试
        val startTime = System.currentTimeMillis()
        viewModel.loadAllContent()
        TestHelper.logPerformance("API", "加载所有内容", startTime)
    }

    private fun testMemoryPerformance() {
        Log.i(TAG, "=== 内存性能测试 ===")
        
        CoroutineScope(Dispatchers.Main).launch {
            // 初始内存状态
            val initialMemory = TestHelper.getMemoryInfo(this@TestActivity)
            Log.i(TAG, "初始内存状态:\n$initialMemory")
            
            // 模拟大量数据加载
            repeat(10) {
                viewModel.refresh()
                kotlinx.coroutines.delay(100)
            }
            
            // 检查内存使用
            kotlinx.coroutines.delay(2000)
            val finalMemory = TestHelper.getMemoryInfo(this@TestActivity)
            Log.i(TAG, "测试后内存状态:\n$finalMemory")
            
            // 建议垃圾回收
            System.gc()
            kotlinx.coroutines.delay(1000)
            
            val afterGCMemory = TestHelper.getMemoryInfo(this@TestActivity)
            Log.i(TAG, "GC后内存状态:\n$afterGCMemory")
        }
    }

    private fun testRemoteControlFunctions() {
        Log.i(TAG, "=== 遥控器功能测试 ===")
        
        val keyTestResults = TestHelper.simulateRemoteKeyTest()
        keyTestResults.forEach { result ->
            Log.i(TAG, "遥控器测试: $result")
        }
        
        Log.i(TAG, "遥控器功能测试完成")
    }

    override fun onResume() {
        super.onResume()
        
        // 测试页面响应时间
        val startTime = System.currentTimeMillis()
        window.decorView.post {
            TestHelper.logPerformance("UI", "页面渲染", startTime)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        
        // 输出最终测试总结
        Log.i(TAG, "=== 测试总结 ===")
        Log.i(TAG, "系统集成测试完成")
        Log.i(TAG, "详细测试结果请查看日志")
        
        Toast.makeText(this, "系统集成测试完成，请查看日志", Toast.LENGTH_LONG).show()
    }

    companion object {
        private const val TAG = "TestActivity"
    }
}
