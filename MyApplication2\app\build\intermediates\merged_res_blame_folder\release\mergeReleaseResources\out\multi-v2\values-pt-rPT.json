{"logs": [{"outputFile": "com.example.myapplicationtv.app-mergeReleaseResources-29:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\850521a42fbfe952cd99f6631de94ce6\\transformed\\core-1.10.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,403,503,610,716,5204", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "197,299,398,498,605,711,832,5300"}}, {"source": "D:\\Android\\GradleRepository\\caches\\8.12\\transforms\\89d10e7acdc9e8617fe8b024a2336641\\transformed\\leanback-1.0.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,634,748,848,951,1075,1195,1295,1411,1572,1697,1818,1921,2018,2147,2242,2345,2448,2549,2642,2752,2874,3000,3122,3235,3352,3466,3585,3693,3806,3893,3980,4085,4223,4379", "endColumns": "106,100,98,95,125,113,99,102,123,119,99,115,160,124,120,102,96,128,94,102,102,100,92,109,121,125,121,112,116,113,118,107,112,86,86,104,137,155,92", "endOffsets": "207,308,407,503,629,743,843,946,1070,1190,1290,1406,1567,1692,1813,1916,2013,2142,2237,2340,2443,2544,2637,2747,2869,2995,3117,3230,3347,3461,3580,3688,3801,3888,3975,4080,4218,4374,4467"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,944,1045,1144,1240,1366,1480,1580,1683,1807,1927,2027,2143,2304,2429,2550,2653,2750,2879,2974,3077,3180,3281,3374,3484,3606,3732,3854,3967,4084,4198,4317,4425,4538,4625,4712,4817,4955,5111", "endColumns": "106,100,98,95,125,113,99,102,123,119,99,115,160,124,120,102,96,128,94,102,102,100,92,109,121,125,121,112,116,113,118,107,112,86,86,104,137,155,92", "endOffsets": "939,1040,1139,1235,1361,1475,1575,1678,1802,1922,2022,2138,2299,2424,2545,2648,2745,2874,2969,3072,3175,3276,3369,3479,3601,3727,3849,3962,4079,4193,4312,4420,4533,4620,4707,4812,4950,5106,5199"}}]}]}