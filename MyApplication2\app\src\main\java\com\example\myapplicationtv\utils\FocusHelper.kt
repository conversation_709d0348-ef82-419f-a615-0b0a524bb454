package com.example.myapplicationtv.utils

import android.view.View
import android.view.ViewGroup
import android.view.KeyEvent
import android.content.Context
import android.media.AudioManager
import android.util.Log

/**
 * 焦点管理辅助类
 */
object FocusHelper {
    
    private const val TAG = "FocusHelper"
    
    /**
     * 设置视图的焦点属性
     */
    fun setupFocusable(view: View, focusable: Boolean = true) {
        view.isFocusable = focusable
        view.isFocusableInTouchMode = focusable
        
        if (focusable) {
            view.setOnFocusChangeListener { v, hasFocus ->
                if (hasFocus) {
                    v.animate()
                        .scaleX(1.1f)
                        .scaleY(1.1f)
                        .setDuration(200)
                        .start()
                } else {
                    v.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(200)
                        .start()
                }
            }
        }
    }
    
    /**
     * 为ViewGroup中的所有子视图设置焦点属性
     */
    fun setupFocusableGroup(viewGroup: ViewGroup, focusable: Boolean = true) {
        for (i in 0 until viewGroup.childCount) {
            val child = viewGroup.getChildAt(i)
            if (child is ViewGroup) {
                setupFocusableGroup(child, focusable)
            } else {
                setupFocusable(child, focusable)
            }
        }
    }
    
    /**
     * 查找下一个可获得焦点的视图
     */
    fun findNextFocusableView(currentView: View, direction: Int): View? {
        val parent = currentView.parent as? ViewGroup ?: return null
        
        return when (direction) {
            View.FOCUS_RIGHT -> findNextHorizontalView(parent, currentView, true)
            View.FOCUS_LEFT -> findNextHorizontalView(parent, currentView, false)
            View.FOCUS_DOWN -> findNextVerticalView(parent, currentView, true)
            View.FOCUS_UP -> findNextVerticalView(parent, currentView, false)
            else -> null
        }
    }
    
    private fun findNextHorizontalView(parent: ViewGroup, currentView: View, toRight: Boolean): View? {
        val children = mutableListOf<View>()
        collectFocusableViews(parent, children)
        
        val currentIndex = children.indexOf(currentView)
        if (currentIndex == -1) return null
        
        return if (toRight) {
            if (currentIndex < children.size - 1) children[currentIndex + 1] else children[0]
        } else {
            if (currentIndex > 0) children[currentIndex - 1] else children[children.size - 1]
        }
    }
    
    private fun findNextVerticalView(parent: ViewGroup, currentView: View, toDown: Boolean): View? {
        // 简化实现，实际应用中可能需要更复杂的逻辑
        return findNextHorizontalView(parent, currentView, toDown)
    }
    
    private fun collectFocusableViews(viewGroup: ViewGroup, result: MutableList<View>) {
        for (i in 0 until viewGroup.childCount) {
            val child = viewGroup.getChildAt(i)
            if (child.isFocusable) {
                result.add(child)
            } else if (child is ViewGroup) {
                collectFocusableViews(child, result)
            }
        }
    }
    
    /**
     * 处理遥控器按键事件
     */
    fun handleKeyEvent(context: Context, keyCode: Int, event: KeyEvent): Boolean {
        if (event.action != KeyEvent.ACTION_DOWN) {
            return false
        }
        
        return when (keyCode) {
            KeyEvent.KEYCODE_DPAD_CENTER,
            KeyEvent.KEYCODE_ENTER -> {
                // 确认键处理
                Log.d(TAG, "Enter/Center key pressed")
                false // 让具体的View处理
            }
            KeyEvent.KEYCODE_BACK -> {
                // 返回键处理
                Log.d(TAG, "Back key pressed")
                false // 让Activity处理
            }
            KeyEvent.KEYCODE_MENU -> {
                // 菜单键处理
                Log.d(TAG, "Menu key pressed")
                true
            }
            KeyEvent.KEYCODE_VOLUME_UP -> {
                // 音量增加
                adjustVolume(context, true)
                true
            }
            KeyEvent.KEYCODE_VOLUME_DOWN -> {
                // 音量减少
                adjustVolume(context, false)
                true
            }
            KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE -> {
                // 播放/暂停
                Log.d(TAG, "Play/Pause key pressed")
                false // 让播放器处理
            }
            KeyEvent.KEYCODE_MEDIA_FAST_FORWARD -> {
                // 快进
                Log.d(TAG, "Fast forward key pressed")
                false
            }
            KeyEvent.KEYCODE_MEDIA_REWIND -> {
                // 快退
                Log.d(TAG, "Rewind key pressed")
                false
            }
            else -> false
        }
    }
    
    private fun adjustVolume(context: Context, increase: Boolean) {
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        val direction = if (increase) AudioManager.ADJUST_RAISE else AudioManager.ADJUST_LOWER
        audioManager.adjustStreamVolume(AudioManager.STREAM_MUSIC, direction, AudioManager.FLAG_SHOW_UI)
    }
    
    /**
     * 设置焦点动画
     */
    fun setFocusAnimation(view: View, focused: Boolean, scaleX: Float = 1.1f, scaleY: Float = 1.1f) {
        if (focused) {
            view.animate()
                .scaleX(scaleX)
                .scaleY(scaleY)
                .setDuration(200)
                .start()
        } else {
            view.animate()
                .scaleX(1.0f)
                .scaleY(1.0f)
                .setDuration(200)
                .start()
        }
    }
    
    /**
     * 请求焦点并滚动到可见区域
     */
    fun requestFocusAndScroll(view: View) {
        view.requestFocus()
        view.parent?.let { parent ->
            if (parent is ViewGroup) {
                parent.requestChildFocus(view, view)
            }
        }
    }
}
