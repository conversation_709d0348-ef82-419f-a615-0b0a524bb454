package com.example.houduan.controller;

import com.example.houduan.dto.GameQueryRequest;
import com.example.houduan.dto.GameResponse;
import com.example.houduan.common.PageResult;
import com.example.houduan.common.Result;
import com.example.houduan.entity.Game;
import com.example.houduan.service.GameService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 游戏控制器
 */
@RestController
@RequestMapping("/games")
@RequiredArgsConstructor
public class GameController {

    private final GameService gameService;

    /**
     * 分页查询游戏列表
     */
    @GetMapping("/list")
    public Result<PageResult<GameResponse>> getGameList(GameQueryRequest request) {
        try {
            PageResult<GameResponse> result = gameService.getGameList(request);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("查询游戏列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取游戏详情
     */
    @GetMapping("/{id}")
    public Result<GameResponse> getGameById(@PathVariable String id) {
        try {
            GameResponse game = gameService.getGameById(id);
            return Result.success(game);
        } catch (Exception e) {
            return Result.error("获取游戏详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取推荐游戏
     */
    @GetMapping("/recommended")
    public Result<List<GameResponse>> getRecommendedGames(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<GameResponse> games = gameService.getRecommendedGames(limit);
            return Result.success(games);
        } catch (Exception e) {
            return Result.error("获取推荐游戏失败: " + e.getMessage());
        }
    }

    /**
     * 获取精选游戏
     */
    @GetMapping("/featured")
    public Result<List<GameResponse>> getFeaturedGames(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<GameResponse> games = gameService.getFeaturedGames(limit);
            return Result.success(games);
        } catch (Exception e) {
            return Result.error("获取精选游戏失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门游戏
     */
    @GetMapping("/hot")
    public Result<List<GameResponse>> getHotGames(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<GameResponse> games = gameService.getHotGames(limit);
            return Result.success(games);
        } catch (Exception e) {
            return Result.error("获取热门游戏失败: " + e.getMessage());
        }
    }

    /**
     * 创建游戏
     */
    @PostMapping
    public Result<GameResponse> createGame(@RequestBody Game game) {
        try {
            GameResponse response = gameService.createGame(game);
            return Result.success("创建成功", response);
        } catch (Exception e) {
            return Result.error("创建游戏失败: " + e.getMessage());
        }
    }

    /**
     * 更新游戏
     */
    @PutMapping("/{id}")
    public Result<GameResponse> updateGame(@PathVariable String id, @RequestBody Game game) {
        try {
            GameResponse response = gameService.updateGame(id, game);
            return Result.success("更新成功", response);
        } catch (Exception e) {
            return Result.error("更新游戏失败: " + e.getMessage());
        }
    }

    /**
     * 删除游戏
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteGame(@PathVariable String id) {
        try {
            gameService.deleteGame(id);
            return Result.success("删除成功");
        } catch (Exception e) {
            return Result.error("删除游戏失败: " + e.getMessage());
        }
    }
}
