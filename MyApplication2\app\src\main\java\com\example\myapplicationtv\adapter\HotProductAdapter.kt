package com.example.myapplicationtv.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.myapplicationtv.ContentItem
import com.example.myapplicationtv.R
import com.example.myapplicationtv.model.ContentType

class HotProductAdapter(
    private val onProductClick: (ContentItem) -> Unit
) : RecyclerView.Adapter<HotProductAdapter.ProductViewHolder>() {

    private var products = listOf<ContentItem>()

    fun updateData(newProducts: List<ContentItem>) {
        products = newProducts
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_hot_product, parent, false)
        return ProductViewHolder(view)
    }

    override fun onBindViewHolder(holder: ProductViewHolder, position: Int) {
        holder.bind(products[position])
    }

    override fun getItemCount(): Int = products.size

    inner class ProductViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivProductImage: ImageView = itemView.findViewById(R.id.iv_product_image)
        private val tvProductName: TextView = itemView.findViewById(R.id.tv_product_name)
        private val tvProductPrice: TextView = itemView.findViewById(R.id.tv_product_price)
        private val tvRating: TextView = itemView.findViewById(R.id.tv_rating)

        fun bind(product: ContentItem) {
            tvProductName.text = product.title
            tvProductPrice.text = "¥${product.price ?: "999"}"
            
            // 设置评分
            val rating = product.rating ?: 4.5f
            val stars = "★".repeat(rating.toInt()) + "☆".repeat(5 - rating.toInt())
            tvRating.text = stars

            // 根据商品类型设置图标
            val iconRes = when (product.type) {
                ContentType.PRODUCT -> when {
                    product.title?.contains("电视") == true -> R.drawable.ic_tv
                    product.title?.contains("手机") == true -> R.drawable.ic_phone
                    product.title?.contains("电脑") == true -> R.drawable.ic_computer
                    product.title?.contains("耳机") == true -> R.drawable.ic_headphone
                    product.title?.contains("音响") == true -> R.drawable.ic_speaker
                    else -> R.drawable.ic_shopping
                }
                else -> R.drawable.ic_shopping
            }
            ivProductImage.setImageResource(iconRes)

            itemView.setOnClickListener {
                onProductClick(product)
            }
        }
    }
}
