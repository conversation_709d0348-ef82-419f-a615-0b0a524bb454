<template>
  <el-container class="layout-container">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
      <div class="logo">
        <h2 v-if="!isCollapse">MovieTV</h2>
        <h2 v-else>MT</h2>
      </div>
      
      <el-menu
        :default-active="$route.path"
        :collapse="isCollapse"
        :unique-opened="true"
        router
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
      >
        <el-menu-item index="/dashboard">
          <el-icon><House /></el-icon>
          <span>仪表盘</span>
        </el-menu-item>

        <el-sub-menu index="/movies">
          <template #title>
            <el-icon><VideoPlay /></el-icon>
            <span>影视管理</span>
          </template>
          <el-menu-item index="/movies/list">影视列表</el-menu-item>
          <el-menu-item index="/movies/add">添加影视</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/apps">
          <template #title>
            <el-icon><Iphone /></el-icon>
            <span>应用管理</span>
          </template>
          <el-menu-item index="/apps/list">应用列表</el-menu-item>
          <el-menu-item index="/apps/add">添加应用</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/games">
          <template #title>
            <el-icon><Trophy /></el-icon>
            <span>游戏管理</span>
          </template>
          <el-menu-item index="/games/list">游戏列表</el-menu-item>
          <el-menu-item index="/games/add">添加游戏</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/products">
          <template #title>
            <el-icon><ShoppingBag /></el-icon>
            <span>商品管理</span>
          </template>
          <el-menu-item index="/products/list">商品列表</el-menu-item>
          <el-menu-item index="/products/add">添加商品</el-menu-item>
        </el-sub-menu>

        <el-menu-item index="/categories/list">
          <el-icon><Folder /></el-icon>
          <span>分类管理</span>
        </el-menu-item>

        <el-sub-menu index="/users">
          <template #title>
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </template>
          <el-menu-item index="/users/list">用户列表</el-menu-item>
          <el-menu-item index="/users/favorites">用户收藏</el-menu-item>
          <el-menu-item index="/users/history">观看历史</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/statistics">
          <template #title>
            <el-icon><DataAnalysis /></el-icon>
            <span>数据统计</span>
          </template>
          <el-menu-item index="/statistics/overview">数据概览</el-menu-item>
          <el-menu-item index="/statistics/content">内容统计</el-menu-item>
          <el-menu-item index="/statistics/user">用户统计</el-menu-item>
        </el-sub-menu>

        <el-menu-item index="/system/config">
          <el-icon><Setting /></el-icon>
          <span>系统设置</span>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button
            text
            @click="toggleCollapse"
            class="collapse-btn"
          >
            <el-icon><Expand v-if="isCollapse" /><Fold v-else /></el-icon>
          </el-button>
          
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.path">
              {{ item.meta.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-icon><User /></el-icon>
              {{ userInfo.name || '管理员' }}
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  House, VideoPlay, Folder, User, Setting,
  Expand, Fold, ArrowDown, Iphone, Trophy,
  ShoppingBag, DataAnalysis
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { logout, getUserInfo } from '@/utils/auth'

const route = useRoute()
const router = useRouter()

const isCollapse = ref(false)
const userInfo = ref(getUserInfo() || { name: '管理员' })

const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.slice(1) // 去掉第一个空的路由
})

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

const handleCommand = (command) => {
  if (command === 'logout') {
    ElMessage.success('退出成功')
    logout()
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  transition: width 0.3s;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3a4b;
  color: white;
  margin-bottom: 10px;
}

.logo h2 {
  margin: 0;
  font-size: 18px;
}

.header {
  background-color: white;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.collapse-btn {
  font-size: 18px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.main-content {
  background-color: #f0f2f5;
  padding: 20px;
}
</style>
