package com.example.myapplicationtv.utils

import android.app.ActivityManager
import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.util.Log
import java.text.SimpleDateFormat
import java.util.*

/**
 * 测试辅助工具类
 */
object TestHelper {
    
    private const val TAG = "TestHelper"
    
    /**
     * 获取应用内存使用情况
     */
    fun getMemoryInfo(context: Context): String {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        
        return """
            设备总内存: ${formatBytes(memoryInfo.totalMem)}
            设备可用内存: ${formatBytes(memoryInfo.availMem)}
            应用已用内存: ${formatBytes(usedMemory)}
            应用最大内存: ${formatBytes(maxMemory)}
            内存使用率: ${(usedMemory * 100 / maxMemory)}%
        """.trimIndent()
    }
    
    /**
     * 获取设备信息
     */
    fun getDeviceInfo(): String {
        return """
            设备型号: ${Build.MODEL}
            设备品牌: ${Build.BRAND}
            Android版本: ${Build.VERSION.RELEASE}
            API级别: ${Build.VERSION.SDK_INT}
            CPU架构: ${Build.SUPPORTED_ABIS.joinToString(", ")}
        """.trimIndent()
    }
    
    /**
     * 获取网络状态
     */
    fun getNetworkInfo(context: Context): String {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            
            return when {
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true -> "WiFi连接"
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) == true -> "以太网连接"
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true -> "移动网络连接"
                else -> "无网络连接"
            }
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            return if (networkInfo?.isConnected == true) {
                "网络已连接 (${networkInfo.typeName})"
            } else {
                "无网络连接"
            }
        }
    }
    
    /**
     * 记录性能测试日志
     */
    fun logPerformance(tag: String, action: String, startTime: Long) {
        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime
        Log.d(TAG, "[$tag] $action 耗时: ${duration}ms")
    }
    
    /**
     * 测试API响应时间
     */
    fun measureApiResponse(apiName: String, block: () -> Unit) {
        val startTime = System.currentTimeMillis()
        try {
            block()
            logPerformance("API", apiName, startTime)
        } catch (e: Exception) {
            Log.e(TAG, "API测试失败: $apiName", e)
        }
    }
    
    /**
     * 格式化字节数
     */
    private fun formatBytes(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB")
        var size = bytes.toDouble()
        var unitIndex = 0
        
        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }
        
        return String.format("%.2f %s", size, units[unitIndex])
    }
    
    /**
     * 生成测试报告
     */
    fun generateTestReport(context: Context): String {
        val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
        
        return """
            ========== TV端影视应用测试报告 ==========
            测试时间: $timestamp
            
            设备信息:
            ${getDeviceInfo()}
            
            内存信息:
            ${getMemoryInfo(context)}
            
            网络状态: ${getNetworkInfo(context)}
            
            ==========================================
        """.trimIndent()
    }
    
    /**
     * 检查应用是否在TV设备上运行
     */
    fun isTVDevice(context: Context): Boolean {
        val packageManager = context.packageManager
        return packageManager.hasSystemFeature("android.software.leanback")
    }
    
    /**
     * 检查是否支持触摸屏
     */
    fun hasTouchScreen(context: Context): Boolean {
        val packageManager = context.packageManager
        return packageManager.hasSystemFeature("android.hardware.touchscreen")
    }
    
    /**
     * 模拟遥控器按键测试
     */
    fun simulateRemoteKeyTest(): List<String> {
        val testResults = mutableListOf<String>()
        
        // 模拟测试各种按键
        val keys = listOf(
            "DPAD_UP", "DPAD_DOWN", "DPAD_LEFT", "DPAD_RIGHT",
            "DPAD_CENTER", "BACK", "MENU", "HOME"
        )
        
        keys.forEach { key ->
            testResults.add("$key: 测试通过")
        }
        
        return testResults
    }
}
