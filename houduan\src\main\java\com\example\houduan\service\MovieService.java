package com.example.houduan.service;

import com.example.houduan.common.PageResult;
import com.example.houduan.dto.MovieQueryRequest;
import com.example.houduan.dto.MovieRequest;
import com.example.houduan.dto.MovieResponse;
import com.example.houduan.entity.Movie;
import com.example.houduan.mapper.MovieMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电影服务类
 */
@Service
@RequiredArgsConstructor
public class MovieService {

    private final MovieMapper movieMapper;

    /**
     * 分页查询电影
     */
    public PageResult<MovieResponse> getMovieList(MovieQueryRequest request) {
        // 构建排序
        String sortBy = StringUtils.hasText(request.getSortBy()) ? request.getSortBy() : "createTime";
        String sortOrder = StringUtils.hasText(request.getSortOrder()) ? request.getSortOrder() : "desc";

        Sort sort = Sort.by("asc".equals(sortOrder) ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy);
        Pageable pageable = PageRequest.of(request.getPage() - 1, request.getSize(), sort);

        // 简化查询，使用基础的分页查询
        Page<Movie> moviePage;

        if (StringUtils.hasText(request.getKeyword())) {
            // 关键词搜索
            moviePage = movieMapper.findByKeywordAndStatus(request.getKeyword(),
                request.getStatus() != null ? request.getStatus() : 1, pageable);
        } else if (StringUtils.hasText(request.getType())) {
            // 类型筛选
            moviePage = movieMapper.findByTypeAndStatus(request.getType(),
                request.getStatus() != null ? request.getStatus() : 1, pageable);
        } else {
            // 默认查询
            moviePage = movieMapper.findByStatus(
                request.getStatus() != null ? request.getStatus() : 1, pageable);
        }

        // 转换为响应DTO
        List<MovieResponse> responseList = moviePage.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        return PageResult.of(responseList, moviePage.getTotalElements(), request.getPage(), request.getSize());
    }

    /**
     * 根据ID获取电影详情
     */
    public MovieResponse getMovieById(String id) {
        Movie movie = movieMapper.findById(id).orElse(null);
        if (movie == null) {
            throw new RuntimeException("电影不存在");
        }

        // 增加播放次数
        movie.setViewCount(movie.getViewCount() + 1);
        movieMapper.save(movie);

        return convertToResponse(movie);
    }

    /**
     * 创建电影
     */
    public MovieResponse createMovie(MovieRequest request) {
        Movie movie = new Movie();
        BeanUtils.copyProperties(request, movie);
        movie.setViewCount(0L);
        movie.setFavoriteCount(0);
        movie.setCreateTime(LocalDateTime.now());
        movie.setUpdateTime(LocalDateTime.now());

        movieMapper.save(movie);
        return convertToResponse(movie);
    }

    /**
     * 更新电影
     */
    public MovieResponse updateMovie(String id, MovieRequest request) {
        Movie movie = movieMapper.findById(id).orElse(null);
        if (movie == null) {
            throw new RuntimeException("电影不存在");
        }

        BeanUtils.copyProperties(request, movie);
        movie.setUpdateTime(LocalDateTime.now());

        movieMapper.save(movie);
        return convertToResponse(movie);
    }

    /**
     * 删除电影
     */
    public void deleteMovie(String id) {
        Movie movie = movieMapper.findById(id).orElse(null);
        if (movie == null) {
            throw new RuntimeException("电影不存在");
        }

        movieMapper.deleteById(id);
    }

    /**
     * 获取推荐电影
     */
    public List<MovieResponse> getRecommendedMovies(int limit) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "rating"));
        Page<Movie> movies = movieMapper.findByIsRecommendedAndStatusOrderByRatingDesc(1, 1, pageable);
        return movies.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 获取热门电影
     */
    public List<MovieResponse> getHotMovies(int limit) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "viewCount"));
        Page<Movie> movies = movieMapper.findByIsHotAndStatusOrderByViewCountDesc(1, 1, pageable);
        return movies.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 获取最新电影
     */
    public List<MovieResponse> getNewMovies(int limit) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createTime"));
        Page<Movie> movies = movieMapper.findByIsNewAndStatusOrderByCreateTimeDesc(1, 1, pageable);
        return movies.getContent().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 转换为响应DTO
     */
    private MovieResponse convertToResponse(Movie movie) {
        MovieResponse response = new MovieResponse();
        BeanUtils.copyProperties(movie, response);
        return response;
    }
}
