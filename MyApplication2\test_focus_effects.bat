@echo off
echo Testing Android TV Focus Effects
echo ================================

echo.
echo 1. Starting application...
adb shell am force-stop com.example.myapplicationtv
adb shell am start -n com.example.myapplicationtv/.MainActivity
timeout /t 3

echo.
echo 2. Taking initial screenshot...
adb shell screencap -p /sdcard/focus_test_initial.png
adb pull /sdcard/focus_test_initial.png .

echo.
echo 3. Testing right navigation (movie cards)...
adb shell input keyevent KEYCODE_DPAD_RIGHT
timeout /t 1
adb shell screencap -p /sdcard/focus_test_right1.png
adb pull /sdcard/focus_test_right1.png .

echo.
echo 4. Testing another right navigation...
adb shell input keyevent KEYCODE_DPAD_RIGHT
timeout /t 1
adb shell screencap -p /sdcard/focus_test_right2.png
adb pull /sdcard/focus_test_right2.png .

echo.
echo 5. Testing down navigation to app categories...
adb shell input keyevent KEYCODE_DPAD_DOWN
adb shell input keyevent KEYCODE_DPAD_DOWN
timeout /t 1
adb shell screencap -p /sdcard/focus_test_down.png
adb pull /sdcard/focus_test_down.png .

echo.
echo 6. Testing right navigation in app categories...
adb shell input keyevent KEYCODE_DPAD_RIGHT
timeout /t 1
adb shell screencap -p /sdcard/focus_test_app_right1.png
adb pull /sdcard/focus_test_app_right1.png .

echo.
echo 7. Testing another right navigation in app categories...
adb shell input keyevent KEYCODE_DPAD_RIGHT
timeout /t 1
adb shell screencap -p /sdcard/focus_test_app_right2.png
adb pull /sdcard/focus_test_app_right2.png .

echo.
echo Focus effects testing completed!
echo Screenshots saved:
echo - focus_test_initial.png
echo - focus_test_right1.png
echo - focus_test_right2.png
echo - focus_test_down.png
echo - focus_test_app_right1.png
echo - focus_test_app_right2.png
echo.
echo Please check the screenshots to verify focus effects are working properly.
